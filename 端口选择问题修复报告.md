# 端口选择问题修复报告

## 问题描述

用户反映：端口号修改后点击保存设置提示保存成功，但重启后显示的不是保存的端口号，而是当前可用端口号列表中的第一个。

## 问题分析

### 根本原因
在初始化电刺激设备配置界面时，端口号下拉框的当前值设置逻辑存在问题：

1. **匹配失败**: `findText()` 方法进行精确匹配，但保存的是 `"COM7"`，下拉框中可能是 `"COM7 (可用)"`
2. **回退机制缺失**: 当精确匹配失败时，没有备选的匹配策略
3. **默认行为**: 匹配失败时下拉框保持第一个选项被选中

### 问题代码
```python
# 原始代码 - 只使用精确匹配
current_port = stimulation_config.get('port_num', 1)
port_text = f"COM{current_port}"
index = self.port_num_combo.findText(port_text)  # 可能找不到
if index >= 0:
    self.port_num_combo.setCurrentIndex(index)
# 如果找不到，下拉框保持默认选择（第一个选项）
```

## 修复方案

### 1. 新增智能端口设置方法
创建 `_set_current_port()` 方法，使用多重匹配策略：

```python
def _set_current_port(self, port_num: int):
    """设置当前选中的端口号"""
    target_port = f"COM{port_num}"
    
    # 方法1: 通过 itemData 查找（最可靠）
    for i in range(self.port_num_combo.count()):
        item_data = self.port_num_combo.itemData(i)
        if item_data == target_port:
            self.port_num_combo.setCurrentIndex(i)
            return
    
    # 方法2: 精确文本匹配
    exact_index = self.port_num_combo.findText(target_port)
    if exact_index >= 0:
        self.port_num_combo.setCurrentIndex(exact_index)
        return
    
    # 方法3: 部分匹配（处理 "COM7 (可用)" 格式）
    for i in range(self.port_num_combo.count()):
        item_text = self.port_num_combo.itemText(i)
        if item_text.startswith(target_port):
            self.port_num_combo.setCurrentIndex(i)
            return
    
    # 备选方案: 设置为第一个选项
    if self.port_num_combo.count() > 0:
        self.port_num_combo.setCurrentIndex(0)
```

### 2. 更新初始化逻辑
```python
# 修复后的代码
current_port = stimulation_config.get('port_num', 1)
self._set_current_port(current_port)  # 使用智能匹配
```

### 3. 更新重置逻辑
```python
# 重置时也使用智能匹配
self._set_current_port(1)  # 重置到COM1
```

## 修复效果

### 匹配策略优先级
1. **itemData 匹配** - 最可靠，直接比较存储的数据值
2. **精确文本匹配** - 处理标准格式端口名
3. **部分文本匹配** - 处理带标识的端口名（如 "COM7 (可用)"）
4. **备选方案** - 确保总是有选项被选中

### 支持的端口格式
- ✅ `"COM7"` - 标准格式
- ✅ `"COM7 (可用)"` - 带可用标识
- ✅ `"COM10"` - 双位数端口
- ✅ `"COM3 (可用)"` - 任意带标识格式

### 测试验证
```
测试场景1: 标准端口列表
设置端口 7: ✅ 通过itemData设置端口: COM7 (索引: 6)

测试场景2: 带可用标识的端口列表  
设置端口 7: ✅ 通过itemData设置端口: COM7 (索引: 1)
当前选择: COM7 (可用) (数据: COM7)

测试场景3: 混合格式
设置端口 7: ✅ 通过itemData设置端口: COM7 (索引: 1)
当前选择: COM7 (可用) (数据: COM7)
```

## 技术细节

### 关键改进点
1. **数据驱动匹配**: 优先使用 `itemData` 而不是显示文本
2. **多重备选**: 三种匹配方式确保成功率
3. **详细日志**: 便于调试和问题追踪
4. **错误处理**: 异常情况下的安全回退

### 兼容性保证
- ✅ 配置文件格式不变
- ✅ 保存逻辑完全兼容
- ✅ 现有功能不受影响
- ✅ 向后兼容所有端口号

## 使用说明

### 用户操作流程
1. 进入"系统设置" -> "设备配置"
2. 在端口号下拉框中选择目标端口
3. 点击"保存设置"
4. 重启系统验证端口号正确显示

### 预期行为
- **保存前**: 下拉框显示用户选择的端口
- **保存后**: 提示保存成功，端口号正确保存到配置
- **重启后**: 下拉框正确显示之前保存的端口号

## 总结

通过实现智能端口匹配算法，彻底解决了端口号重启后显示错误的问题。修复方案具有以下特点：

- **高可靠性**: 多重匹配策略确保成功率
- **强兼容性**: 支持各种端口名格式
- **易维护性**: 清晰的代码结构和详细日志
- **用户友好**: 透明的操作体验

现在用户可以放心地设置端口号，重启后将正确显示保存的配置。
