#!/usr/bin/env python3
"""
测试患者管理界面修复的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

from ui.patient_management_ui import PatientManagementWidget
from core.database_manager import DatabaseManager

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("患者管理界面修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建患者管理界面
        self.patient_widget = PatientManagementWidget()
        layout.addWidget(self.patient_widget)
        
        # 创建数据库管理器（测试用）
        try:
            db_manager = DatabaseManager()
            if db_manager.connect():
                self.patient_widget.set_database_manager(db_manager)
                print("数据库连接成功")
            else:
                print("数据库连接失败")
        except Exception as e:
            print(f"数据库初始化失败: {e}")
        
        # 连接信号测试
        self.patient_widget.switch_to_treatment.connect(self.on_switch_to_treatment)
        
        print("测试窗口初始化完成")
        print("测试项目：")
        print("1. 表单初始状态应该是灰色不可编辑")
        print("2. 患者编号和年龄应该是文本框")
        print("3. 双击患者列表不应该进入编辑状态")
        print("4. 保存患者时不应该出现value()错误")
    
    def on_switch_to_treatment(self, patient_data):
        """测试切换到治疗页面的信号"""
        print(f"收到切换到治疗页面信号，患者: {patient_data.get('name', '')} (编号: {patient_data.get('bianhao', '')})")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
