#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复数据上传功能
Test Duplicate Data Upload Functionality

测试当患者数据重复时的处理逻辑

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader, UploadStatus
from core.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_duplicate_upload():
    """测试重复数据上传"""
    print("🔄 测试重复数据上传处理")
    print("=" * 50)
    
    # 1. 初始化组件
    print("\n1. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 2. 获取医院信息
    hospital_info = db_manager.get_hospital_info()
    
    # 3. 创建测试患者数据（使用可能重复的编号）
    print("\n2. 测试重复数据上传...")
    duplicate_patient = {
        'bianhao': 66,  # 使用用户提到的编号
        'name': '重复测试患者',
        'age': 30,
        'xingbie': '男',
        'cardid': '110101199001011234',
        'zhenduan': '重复测试诊断',
        'bingshi': '重复测试既往史',
        'brhc': '左侧',
        'zhuzhi': '重复测试医生',
        'czy': '重复测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1)
    }
    
    print(f"测试患者编号: {duplicate_patient['bianhao']}")
    
    # 4. 执行上传
    print("\n3. 执行上传...")
    upload_result = uploader.upload_patient_data(duplicate_patient, hospital_info)
    
    print(f"上传结果:")
    print(f"  - 成功: {upload_result.success}")
    print(f"  - 状态: {upload_result.status.value}")
    print(f"  - 消息: {upload_result.message}")
    
    # 5. 测试错误消息处理
    print("\n4. 测试错误消息处理...")
    if not upload_result.success:
        if "重复或已存在" in upload_result.message:
            print("✅ 正确识别为重复数据")
            print("💡 建议消息: 该患者数据在平台中已存在，已保存到本地数据库。")
        else:
            print("⚠️ 其他类型的上传失败")
            print(f"💡 建议消息: 数据上传到平台失败，已保存到本地数据库。")
    else:
        print("✅ 上传成功")
    
    # 6. 测试新的编号
    print("\n5. 测试新编号...")
    import time
    new_patient = duplicate_patient.copy()
    new_patient['bianhao'] = int(time.time()) % 1000000  # 使用时间戳生成唯一编号
    new_patient['name'] = '新测试患者'
    
    print(f"新患者编号: {new_patient['bianhao']}")
    
    new_upload_result = uploader.upload_patient_data(new_patient, hospital_info)
    
    print(f"新患者上传结果:")
    print(f"  - 成功: {new_upload_result.success}")
    print(f"  - 状态: {new_upload_result.status.value}")
    print(f"  - 消息: {new_upload_result.message}")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    # 7. 总结
    print("\n📋 总结:")
    print("- 状态码 -2 表示患者数据重复或已存在")
    print("- 这是正常的业务逻辑，不是错误")
    print("- 系统会正确处理重复数据并给出友好提示")
    print("- 无论上传成功与否，数据都会保存到本地数据库")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_duplicate_upload()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
