#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电刺激设备清理修复
Test Stimulation Device Cleanup Fix

这个脚本用于验证电刺激设备在程序退出时是否正确断开
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device_qt import StimulationDeviceQt, DeviceStatus, ChannelState


def test_stimulation_cleanup():
    """测试电刺激设备清理功能"""
    print("=" * 60)
    print("测试电刺激设备清理功能")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    device = None
    try:
        # 1. 创建设备实例
        print("\n1. 创建电刺激设备实例...")
        device = StimulationDeviceQt()
        print(f"   设备状态: {device.status}")
        
        # 2. 尝试连接设备
        print("\n2. 尝试连接设备...")
        port_num = 7  # 默认端口
        if device.connect(port_num):
            print(f"   ✅ 设备连接成功 (端口: COM{port_num})")
            print(f"   设备状态: {device.status}")
            
            # 3. 测试设置电流
            print("\n3. 测试设置电流...")
            if device.set_current(1, 10.0):  # A通道设置10mA
                print("   ✅ A通道电流设置成功: 10.0mA")
            else:
                print("   ❌ A通道电流设置失败")
                
            # 4. 测试开始刺激
            print("\n4. 测试开始刺激...")
            if device.start_stimulation(1):  # 启动A通道
                print("   ✅ A通道刺激启动成功")
                print("   ⚠️  电刺激正在进行中...")
                
                # 等待一段时间模拟正常使用
                import time
                time.sleep(2)
                
                # 5. 测试清理功能
                print("\n5. 测试程序退出时的清理功能...")
                print("   正在执行disconnect()方法...")
                
                if device.disconnect():
                    print("   ✅ 设备断开成功")
                    print("   ✅ 所有电刺激已停止")
                    print("   ✅ 设备已切换到空闲状态")
                else:
                    print("   ❌ 设备断开失败")
            else:
                print("   ❌ A通道刺激启动失败")
                
        else:
            print(f"   ❌ 设备连接失败 (端口: COM{port_num})")
            print("   这可能是因为:")
            print("   - 设备未连接到指定端口")
            print("   - 设备驱动未安装")
            print("   - 端口被其他程序占用")
            print("   - DLL文件缺失或版本不匹配")
            
            # 即使连接失败，也测试disconnect方法
            print("\n   测试disconnect方法（设备未连接状态）...")
            if device.disconnect():
                print("   ✅ disconnect方法在未连接状态下正常工作")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 确保设备被正确清理
        print("\n6. 最终清理...")
        if device:
            try:
                device.disconnect()
                print("   ✅ 最终清理完成")
            except Exception as e:
                print(f"   ⚠️  最终清理时发生错误: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def test_treatment_ui_cleanup():
    """测试治疗界面清理功能"""
    print("\n" + "=" * 60)
    print("测试治疗界面清理功能")
    print("=" * 60)
    
    try:
        # 模拟治疗界面的清理逻辑
        print("\n1. 模拟治疗界面cleanup方法...")
        
        # 创建模拟的治疗界面状态
        stimulation_device = StimulationDeviceQt()
        stimulation_connected = False
        
        # 模拟连接状态
        if stimulation_device.connect(7):
            stimulation_connected = True
            print("   ✅ 模拟电刺激设备已连接")
            
            # 模拟治疗界面的cleanup逻辑
            print("\n2. 执行治疗界面cleanup逻辑...")
            
            if stimulation_device and stimulation_connected:
                print("   正在断开电刺激设备连接...")
                try:
                    # 先停止所有刺激
                    stimulation_device.stop_all_stimulation()
                    # 然后断开连接
                    stimulation_device.disconnect()
                    stimulation_connected = False
                    print("   ✅ 电刺激设备已安全断开")
                except Exception as e:
                    print(f"   ❌ 断开电刺激设备时发生错误: {e}")
                    # 即使出错也要尝试强制断开
                    try:
                        if stimulation_device:
                            stimulation_device.disconnect()
                    except:
                        pass
        else:
            print("   ⚠️  设备连接失败，跳过连接测试")
            
    except Exception as e:
        print(f"\n❌ 治疗界面清理测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("电刺激设备清理修复测试")
    print("这个测试验证程序退出时电刺激设备是否正确断开")
    
    # 测试设备清理
    test_stimulation_cleanup()
    
    # 测试治疗界面清理
    test_treatment_ui_cleanup()
    
    print("\n🔧 修复总结:")
    print("1. ✅ 在治疗界面cleanup方法中添加了电刺激设备断开逻辑")
    print("2. ✅ 在主窗口closeEvent中添加了电刺激设备状态检查")
    print("3. ✅ 确保程序退出时电刺激设备会被安全断开")
    print("4. ✅ 添加了异常处理确保即使出错也会尝试断开设备")
    
    input("\n按回车键退出...")
