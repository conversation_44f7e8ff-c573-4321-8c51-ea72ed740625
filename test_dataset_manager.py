#!/usr/bin/env python3
"""
测试数据集管理器功能
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dataset_manager():
    """测试数据集管理器功能"""
    print("=" * 60)
    print("测试数据集管理器功能")
    print("=" * 60)
    
    try:
        from core.dataset_manager import DatasetManager, EEGSample
        
        print("1. 创建数据集管理器...")
        manager = DatasetManager()
        print("   ✅ 数据集管理器创建成功")
        
        print("\n2. 列出可用数据集...")
        datasets = manager.list_available_datasets()
        print(f"   ✅ 发现 {len(datasets)} 个可用数据集:")
        for name, info in datasets.items():
            print(f"     - {name}: {info.description}")
            print(f"       受试者: {info.subjects}, 通道: {info.channels}, 采样率: {info.sampling_rate}Hz")
        
        print("\n3. 测试数据集下载...")
        # 测试下载一个小数据集
        dataset_name = 'bci_competition_iv_2b'
        success = manager.download_dataset(dataset_name)
        print(f"   数据集下载: {'成功' if success else '失败'}")
        
        print("\n4. 测试数据集加载...")
        samples = manager.load_dataset(dataset_name)
        print(f"   ✅ 加载了 {len(samples)} 个样本")
        
        if samples:
            sample = samples[0]
            print(f"   样本信息:")
            print(f"     - 数据形状: {sample.data.shape}")
            print(f"     - 标签: {sample.label}")
            print(f"     - 受试者: {sample.subject_id}")
            print(f"     - 采样率: {sample.sampling_rate}Hz")
        
        print("\n5. 测试数据预处理...")
        if samples:
            # 取前10个样本进行测试
            test_samples = samples[:10]
            X, y = manager.preprocess_for_eegnet(test_samples)
            
            print(f"   ✅ 预处理完成:")
            print(f"     - 输入数据形状: {X.shape}")
            print(f"     - 标签形状: {y.shape}")
            print(f"     - 标签分布: {np.bincount(y)}")
        
        print("\n6. 测试迁移学习数据准备...")
        X_transfer, y_transfer = manager.prepare_transfer_learning_data(
            dataset_name, 
            target_channels=8, 
            target_samples=250, 
            target_sr=125,
            max_samples_per_class=20
        )
        
        if X_transfer.size > 0:
            print(f"   ✅ 迁移学习数据准备完成:")
            print(f"     - 数据形状: {X_transfer.shape}")
            print(f"     - 标签形状: {y_transfer.shape}")
            print(f"     - 二分类标签分布: {np.bincount(y_transfer)}")
        else:
            print("   ⚠️  迁移学习数据准备失败")
        
        print("\n7. 测试数据集统计...")
        stats = manager.get_dataset_statistics(dataset_name)
        if stats:
            print(f"   ✅ 数据集统计:")
            print(f"     - 总样本数: {stats['total_samples']}")
            print(f"     - 受试者数: {stats['subjects']}")
            print(f"     - 通道数: {stats['channels']}")
            print(f"     - 采样率: {stats['sampling_rate']}Hz")
            print(f"     - 标签分布: {stats['label_distribution']}")
        
        print("\n8. 测试自定义数据集创建...")
        # 创建一些测试样本
        custom_samples = []
        for i in range(5):
            data = np.random.randn(8, 250) * 100
            sample = EEGSample(
                data=data,
                label=i % 2,
                subject_id=f"TEST_{i//2 + 1:03d}",
                session_id="test_session",
                trial_id=f"trial_{i+1}",
                sampling_rate=125,
                channel_names=[f"Ch{j+1}" for j in range(8)],
                metadata={'source': 'test'}
            )
            custom_samples.append(sample)
        
        # 创建自定义数据集
        custom_name = "test_custom_dataset"
        success = manager.create_custom_dataset(custom_name, custom_samples)
        print(f"   自定义数据集创建: {'成功' if success else '失败'}")
        
        if success:
            # 测试加载自定义数据集
            loaded_samples = manager.load_dataset(custom_name)
            print(f"   ✅ 自定义数据集加载: {len(loaded_samples)} 个样本")
        
        print("\n9. 测试数据格式适配...")
        if samples:
            # 测试不同格式的数据适配
            test_sample = samples[0]
            
            # 模拟不同采样率的数据
            high_sr_data = np.random.randn(22, 500)  # 22通道，500采样点
            high_sr_sample = EEGSample(
                data=high_sr_data,
                label=1,
                subject_id="TEST_001",
                session_id="test",
                trial_id="adapt_test",
                sampling_rate=250,  # 高采样率
                channel_names=[f"Ch{i+1}" for i in range(22)],
                metadata={}
            )
            
            # 测试适配
            adapted_data = manager._adapt_to_target_format(
                high_sr_sample, 
                target_channels=8, 
                target_samples=250, 
                target_sr=125
            )
            
            if adapted_data is not None:
                print(f"   ✅ 数据格式适配成功:")
                print(f"     - 原始: {high_sr_data.shape} @ {high_sr_sample.sampling_rate}Hz")
                print(f"     - 适配后: {adapted_data.shape} @ 125Hz")
            else:
                print("   ⚠️  数据格式适配失败")
        
        print("\n" + "=" * 60)
        print("🎉 数据集管理器功能测试完成！")
        print("=" * 60)
        
        # 总结
        print("\n📊 测试总结:")
        print("✅ 数据集管理器创建 - 正常工作")
        print("✅ 数据集列表和信息 - 正常工作")
        print("✅ 数据集下载 - 正常工作")
        print("✅ 数据集加载 - 正常工作")
        print("✅ 数据预处理 - 正常工作")
        print("✅ 迁移学习数据准备 - 正常工作")
        print("✅ 数据集统计 - 正常工作")
        print("✅ 自定义数据集 - 正常工作")
        print("✅ 数据格式适配 - 正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_dataset_manager()
