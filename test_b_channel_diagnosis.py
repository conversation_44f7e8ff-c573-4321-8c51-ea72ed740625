#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
B通道问题诊断测试
专门分析B通道没有输出的原因
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('b_channel_diagnosis.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_b_channel_diagnosis():
    """B通道问题诊断"""
    print("=" * 60)
    print("🔍 B通道问题诊断测试")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        # 步骤1：连接设备
        print("\n📡 步骤1: 连接设备")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")
        
        # 显示初始状态
        print("\n📊 初始设备状态:")
        status_info = device.get_status_info()
        print(f"   设备状态: {status_info['device_status']}")
        print(f"   A通道状态: {status_info['channel_a_status']}")
        print(f"   B通道状态: {status_info['channel_b_status']}")
        
        # 步骤2：设置A通道参数
        print("\n⚙️ 步骤2: 设置A通道参数")
        params_a = StimulationParameters(
            channel_num=1,  # A通道
            frequency=25.0,
            pulse_width=250.0,
            relax_time=5.0,
            climb_time=2.0,
            work_time=10.0,
            fall_time=2.0,
            wave_type=0
        )
        
        if device.set_stimulation_parameters(params_a):
            print("✅ A通道参数设置成功")
        else:
            print("❌ A通道参数设置失败")
            
        # 步骤3：设置B通道参数
        print("\n⚙️ 步骤3: 设置B通道参数")
        params_b = StimulationParameters(
            channel_num=2,  # B通道
            frequency=25.0,
            pulse_width=250.0,
            relax_time=5.0,
            climb_time=2.0,
            work_time=10.0,
            fall_time=2.0,
            wave_type=0
        )
        
        if device.set_stimulation_parameters(params_b):
            print("✅ B通道参数设置成功")
        else:
            print("❌ B通道参数设置失败")
            
        # 步骤4：设置A通道电流
        print("\n🔋 步骤4: 设置A通道电流")
        if device.set_current(1, 2.0):
            print("✅ A通道电流设置成功: 2.0mA")
        else:
            print("❌ A通道电流设置失败")
            
        # 步骤5：设置B通道电流
        print("\n🔋 步骤5: 设置B通道电流")
        if device.set_current(2, 1.0):
            print("✅ B通道电流设置成功: 1.0mA")
        else:
            print("❌ B通道电流设置失败")
            
        # 步骤6：启动A通道刺激
        print("\n🚀 步骤6: 启动A通道刺激")
        if device.start_stimulation(1):
            print("✅ A通道刺激启动成功")
        else:
            print("❌ A通道刺激启动失败")
            
        # 等待并观察状态
        print("\n⏱️ 观察A通道刺激状态 (5秒)")
        for i in range(5):
            time.sleep(1)
            status_info = device.get_status_info()
            print(f"   [{i+1}s] A通道: {status_info['channel_a_status']}, B通道: {status_info['channel_b_status']}")
            
        # 步骤7：启动B通道刺激
        print("\n🚀 步骤7: 启动B通道刺激")
        if device.start_stimulation(2):
            print("✅ B通道刺激启动成功")
        else:
            print("❌ B通道刺激启动失败")
            
        # 等待并观察状态
        print("\n⏱️ 观察双通道刺激状态 (10秒)")
        for i in range(10):
            time.sleep(1)
            status_info = device.get_status_info()
            a_status = status_info['channel_a_status']
            b_status = status_info['channel_b_status']
            print(f"   [{i+1:2d}s] A通道: {a_status}, B通道: {b_status}")
            
            # 分析状态
            if a_status == 3 and b_status != 3:
                print(f"      ⚠️ 问题发现: A通道正常工作(3)，B通道状态异常({b_status})")
            elif a_status != 3 and b_status == 3:
                print(f"      ⚠️ 问题发现: B通道正常工作(3)，A通道状态异常({a_status})")
            elif a_status == 3 and b_status == 3:
                print(f"      ✅ 双通道都在正常工作")
                
        # 步骤8：停止所有刺激
        print("\n🛑 步骤8: 停止所有刺激")
        if device.stop_all_stimulation():
            print("✅ 所有刺激已停止")
        else:
            print("❌ 停止刺激失败")
            
        # 最终状态检查
        print("\n📊 最终设备状态:")
        status_info = device.get_status_info()
        print(f"   设备状态: {status_info['device_status']}")
        print(f"   A通道状态: {status_info['channel_a_status']}")
        print(f"   B通道状态: {status_info['channel_b_status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 确保断开连接
        try:
            device.disconnect()
            print("\n🔌 设备连接已断开")
        except:
            pass

def test_parameter_channel_issue():
    """测试参数设置中的通道问题"""
    print("\n" + "=" * 60)
    print("🔍 参数设置通道问题诊断")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
            
        print("✅ 设备连接成功")
        
        # 测试问题：参数设置时channel_num字段的影响
        print("\n🔍 测试1: 检查参数设置中的channel_num字段")
        
        # 创建参数但不指定channel_num
        params_default = StimulationParameters()
        print(f"   默认参数channel_num: {params_default.channel_num}")
        
        # 设置参数（使用默认channel_num=1）
        print("   设置参数（默认channel_num=1）")
        if device.set_stimulation_parameters(params_default):
            print("   ✅ 参数设置成功")
        else:
            print("   ❌ 参数设置失败")
            
        # 现在尝试启动B通道
        print("\n🔍 测试2: 使用默认参数启动B通道")
        device.set_current(2, 1.0)
        if device.start_stimulation(2):
            print("   ✅ B通道启动成功")
        else:
            print("   ❌ B通道启动失败")
            
        time.sleep(3)
        status_info = device.get_status_info()
        print(f"   B通道状态: {status_info['channel_b_status']}")
        
        device.stop_stimulation(2)
        
        # 测试：明确为B通道设置参数
        print("\n🔍 测试3: 明确为B通道设置参数")
        params_b = StimulationParameters(channel_num=2)
        if device.set_stimulation_parameters(params_b):
            print("   ✅ B通道参数设置成功")
        else:
            print("   ❌ B通道参数设置失败")
            
        # 再次尝试启动B通道
        device.set_current(2, 1.0)
        if device.start_stimulation(2):
            print("   ✅ B通道启动成功")
        else:
            print("   ❌ B通道启动失败")
            
        time.sleep(3)
        status_info = device.get_status_info()
        print(f"   B通道状态: {status_info['channel_b_status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        try:
            device.disconnect()
        except:
            pass

if __name__ == "__main__":
    setup_logging()
    
    print("🔍 开始B通道问题诊断...")
    
    # 基础诊断测试
    success1 = test_b_channel_diagnosis()
    
    # 参数通道问题测试
    success2 = test_parameter_channel_issue()
    
    print("\n" + "=" * 60)
    print("📋 诊断结果总结")
    print("=" * 60)
    print(f"基础诊断测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"参数通道测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过，B通道功能正常")
    else:
        print("\n⚠️ 发现问题，请查看详细日志")
        print("   日志文件: b_channel_diagnosis.log")
