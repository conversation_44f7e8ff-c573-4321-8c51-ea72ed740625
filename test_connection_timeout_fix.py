#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备连接超时问题修复测试
测试连接错误端口超时后，切换到正确端口是否能正常连接
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('connection_timeout_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_connection_timeout_fix():
    """测试连接超时修复"""
    print("🔧 电刺激设备连接超时问题修复测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.stimulation_device import StimulationDevice
        from utils.app_config import AppConfig
        
        # 创建设备实例
        device = StimulationDevice()
        
        # 测试结果记录
        test_results = []
        
        print("\n📍 测试1: 连接错误端口（应该超时）")
        wrong_port = 99  # 不存在的端口
        print(f"   尝试连接端口: {wrong_port}")
        
        start_time = time.time()
        result1 = device.connect(wrong_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result1 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if not result1:  # 应该连接失败（可能是立即失败或超时）
            print("   ✅ 错误端口连接正确失败")
            test_results.append(('错误端口连接失败', True))
        else:
            print("   ❌ 错误端口连接异常成功")
            test_results.append(('错误端口连接失败', False))
        
        # 等待一下，确保清理完成
        print("\n⏳ 等待资源清理完成...")
        time.sleep(2)
        
        print("\n📍 测试2: 连接正确端口（应该成功）")
        correct_port = 7  # 正确的端口
        print(f"   尝试连接端口: {correct_port}")
        
        start_time = time.time()
        result2 = device.connect(correct_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result2 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if result2:
            print("   ✅ 正确端口连接成功")
            test_results.append(('正确端口连接', True))
            
            # 断开连接
            print("   断开连接...")
            device.disconnect()
        else:
            print("   ❌ 正确端口连接失败")
            test_results.append(('正确端口连接', False))
        
        print("\n📍 测试3: 再次连接错误端口（验证清理效果）")
        print(f"   尝试连接端口: {wrong_port}")
        
        start_time = time.time()
        result3 = device.connect(wrong_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result3 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if not result3:
            print("   ✅ 再次错误端口连接正确失败")
            test_results.append(('再次错误端口连接失败', True))
        else:
            print("   ❌ 再次错误端口连接异常成功")
            test_results.append(('再次错误端口连接失败', False))
        
        # 等待清理
        time.sleep(2)
        
        print("\n📍 测试4: 再次连接正确端口（最终验证）")
        print(f"   尝试连接端口: {correct_port}")
        
        start_time = time.time()
        result4 = device.connect(correct_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result4 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if result4:
            print("   ✅ 最终正确端口连接成功")
            test_results.append(('最终正确端口连接', True))
            device.disconnect()
        else:
            print("   ❌ 最终正确端口连接失败")
            test_results.append(('最终正确端口连接', False))
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, passed in test_results:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"   {test_name}: {status}")
            if passed:
                passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate == 100:
            print("🎉 所有测试通过！连接超时问题已修复")
        elif success_rate >= 75:
            print("⚠️  大部分测试通过，但仍有问题需要解决")
        else:
            print("❌ 测试失败，连接超时问题未解决")
        
        return success_rate == 100
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_connection_timeout_fix()
    sys.exit(0 if success else 1)
