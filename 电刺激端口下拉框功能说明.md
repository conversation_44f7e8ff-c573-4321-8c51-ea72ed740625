# 电刺激设备端口下拉框功能说明

## 概述

本次更新将电刺激设备配置中的端口号调节框（QSpinBox）改为下拉框（QComboBox），并实现了自动检测可用串口的功能。

## 主要改进

### 1. UI组件变更
- **原来**: 使用 `QSpinBox` 手动输入端口号（1-10）
- **现在**: 使用 `QComboBox` 从下拉列表选择端口

### 2. 自动串口检测
- 使用 `serial.tools.list_ports` 自动检测系统可用串口
- 优先显示系统检测到的串口，并标记为"(可用)"
- 同时提供常用端口选项（COM1-COM20）

### 3. 智能端口列表
- **系统检测端口**: 显示为 "COM7 (可用)"
- **常用端口**: 显示为 "COM1", "COM2" 等
- 自动去重，优先显示可用端口

## 技术实现

### 核心函数

#### `_populate_stimulation_ports()`
```python
def _populate_stimulation_ports(self):
    """填充电刺激设备可用串口"""
    # 1. 检测系统可用串口
    # 2. 生成常用端口列表
    # 3. 合并并去重
    # 4. 添加到下拉框
```

#### 端口号提取逻辑
```python
def extract_port_num(port_text):
    """从端口文本提取端口号"""
    if port_text and port_text.startswith('COM'):
        # 处理 "COM7 (可用)" 格式
        if ' (' in port_text:
            port_text = port_text.split(' (')[0]
        return int(port_text[3:])
    return 1  # 默认值
```

### 修改的文件

#### `ui/settings_ui.py`
1. **UI组件变更**:
   - 将 `self.port_num_spin` 改为 `self.port_num_combo`
   - 添加 `_populate_stimulation_ports()` 方法

2. **逻辑更新**:
   - `save_settings()`: 更新端口号保存逻辑
   - `reset_settings()`: 更新重置逻辑
   - `test_stimulation_connection()`: 更新测试连接逻辑
   - `download_stimulation_parameters()`: 更新下传参数逻辑

## 功能特性

### 1. 自动检测
- 启动时自动检测系统可用串口
- 实时显示端口可用状态

### 2. 用户友好
- 可用端口明确标识
- 支持常用端口快速选择
- 保持向后兼容性

### 3. 错误处理
- 处理 pyserial 未安装的情况
- 处理端口检测失败的情况
- 提供默认端口选项

### 4. 数据完整性
- 正确提取端口号数字
- 处理带标识的端口名
- 保持配置文件兼容性

## 测试验证

### 测试脚本
- `test_port_logic.py`: 验证端口号提取逻辑
- `test_port_dropdown.py`: GUI功能测试（需要GUI环境）

### 测试结果
```
✅ 端口号提取逻辑: 100% 通过
✅ 串口检测功能: 正常工作
✅ 配置集成: 完全兼容
```

## 使用说明

### 1. 界面操作
1. 进入"系统设置" -> "设备配置"
2. 在"电刺激设备配置"部分找到"端口号"下拉框
3. 选择合适的端口（优先选择标记为"可用"的端口）
4. 点击"测试连接"验证设备连接
5. 点击"保存设置"保存配置

### 2. 端口选择建议
- **优先选择**: 标记为"(可用)"的端口
- **常用端口**: COM1, COM7, COM8 等
- **测试方法**: 使用"测试连接"功能验证

### 3. 故障排除
| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 下拉框为空 | pyserial未安装 | 安装pyserial: `pip install pyserial` |
| 无可用端口 | 设备未连接 | 检查设备连接和驱动 |
| 连接失败 | 端口被占用 | 选择其他端口或关闭占用程序 |

## 兼容性

### 向后兼容
- 配置文件格式不变
- 端口号数值保持一致
- 现有功能完全保留

### 系统要求
- Python 3.8+
- PySide6
- pyserial (可选，用于自动检测)

## 总结

本次更新显著提升了电刺激设备端口配置的用户体验：

1. **更直观**: 下拉框比数字输入更直观
2. **更智能**: 自动检测可用端口
3. **更可靠**: 减少端口配置错误
4. **更友好**: 清晰标识端口状态

用户现在可以更轻松地配置电刺激设备端口，减少连接问题，提高系统可用性。
