#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试信号质量评估组件
Debug Signal Quality Assessment Components

作者: AI Assistant
版本: 1.0.0
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_quality_components():
    """调试质量评估各个组件"""
    print("🔍 调试信号质量评估组件")
    print("=" * 60)
    
    try:
        from core.signal_processor import EEGSignalProcessor
        
        processor = EEGSignalProcessor()
        
        # 测试两种极端情况
        channels = 8
        samples = 500
        
        # 1. 极小信号（脑电帽未佩戴）
        print("\n📊 测试1: 极小信号（脑电帽未佩戴）")
        data1 = np.random.randn(channels, samples) * 2.0
        
        # 分别测试各个组件
        for ch in range(1):  # 只测试第一个通道
            channel_data = data1[ch]
            
            # 幅值评估
            amplitude_score = processor._assess_signal_amplitude(channel_data)
            print(f"   幅值评估: {amplitude_score:.3f} (标准差: {np.std(channel_data):.1f})")
            
            # 稳定性评估
            stability_score = processor._assess_signal_stability(channel_data)
            print(f"   稳定性评估: {stability_score:.3f}")
            
            # 连接状态评估
            connection_score = processor._assess_connection_status(channel_data)
            print(f"   连接状态评估: {connection_score:.3f} (方差: {np.var(channel_data):.1f})")
            
            # 频域特征评估
            freq_score = processor._assess_frequency_features(channel_data)
            print(f"   频域特征评估: {freq_score:.3f}")
            
            # 综合评分
            channel_score = (0.4 * amplitude_score + 
                           0.3 * freq_score + 
                           0.2 * stability_score +
                           0.1 * connection_score)
            print(f"   综合评分: {channel_score:.3f}")
        
        # 2. 正常脑电信号
        print("\n📊 测试2: 正常脑电信号")
        data2 = np.random.randn(channels, samples) * 50.0
        t = np.linspace(0, 4, samples)
        for ch in range(channels):
            alpha = 100 * np.sin(2 * np.pi * 10 * t)
            beta = 50 * np.sin(2 * np.pi * 20 * t)
            data2[ch] += alpha + beta
        
        # 分别测试各个组件
        for ch in range(1):  # 只测试第一个通道
            channel_data = data2[ch]
            
            # 幅值评估
            amplitude_score = processor._assess_signal_amplitude(channel_data)
            print(f"   幅值评估: {amplitude_score:.3f} (标准差: {np.std(channel_data):.1f})")
            
            # 稳定性评估
            stability_score = processor._assess_signal_stability(channel_data)
            print(f"   稳定性评估: {stability_score:.3f}")
            
            # 连接状态评估
            connection_score = processor._assess_connection_status(channel_data)
            print(f"   连接状态评估: {connection_score:.3f} (方差: {np.var(channel_data):.1f})")
            
            # 频域特征评估
            freq_score = processor._assess_frequency_features(channel_data)
            print(f"   频域特征评估: {freq_score:.3f}")
            
            # 综合评分
            channel_score = (0.4 * amplitude_score + 
                           0.3 * freq_score + 
                           0.2 * stability_score +
                           0.1 * connection_score)
            print(f"   综合评分: {channel_score:.3f}")
        
        # 3. 分析频域特征评估的详细信息
        print("\n🔍 详细分析频域特征评估:")
        
        def analyze_frequency_features(data, name):
            from scipy.signal import welch
            
            print(f"\n   {name}:")
            freqs, psd = welch(data, 125, nperseg=min(256, len(data)//4))
            
            # 谱熵计算
            psd_normalized = psd / np.sum(psd)
            spectral_entropy = -np.sum(psd_normalized * np.log2(psd_normalized + 1e-10))
            max_entropy = np.log2(len(psd))
            normalized_entropy = spectral_entropy / max_entropy
            structure_score = 1.0 - normalized_entropy
            
            print(f"      谱熵: {spectral_entropy:.3f}")
            print(f"      最大熵: {max_entropy:.3f}")
            print(f"      归一化熵: {normalized_entropy:.3f}")
            print(f"      结构分数: {structure_score:.3f}")
            
            # 脑电频带功率
            alpha_mask = (freqs >= 8) & (freqs <= 13)
            beta_mask = (freqs >= 13) & (freqs <= 30)
            
            alpha_power = np.sum(psd[alpha_mask])
            beta_power = np.sum(psd[beta_mask])
            total_power = np.sum(psd)
            
            brain_power_ratio = (alpha_power + beta_power) / total_power
            
            print(f"      Alpha功率: {alpha_power:.1f}")
            print(f"      Beta功率: {beta_power:.1f}")
            print(f"      总功率: {total_power:.1f}")
            print(f"      脑电功率比: {brain_power_ratio:.3f}")
            
            freq_quality = 0.6 * structure_score + 0.4 * min(1.0, brain_power_ratio * 2.0)
            print(f"      最终频域质量: {freq_quality:.3f}")
        
        analyze_frequency_features(data1[0], "极小信号")
        analyze_frequency_features(data2[0], "正常脑电")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplified_quality():
    """测试简化的质量评估"""
    print("\n🎯 测试简化的质量评估方法")
    print("=" * 60)
    
    try:
        # 简化的质量评估函数
        def simple_quality_assessment(data):
            """简化的质量评估"""
            channel_scores = []
            
            for ch in range(data.shape[0]):
                channel_data = data[ch]
                signal_std = np.std(channel_data)
                signal_var = np.var(channel_data)
                
                # 基于幅值的简单评估
                if signal_std < 5.0:
                    # 信号太小
                    score = 0.1
                elif signal_std < 20.0:
                    # 信号较小
                    score = 0.3
                elif signal_std < 100.0:
                    # 信号适中
                    score = 0.6
                elif signal_std < 1000.0:
                    # 信号良好
                    score = 0.8
                elif signal_std < 5000.0:
                    # 信号较大
                    score = 0.6
                else:
                    # 信号过大
                    score = 0.2
                
                # 基于方差的连接检查
                if signal_var < 1.0:
                    score *= 0.1  # 连接很差
                elif signal_var < 10.0:
                    score *= 0.5  # 连接不良
                
                channel_scores.append(score)
            
            return np.mean(channel_scores)
        
        # 测试不同信号
        test_cases = [
            ("未连接", np.random.randn(8, 500) * 1.0),
            ("电极脱落", np.random.randn(8, 500) * 5.0),
            ("连接不良", np.random.randn(8, 500) * 50.0),
            ("正常脑电", np.random.randn(8, 500) * 100.0),
            ("高质量脑电", np.random.randn(8, 500) * 200.0),
            ("运动伪迹", np.random.randn(8, 500) * 2000.0),
        ]
        
        print("📊 简化质量评估结果:")
        for name, data in test_cases:
            score = simple_quality_assessment(data)
            avg_std = np.mean([np.std(data[ch]) for ch in range(data.shape[0])])
            print(f"   {name:12s}: {score:.3f} (平均标准差: {avg_std:.1f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化质量评估测试失败: {e}")
        return False

def main():
    """主函数"""
    print("信号质量评估组件调试")
    print("=" * 80)
    
    tests = [
        debug_quality_components,
        test_simplified_quality,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n调试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    
    if passed == total:
        print("\n🔍 调试发现:")
        print("1. 频域特征评估可能过于复杂，导致区分度不够")
        print("2. 谱熵计算对所有信号都给出相似的结果")
        print("3. 需要简化评估算法，重点关注幅值和连接状态")
        print("4. 建议使用基于幅值的简单评估方法")
        
        print("\n💡 改进建议:")
        print("• 简化频域特征评估，减少复杂计算")
        print("• 增加幅值评估的权重")
        print("• 使用更直观的连接状态检测")
        print("• 减少对谱熵等复杂指标的依赖")
        
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
