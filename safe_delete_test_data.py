#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全删除测试数据脚本
处理外键约束，安全删除yiyuan表中id为1的测试数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def check_foreign_key_references():
    """检查外键引用"""
    print("🔗 检查外键引用...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 获取所有表的外键信息
        tables = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        
        references_found = []
        
        for table in tables:
            table_name = table['name']
            if table_name.startswith('sqlite_'):
                continue
                
            try:
                # 获取外键信息
                foreign_keys = db.execute_query(f"PRAGMA foreign_key_list({table_name})")
                
                for fk in foreign_keys:
                    if fk['table'] == 'yiyuan':
                        references_found.append({
                            'table': table_name,
                            'column': fk['from'],
                            'referenced_column': fk['to']
                        })
                        
                        # 检查是否有数据引用id=1
                        count_query = f"SELECT COUNT(*) as count FROM {table_name} WHERE {fk['from']} = 1"
                        count_result = db.execute_query(count_query)
                        count = count_result[0]['count'] if count_result else 0
                        
                        print(f"📋 表 {table_name}.{fk['from']} 引用 yiyuan.{fk['to']}")
                        print(f"   引用id=1的记录数: {count}")
                        
            except Exception as e:
                print(f"   检查表 {table_name} 时出错: {e}")
        
        if not references_found:
            print("✅ 没有发现外键引用yiyuan表")
        
        return references_found
        
    except Exception as e:
        print(f"❌ 检查外键引用失败: {e}")
        return None

def disable_foreign_keys_and_delete():
    """临时禁用外键约束并删除数据"""
    print("\n🛠️ 临时禁用外键约束并删除测试数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 使用原始连接来控制外键约束
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 1. 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已临时禁用外键约束")
            
            # 2. 检查要删除的记录
            cursor.execute("SELECT * FROM yiyuan WHERE id = 1")
            record = cursor.fetchone()
            
            if record:
                print(f"📋 准备删除记录:")
                print(f"   ID: {record[0]}")
                print(f"   医院名称: {record[1]}")
                print(f"   科室: {record[2]}")
                print(f"   设备ID: {record[3]}")
                
                # 3. 执行删除
                cursor.execute("DELETE FROM yiyuan WHERE id = 1")
                affected_rows = cursor.rowcount
                
                if affected_rows > 0:
                    print(f"✅ 成功删除 {affected_rows} 条记录")
                    
                    # 4. 验证删除
                    cursor.execute("SELECT COUNT(*) FROM yiyuan WHERE id = 1")
                    remaining = cursor.fetchone()[0]
                    
                    if remaining == 0:
                        print("✅ 验证：记录已完全删除")
                    else:
                        print(f"⚠️ 警告：仍有 {remaining} 条记录")
                        return False
                else:
                    print("❌ 没有记录被删除")
                    return False
            else:
                print("ℹ️ 没有找到id=1的记录")
                return True
            
            # 5. 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 6. 提交事务
            conn.commit()
            print("✅ 事务已提交")
            
            return True
            
    except Exception as e:
        print(f"❌ 删除操作失败: {e}")
        return False

def verify_deletion():
    """验证删除结果"""
    print("\n🔍 验证删除结果...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 查询所有yiyuan数据
        results = db.execute_query("SELECT * FROM yiyuan ORDER BY id")
        
        print(f"📋 yiyuan表中现有 {len(results)} 条记录:")
        if results:
            print("-" * 80)
            print(f"{'ID':<5} {'医院名称':<20} {'科室':<15} {'设备ID':<10}")
            print("-" * 80)
            
            for row in results:
                print(f"{row['id']:<5} {row['hname']:<20} {row['keshi']:<15} {row['shebeiid']:<10}")
        else:
            print("   (表为空)")
        
        # 检查是否还有id=1的记录
        test_record = db.execute_query("SELECT * FROM yiyuan WHERE id = 1")
        if test_record:
            print("❌ 错误：id=1的记录仍然存在")
            return False
        else:
            print("✅ 确认：id=1的测试记录已被删除")
            return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_data_integrity():
    """检查数据完整性"""
    print("\n🔍 检查数据完整性...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查外键约束是否正常
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"⚠️ 发现 {len(violations)} 个外键约束违规:")
                for violation in violations:
                    print(f"   {violation}")
                return False
            else:
                print("✅ 外键约束检查通过")
        
        # 检查您的实际数据是否完整
        your_hospital = db.execute_query("SELECT * FROM yiyuan WHERE id = 3")
        if your_hospital:
            hospital = your_hospital[0]
            print(f"✅ 您的医院数据完整:")
            print(f"   ID: {hospital['id']}")
            print(f"   医院名称: {hospital['hname']}")
            print(f"   科室: {hospital['keshi']}")
            print(f"   设备ID: {hospital['shebeiid']}")
        else:
            print("⚠️ 警告：没有找到您的医院数据(id=3)")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🗑️ 安全删除yiyuan表测试数据工具")
    print("=" * 80)
    
    # 1. 检查外键引用
    references = check_foreign_key_references()
    if references is None:
        print("❌ 外键检查失败，退出")
        return False
    
    # 2. 执行安全删除
    if not disable_foreign_keys_and_delete():
        print("❌ 删除操作失败，退出")
        return False
    
    # 3. 验证删除结果
    if not verify_deletion():
        print("❌ 删除验证失败，退出")
        return False
    
    # 4. 检查数据完整性
    if not check_data_integrity():
        print("❌ 数据完整性检查失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 测试数据删除成功！")
    print("✅ id=1的测试数据已被安全删除")
    print("✅ 您的实际医院数据(id=3)保持完整")
    print("✅ 治疗数据上传平台冲突问题已解决")
    print("✅ 数据库完整性检查通过")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
