#!/usr/bin/env python3
"""
诊断EEGNet模型性能问题
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_model_performance():
    """诊断模型性能问题"""
    print("=" * 60)
    print("EEGNet模型性能诊断")
    print("=" * 60)
    
    try:
        from core.ml_model import ModelManager
        
        print("1. 加载当前模型...")
        manager = ModelManager()
        model = manager.load_model("MI_Model_1748771308")
        
        if not model:
            print("   ❌ 无法加载模型")
            return False
        
        print(f"   ✅ 模型加载成功: {model.model_name}")
        print(f"   - 使用EEGNet: {model.use_eegnet}")
        print(f"   - 已训练: {model.is_trained}")
        
        # 获取模型信息
        info = model.get_model_info()
        print(f"   - 训练样本数: {info.total_samples}")
        print(f"   - 训练轮次: {info.training_rounds}")
        if info.performance:
            print(f"   - 训练准确率: {info.performance.accuracy:.3f}")
            print(f"   - 验证准确率: {info.performance.val_accuracy:.3f}")
        
        print("\n2. 测试模型预测多样性...")
        predictions = []
        confidences = []
        
        # 生成多种不同的测试数据
        test_cases = [
            ("随机噪声", np.random.randn(8, 250) * 50),
            ("低频信号", np.sin(2 * np.pi * 8 * np.linspace(0, 2, 250)) * 100),
            ("高频信号", np.sin(2 * np.pi * 30 * np.linspace(0, 2, 250)) * 100),
            ("混合信号", np.random.randn(8, 250) * 100 + np.sin(2 * np.pi * 10 * np.linspace(0, 2, 250)) * 200),
            ("零信号", np.zeros((8, 250))),
            ("大幅值信号", np.random.randn(8, 250) * 1000),
        ]
        
        for name, test_data in test_cases:
            # 确保数据形状正确
            if test_data.ndim == 1:
                test_data = np.tile(test_data, (8, 1))
            
            prediction, confidence = model.predict(test_data)
            predictions.append(prediction)
            confidences.append(confidence)
            
            print(f"   {name:12s}: 类别={prediction}, 置信度={confidence:.4f}")
        
        # 分析预测多样性
        unique_predictions = len(set(predictions))
        confidence_range = max(confidences) - min(confidences)
        
        print(f"\n   📊 预测多样性分析:")
        print(f"   - 不同预测类别数: {unique_predictions}/2")
        print(f"   - 置信度范围: {confidence_range:.4f}")
        print(f"   - 平均置信度: {np.mean(confidences):.4f}")
        print(f"   - 置信度标准差: {np.std(confidences):.4f}")
        
        if unique_predictions == 1:
            print("   ⚠️  模型总是预测同一类别 - 可能存在问题")
        
        if confidence_range < 0.01:
            print("   ⚠️  置信度变化很小 - 模型可能没有学到有效特征")
        
        print("\n3. 分析训练数据质量...")
        if hasattr(model, 'training_data') and len(model.training_data) > 0:
            training_data = np.array(model.training_data)
            training_labels = np.array(model.training_labels)
            
            print(f"   - 训练数据形状: {training_data.shape}")
            print(f"   - 标签分布: {np.bincount(training_labels)}")
            
            # 分析不同类别的数据差异
            class_0_data = training_data[training_labels == 0]
            class_1_data = training_data[training_labels == 1]
            
            if len(class_0_data) > 0 and len(class_1_data) > 0:
                # 计算类别间的统计差异
                mean_0 = np.mean(class_0_data)
                mean_1 = np.mean(class_1_data)
                std_0 = np.std(class_0_data)
                std_1 = np.std(class_1_data)
                
                print(f"   - 类别0平均值: {mean_0:.2f}, 标准差: {std_0:.2f}")
                print(f"   - 类别1平均值: {mean_1:.2f}, 标准差: {std_1:.2f}")
                print(f"   - 类别间均值差异: {abs(mean_1 - mean_0):.2f}")
                
                if abs(mean_1 - mean_0) < 10:
                    print("   ⚠️  类别间差异很小 - 可能缺乏区分性特征")
        
        print("\n4. 建议的解决方案...")
        
        if unique_predictions == 1 and confidence_range < 0.01:
            print("   🔧 问题诊断: 模型没有学到有效的分类特征")
            print("   💡 建议解决方案:")
            print("   1. 重新收集训练数据，确保运动想象和休息状态有明显差异")
            print("   2. 增加训练数据量（建议至少50-100个样本）")
            print("   3. 检查数据采集时的指导是否清晰")
            print("   4. 考虑使用预训练模型进行迁移学习")
            
            # 提供快速修复方案
            print("\n   🚀 快速修复方案:")
            print("   1. 调整决策阈值到一个更合理的范围（0.4-0.6）")
            print("   2. 重新训练模型，增加更多样化的数据")
            print("   3. 使用数据增强技术")
        
        print("\n5. 测试决策阈值效果...")
        test_data = np.random.randn(8, 250) * 100
        
        thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
        for threshold in thresholds:
            info.decision_threshold = threshold
            pred, conf, status = model.predict_with_adjustment(test_data)
            print(f"   阈值{threshold:.1f}: 类别={pred}, 置信度={conf:.3f}, 状态={status}")
        
        print("\n6. 推荐的参数设置...")
        avg_confidence = np.mean(confidences)
        
        if avg_confidence > 0.7:
            recommended_threshold = 0.6
        elif avg_confidence > 0.6:
            recommended_threshold = 0.5
        else:
            recommended_threshold = max(0.3, avg_confidence - 0.1)
        
        print(f"   💡 推荐决策阈值: {recommended_threshold:.2f}")
        print(f"   💡 推荐置信度阈值: {recommended_threshold + 0.1:.2f}")
        
        # 应用推荐设置
        info.decision_threshold = recommended_threshold
        info.confidence_threshold = recommended_threshold + 0.1
        
        print(f"\n   ✅ 已应用推荐设置")
        print(f"   - 决策阈值: {info.decision_threshold:.2f}")
        print(f"   - 置信度阈值: {info.confidence_threshold:.2f}")
        
        print("\n" + "=" * 60)
        print("🎯 诊断完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    diagnose_model_performance()
