#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终检查和修复脚本
检查并修复数据库中的外键约束问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def detailed_foreign_key_check():
    """详细的外键约束检查"""
    print("🔍 详细外键约束检查...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查外键约束违规
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"📋 发现 {len(violations)} 个外键约束违规:")
                for i, violation in enumerate(violations):
                    print(f"   违规 {i+1}: {list(violation)}")
                    
                    # 尝试解析违规信息
                    if len(violation) >= 4:
                        table = violation[0]
                        rowid = violation[1] 
                        parent_table = violation[2]
                        fk_index = violation[3]
                        
                        print(f"      表: {table}, 行ID: {rowid}, 父表: {parent_table}, 外键索引: {fk_index}")
                        
                        # 查询具体的违规记录
                        try:
                            if table == 'bingren':
                                cursor.execute(f"SELECT bianhao, name, yiyuanid FROM bingren WHERE rowid = ?", (rowid,))
                                record = cursor.fetchone()
                                if record:
                                    print(f"      违规记录: 患者编号={record[0]}, 姓名={record[1]}, 医院ID={record[2]}")
                        except Exception as e:
                            print(f"      查询违规记录失败: {e}")
                
                return violations
            else:
                print("✅ 没有发现外键约束违规")
                return []
        
    except Exception as e:
        print(f"❌ 外键约束检查失败: {e}")
        return None

def check_all_references():
    """检查所有引用关系"""
    print("\n🔗 检查所有引用关系...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查患者-医院引用
        print("📋 患者-医院引用检查:")
        patient_hospital_check = db.execute_query("""
            SELECT 
                b.bianhao,
                b.name,
                b.yiyuanid,
                y.hname,
                CASE WHEN y.id IS NULL THEN '❌ 无效引用' ELSE '✅ 有效引用' END as status
            FROM bingren b
            LEFT JOIN yiyuan y ON b.yiyuanid = y.id
            ORDER BY b.bianhao
        """)
        
        invalid_refs = 0
        for ref in patient_hospital_check:
            status_icon = "❌" if "无效" in ref['status'] else "✅"
            print(f"   {status_icon} 患者 {ref['name']} (编号:{ref['bianhao']}) -> 医院ID:{ref['yiyuanid']} ({ref['hname'] or '不存在'})")
            if "无效" in ref['status']:
                invalid_refs += 1
        
        if invalid_refs == 0:
            print("✅ 所有患者-医院引用都有效")
        else:
            print(f"❌ 发现 {invalid_refs} 个无效引用")
        
        # 检查其他可能的引用
        print("\n📋 检查其他表的引用:")
        
        # 检查治疗记录表
        try:
            treatment_check = db.execute_query("SELECT COUNT(*) as count FROM zhiliao")
            if treatment_check:
                print(f"   治疗记录表: {treatment_check[0]['count']} 条记录")
        except Exception as e:
            print(f"   治疗记录表检查失败: {e}")
        
        return invalid_refs == 0
        
    except Exception as e:
        print(f"❌ 引用关系检查失败: {e}")
        return False

def rebuild_foreign_keys():
    """重建外键约束"""
    print("\n🔧 重建外键约束...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 1. 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已禁用外键约束")
            
            # 2. 检查数据完整性
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()
            print(f"📋 数据完整性检查: {integrity_result[0]}")
            
            # 3. 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 4. 再次检查外键约束
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"❌ 重建后仍有 {len(violations)} 个违规")
                return False
            else:
                print("✅ 外键约束重建成功")
                return True
        
    except Exception as e:
        print(f"❌ 重建外键约束失败: {e}")
        return False

def final_verification():
    """最终验证"""
    print("\n🎯 最终验证...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. 检查yiyuan表
        yiyuan_data = db.execute_query("SELECT * FROM yiyuan ORDER BY id")
        print(f"📋 yiyuan表: {len(yiyuan_data)} 条记录")
        for hospital in yiyuan_data:
            print(f"   ID: {hospital['id']}, 名称: {hospital['hname']}")
        
        # 2. 检查患者数据分布
        patient_distribution = db.execute_query("""
            SELECT yiyuanid, COUNT(*) as count 
            FROM bingren 
            GROUP BY yiyuanid 
            ORDER BY yiyuanid
        """)
        
        print(f"\n📊 患者分布:")
        for dist in patient_distribution:
            hospital_name = "未知医院"
            if dist['yiyuanid']:
                hospital_info = db.execute_query("SELECT hname FROM yiyuan WHERE id = ?", (dist['yiyuanid'],))
                if hospital_info:
                    hospital_name = hospital_info[0]['hname']
            
            print(f"   医院ID {dist['yiyuanid']} ({hospital_name}): {dist['count']} 个患者")
        
        # 3. 最终外键检查
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            final_violations = cursor.fetchall()
            
            if final_violations:
                print(f"\n❌ 最终检查仍有 {len(final_violations)} 个外键约束违规")
                return False
            else:
                print(f"\n✅ 最终外键约束检查通过")
                return True
        
    except Exception as e:
        print(f"❌ 最终验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 数据库外键约束最终修复工具")
    print("=" * 80)
    
    # 1. 详细检查外键约束违规
    violations = detailed_foreign_key_check()
    if violations is None:
        print("❌ 外键约束检查失败，退出")
        return False
    
    # 2. 检查所有引用关系
    if not check_all_references():
        print("❌ 引用关系检查发现问题")
    
    # 3. 如果有违规，尝试重建外键约束
    if violations:
        if not rebuild_foreign_keys():
            print("❌ 重建外键约束失败")
            return False
    
    # 4. 最终验证
    if not final_verification():
        print("❌ 最终验证失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 数据库修复完成！")
    print("✅ 测试数据(id=1)已删除")
    print("✅ 外键约束检查通过")
    print("✅ 所有患者数据关联到您的医院(id=3)")
    print("✅ 治疗数据上传平台冲突问题已解决")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
