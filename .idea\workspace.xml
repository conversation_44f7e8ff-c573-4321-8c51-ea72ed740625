<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="df74ccb8-892b-4897-b0aa-d75a23498812" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xc38TDuTb0bQi3el32UWtVFt6N" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python 测试.Python 测试 (main.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (test_enhanced_algorithms.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (test_stimulation_fix.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.Python 测试 (test_system.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python.adaptive_classification_manager.executor&quot;: &quot;Run&quot;,
    &quot;Python.eeg_device.executor&quot;: &quot;Run&quot;,
    &quot;Python.eegnet_model.executor&quot;: &quot;Run&quot;,
    &quot;Python.http_client.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_window.executor&quot;: &quot;Run&quot;,
    &quot;Python.patient_management_ui.executor&quot;: &quot;Run&quot;,
    &quot;Python.quick_start.executor&quot;: &quot;Run&quot;,
    &quot;Python.stimulation_device.executor&quot;: &quot;Run&quot;,
    &quot;Python.treatment_ui.executor&quot;: &quot;Run&quot;,
    &quot;Python.udp_communicator.executor&quot;: &quot;Run&quot;,
    &quot;Python.user_management_ui.executor&quot;: &quot;Run&quot;,
    &quot;Python.voice_prompt.executor&quot;: &quot;Run&quot;,
    &quot;Python.样式.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/NK_QT/QT6/NK/NK/Python_NK_System&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.25659.43" />
        <option value="bundled-python-sdk-181015f7ab06-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="df74ccb8-892b-4897-b0aa-d75a23498812" name="更改" comment="" />
      <created>1748223374645</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748223374645</updated>
      <workItem from="1748223375827" duration="21932000" />
      <workItem from="1748302363066" duration="363000" />
      <workItem from="1748302929999" duration="22062000" />
      <workItem from="1748389675592" duration="21500000" />
      <workItem from="1748476414236" duration="14825000" />
      <workItem from="1748505443958" duration="7203000" />
      <workItem from="1748563102664" duration="20392000" />
      <workItem from="1748595037730" duration="8748000" />
      <workItem from="1748604721141" duration="30000" />
      <workItem from="1748649359085" duration="29862000" />
      <workItem from="1748736100941" duration="8278000" />
      <workItem from="1748766356986" duration="11178000" />
      <workItem from="1748822731150" duration="20336000" />
      <workItem from="1748910220491" duration="36109000" />
      <workItem from="1748995105436" duration="31482000" />
      <workItem from="1749081649718" duration="34951000" />
      <workItem from="1749167695900" duration="14440000" />
      <workItem from="1749427413774" duration="4974000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Python_NK_System$http_client.coverage" NAME="http_client 覆盖结果" MODIFIED="1749008141633" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$udp_communicator.coverage" NAME="udp_communicator 覆盖结果" MODIFIED="1749036839370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$.coverage" NAME=" 覆盖结果" MODIFIED="1749179950639" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Python_NK_System$main.coverage" NAME="main 覆盖结果" MODIFIED="1749461555093" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Python_NK_System$eegnet_model.coverage" NAME="eegnet_model 覆盖结果" MODIFIED="1749042912237" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$treatment_ui.coverage" NAME="treatment_ui 覆盖结果" MODIFIED="1749104688836" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui" />
    <SUITE FILE_PATH="coverage/Python_NK_System$patient_management_ui.coverage" NAME="patient_management_ui 覆盖结果" MODIFIED="1748698986070" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui" />
    <SUITE FILE_PATH="coverage/Python_NK_System$user_management_ui.coverage" NAME="user_management_ui 覆盖结果" MODIFIED="1748420481421" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ui" />
    <SUITE FILE_PATH="coverage/Python_NK_System$quick_start.coverage" NAME="quick_start 覆盖结果" MODIFIED="1748223549286" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Python_NK_System$eeg_device.coverage" NAME="eeg_device 覆盖结果" MODIFIED="1748316108026" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$voice_prompt.coverage" NAME="voice_prompt 覆盖结果" MODIFIED="1748349456409" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$stimulation_device.coverage" NAME="stimulation_device 覆盖结果" MODIFIED="1748501680577" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$adaptive_classification_manager.coverage" NAME="adaptive_classification_manager 覆盖结果" MODIFIED="1748859453341" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
    <SUITE FILE_PATH="coverage/Python_NK_System$main_window.coverage" NAME="main_window 覆盖结果" MODIFIED="1748490786919" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/core" />
  </component>
</project>