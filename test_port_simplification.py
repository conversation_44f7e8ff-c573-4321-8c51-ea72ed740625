#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试端口简化功能
Test script for port simplification
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_port_simplification():
    """测试端口简化功能"""
    print("=" * 60)
    print("电刺激设备端口简化测试")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：检查端口选择控件是否已移除
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查端口选择控件是否已移除...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了端口选择相关控件
        removed_elements = [
            'self.stimulation_port_combo = QComboBox()',
            'refresh_port_button = QPushButton("刷新")',
            'refresh_port_button.clicked.connect(self._populate_stimulation_ports)',
            '_populate_stimulation_ports'
        ]
        
        found_elements = []
        for element in removed_elements:
            if element in content:
                found_elements.append(element)
        
        if found_elements:
            print(f"❌ 仍存在应移除的元素: {found_elements}")
        else:
            print("✅ 端口选择控件已成功移除")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查端口选择控件移除失败: {e}")
    
    # 测试2：检查新的端口显示控件
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查新的端口显示控件...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新的端口显示控件
        new_elements = [
            'self.stimulation_port_label = QLabel("--")',
            'settings_hint = QLabel("(在系统设置中配置)")',
            'self._update_port_display()',
            'port_info_layout = QHBoxLayout()'
        ]
        
        missing_elements = []
        for element in new_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ 缺少新的端口显示元素: {missing_elements}")
        else:
            print("✅ 新的端口显示控件已正确添加")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查新端口显示控件失败: {e}")
    
    # 测试3：检查_get_selected_port方法修改
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查_get_selected_port方法修改...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_get_selected_port方法的新实现
        new_implementation = [
            'def _get_selected_port(self) -> int:',
            '"""从系统配置获取端口号"""',
            'from utils.app_config import AppConfig',
            'port_num = AppConfig.STIMULATION_CONFIG.get(\'port_num\', 1)',
            '从系统配置获取端口号'
        ]
        
        missing_implementation = []
        for impl in new_implementation:
            if impl not in content:
                missing_implementation.append(impl)
        
        if missing_implementation:
            print(f"❌ _get_selected_port方法实现不完整: {missing_implementation}")
        else:
            print("✅ _get_selected_port方法已正确修改为从系统配置获取")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查_get_selected_port方法失败: {e}")
    
    # 测试4：检查_update_port_display方法
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查_update_port_display方法...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_update_port_display方法
        display_method = [
            'def _update_port_display(self):',
            '"""更新端口显示"""',
            'self.stimulation_port_label.setText(f"COM{port_num}")',
            '更新端口显示'
        ]
        
        missing_display = []
        for method in display_method:
            if method not in content:
                missing_display.append(method)
        
        if missing_display:
            print(f"❌ _update_port_display方法不完整: {missing_display}")
        else:
            print("✅ _update_port_display方法已正确添加")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查_update_port_display方法失败: {e}")
    
    # 测试5：检查refresh_stimulation_config方法
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查refresh_stimulation_config方法...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查refresh_stimulation_config方法
        refresh_method = [
            'def refresh_stimulation_config(self):',
            '"""刷新电刺激配置显示（供外部调用）"""',
            'self._update_port_display()',
            '已刷新电刺激配置显示'
        ]
        
        missing_refresh = []
        for method in refresh_method:
            if method not in content:
                missing_refresh.append(method)
        
        if missing_refresh:
            print(f"❌ refresh_stimulation_config方法不完整: {missing_refresh}")
        else:
            print("✅ refresh_stimulation_config方法已正确添加")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查refresh_stimulation_config方法失败: {e}")
    
    # 测试6：检查连接逻辑是否保持不变
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查连接逻辑是否保持不变...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查连接逻辑关键部分
        connection_logic = [
            'def connect_stimulation_device(self):',
            'port_num = self._get_selected_port()',
            'self.add_stimulation_log(f"尝试连接端口: COM{port_num}")',
            'result = self.device.connect(self.port_num)'
        ]
        
        missing_logic = []
        for logic in connection_logic:
            if logic not in content:
                missing_logic.append(logic)
        
        if missing_logic:
            print(f"❌ 连接逻辑不完整: {missing_logic}")
        else:
            print("✅ 连接逻辑保持完整")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查连接逻辑失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 60)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有端口简化测试通过！")
        print("\n✅ 简化完成的功能:")
        print("   🗑️ 移除了端口选择下拉框")
        print("   🗑️ 移除了刷新端口按钮")
        print("   🗑️ 删除了_populate_stimulation_ports方法")
        print("   📱 添加了简洁的端口信息显示")
        print("   ⚙️ 添加了系统设置提示")
        print("   🔄 修改为从系统配置自动获取端口")
        print("\n🚀 新的工作流程:")
        print("   1. 在系统设置中配置端口号")
        print("   2. 治疗界面自动显示配置的端口")
        print("   3. 点击连接按钮直接使用配置的端口")
        print("   4. 无需手动选择或刷新端口")
        print("\n💡 用户体验改进:")
        print("   ✨ 界面更简洁，减少用户操作步骤")
        print("   🎯 避免端口选择错误")
        print("   ⚡ 连接更快速，无需选择")
        print("   🔧 统一在设置中管理端口配置")
        return 0
    else:
        print("⚠️ 部分端口简化测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    sys.exit(test_port_simplification())
