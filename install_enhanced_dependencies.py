#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装增强脑电信号处理依赖库
Install Enhanced EEG Signal Processing Dependencies

作者: AI Assistant
版本: 1.0.0
"""

import subprocess
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        # 尝试导入包
        __import__(import_name)
        logger.info(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        logger.info(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            logger.info(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {package_name} 安装失败: {e}")
            return False

def main():
    """主函数"""
    logger.info("🚀 开始安装增强脑电信号处理依赖库...")
    
    # 需要安装的包列表
    packages = [
        # MNE-Python - 专业脑电信号处理库
        ("mne", "mne"),
        
        # PyWavelets - 小波变换库
        ("PyWavelets", "pywt"),
        
        # 确保scikit-learn是最新版本
        ("scikit-learn>=1.0.0", "sklearn"),
        
        # 确保scipy是最新版本
        ("scipy>=1.7.0", "scipy"),
        
        # 确保numpy是最新版本
        ("numpy>=1.21.0", "numpy"),
        
        # 可选：更高级的信号处理库
        ("matplotlib", "matplotlib"),  # 用于可视化
        ("seaborn", "seaborn"),        # 用于统计可视化
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_spec, import_name in packages:
        if install_package(package_spec, import_name):
            success_count += 1
    
    logger.info(f"\n📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        logger.info("🎉 所有依赖库安装完成！")
        
        # 验证关键功能
        logger.info("\n🔍 验证关键功能...")
        
        try:
            import mne
            logger.info(f"✅ MNE-Python 版本: {mne.__version__}")
        except ImportError:
            logger.warning("⚠️ MNE-Python 导入失败")
        
        try:
            import pywt
            logger.info(f"✅ PyWavelets 版本: {pywt.__version__}")
        except ImportError:
            logger.warning("⚠️ PyWavelets 导入失败")
        
        try:
            from sklearn import __version__ as sklearn_version
            logger.info(f"✅ Scikit-learn 版本: {sklearn_version}")
        except ImportError:
            logger.warning("⚠️ Scikit-learn 导入失败")
        
        logger.info("\n🎯 增强脑电信号处理系统已准备就绪！")
        logger.info("新功能包括:")
        logger.info("  • MNE-Python完整CSP实现")
        logger.info("  • 正则化CSP(rCSP)")
        logger.info("  • Filter Bank CSP(FBCSP)")
        logger.info("  • 小波变换特征提取")
        logger.info("  • 自适应频带选择")
        logger.info("  • 改进的伪迹检测算法")
        
    else:
        logger.warning(f"⚠️ 部分依赖库安装失败，可能影响增强功能的使用")
        logger.info("请检查网络连接或手动安装失败的包")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
