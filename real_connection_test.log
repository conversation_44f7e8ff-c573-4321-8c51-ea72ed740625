2025-06-03 15:34:46,521 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 15:34:46,522 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 26
2025-06-03 15:34:46,524 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:34:46,524 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:34:46,525 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 26
2025-06-03 15:34:49,036 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:34:49,037 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:34:50,543 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:34:51,348 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:34:51,519 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:34:51,708 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:34:51,709 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:34:51,709 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:34:51,710 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:34:52,032 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:34:53,541 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:34:53,542 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:34:53,544 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 26
2025-06-03 15:34:53,647 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 26
2025-06-03 15:34:56,156 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:34:56,156 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:34:57,157 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:34:57,895 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:34:58,075 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:34:58,263 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:34:58,264 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:34:58,264 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:34:58,265 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:34:58,590 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:35:00,096 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:00,097 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:35:00,598 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 26
2025-06-03 15:35:00,599 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 26
2025-06-03 15:35:03,107 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:03,107 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:35:04,109 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:35:04,914 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:35:05,084 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:35:05,273 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:35:05,274 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:35:05,274 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:35:05,274 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:35:05,600 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:35:07,107 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:07,107 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:35:07,609 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 26
2025-06-03 15:35:07,610 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 26
2025-06-03 15:35:10,115 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:10,115 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:35:11,118 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:35:11,922 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:35:12,092 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:35:12,279 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:35:12,279 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:35:12,280 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:35:12,280 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:35:12,605 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:35:14,113 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:14,114 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:35:14,618 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:35:14,619 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:35:16,125 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:35:16,126 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
