# 患者数据上传功能实现文档

## 概述

本文档描述了在NK脑机接口系统中实现的患者数据上传功能。该功能在添加患者到本地数据库的同时，自动将患者数据上传到平台服务器，完全按照原QT程序的实现方式。

## 功能特点

### ✅ 核心功能
- **同步上传**: 添加患者时自动上传到平台
- **状态记录**: 无论上传成功与否都在本地数据库status字段记录状态
- **格式兼容**: 完全按照QT程序的JSON字段名称组织数据
- **错误处理**: 完善的网络异常和错误处理机制
- **重试机制**: 支持网络失败时的自动重试
- **日志记录**: 详细的操作日志记录

### 📊 上传状态管理
- `pending`: 待上传（默认状态）
- `success`: 上传成功
- `failed`: 上传失败
- `timeout`: 上传超时

## 实现架构

### 1. HTTP客户端模块 (`core/http_client.py`)

```python
class PatientDataUploader:
    """患者数据上传器"""
    
    def upload_patient_data(self, patient_data, hospital_info):
        """上传患者数据到平台"""
        # 组织JSON数据
        # 执行HTTP请求
        # 处理响应结果
        # 返回上传结果
```

**主要功能**:
- JSON数据格式化（按QT程序格式）
- HTTP GET请求发送
- 网络连接测试
- 重试机制实现
- 错误处理和状态管理

### 2. 数据库集成 (`core/database_manager.py`)

**修改内容**:
- `add_patient()` 方法增加status字段支持
- 默认status为'pending'状态

### 3. 患者管理界面集成 (`ui/patient_management_ui.py`)

**修改内容**:
- 导入PatientDataUploader和UploadStatus
- 初始化数据上传器实例
- 在save_patient()方法中集成上传逻辑
- 根据上传结果显示不同的成功消息
- 在操作日志中记录上传状态

## JSON数据格式

按照QT程序的格式组织数据：

```json
{
    "num": "患者编号",
    "name": "患者姓名", 
    "sex": "性别",
    "age": 年龄数字,
    "hospitalID": 医院编号,
    "idCard": "身份证号",
    "department": "科室",
    "equipmentNum": "设备编号",
    "attdoctor": "主治医师",
    "operator": "操作员",
    "jiwangshi": "既往史",
    "zhenduan": "诊断"
}
```

## 上传流程

### 添加患者时的完整流程：

1. **表单验证** - 验证必填字段和数据格式
2. **收集数据** - 从表单收集患者信息
3. **获取医院信息** - 从数据库获取医院配置
4. **执行上传** - 调用上传器上传数据到平台
5. **设置状态** - 根据上传结果设置status字段
6. **保存本地** - 将患者数据（含状态）保存到本地数据库
7. **显示结果** - 根据上传状态显示相应的成功消息
8. **记录日志** - 记录操作和上传状态到系统日志

### 上传逻辑代码示例：

```python
# 如果是添加新患者，尝试上传到平台
if not self.current_patient:
    try:
        # 获取医院信息
        hospital_info = self.db_manager.get_hospital_info()
        if hospital_info:
            # 执行上传
            upload_result = self.data_uploader.upload_patient_data(patient_data, hospital_info)
            
            if upload_result.success:
                upload_status = "success"
            else:
                upload_status = "failed"
        else:
            upload_status = "failed"
            
    except Exception as e:
        upload_status = "failed"

# 设置上传状态
patient_data['status'] = upload_status

# 保存到本地数据库
success = self.db_manager.add_patient(patient_data)
```

## 配置说明

### 网络配置 (`utils/app_config.py`)

```python
NETWORK_CONFIG = {
    'http': {
        'base_url': 'http://111.17.215.37:8082/shdekf/Api/',
        'timeout': 30,
        'retry_count': 3,
        'retry_delay': 1.0,
    }
}
```

### 上传URL格式

```
http://111.17.215.37:8082/shdekf/Api/AppPatientServlet?act=sendPatients&data={JSON数据}
```

## 错误处理

### 网络异常处理
- **连接超时**: 自动重试，最终标记为timeout状态
- **连接失败**: 自动重试，最终标记为failed状态
- **服务器错误**: 根据HTTP状态码处理

### 数据异常处理
- **医院信息缺失**: 跳过上传，标记为failed状态
- **JSON格式错误**: 记录错误日志，标记为failed状态

## 测试验证

### 测试脚本
- `test_patient_upload.py` - 完整功能测试
- `demo_patient_upload.py` - 功能演示

### 测试结果
✅ 所有测试通过，功能正常工作

## 用户体验

### 成功消息
- **上传成功**: "添加患者成功！数据已同步上传到平台。"
- **上传失败**: "添加患者成功！注意：数据上传到平台失败，已保存到本地数据库。"
- **默认情况**: "添加患者成功！数据已保存到本地数据库。"

### 日志记录
- 操作日志中包含上传状态信息
- 详细的网络请求和响应日志
- 错误和异常的完整记录

## 依赖要求

### Python库
- `requests` - HTTP请求库（需要安装）
- `json` - JSON数据处理（标准库）
- `logging` - 日志记录（标准库）

### 安装命令
```bash
pip install requests
```

## 总结

患者数据上传功能已成功集成到NK脑机接口系统中，完全符合原QT程序的实现方式。该功能具有以下优势：

1. **医疗级可靠性** - 完善的错误处理和状态管理
2. **用户友好** - 清晰的状态提示和错误信息
3. **数据完整性** - 无论上传成功与否都保存到本地
4. **可追溯性** - 详细的日志记录和状态跟踪
5. **兼容性** - 完全按照原QT程序的数据格式

该实现确保了患者数据的安全性和完整性，同时提供了良好的用户体验和系统可维护性。
