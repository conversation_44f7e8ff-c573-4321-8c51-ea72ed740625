#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复测试患者记录
修复引用已删除医院的测试患者记录
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def fix_test_patients():
    """修复测试患者记录"""
    print("🛠️ 修复测试患者记录...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 查找引用不存在医院的患者
        problem_patients = db.execute_query("""
            SELECT b.bianhao, b.name, b.yiyuanid
            FROM bingren b
            LEFT JOIN yiyuan y ON b.yiyuanid = y.id
            WHERE y.id IS NULL AND b.yiyuanid IS NOT NULL
        """)
        
        if not problem_patients:
            print("✅ 没有发现问题患者记录")
            return True
        
        print(f"📋 发现 {len(problem_patients)} 个问题患者记录:")
        for patient in problem_patients:
            print(f"   患者编号: {patient['bianhao']}, 姓名: {patient['name']}, 引用医院ID: {patient['yiyuanid']}")
        
        # 获取有效的医院ID
        valid_hospital = db.execute_query("SELECT id, hname FROM yiyuan ORDER BY id LIMIT 1")
        if not valid_hospital:
            print("❌ 没有找到有效的医院记录")
            return False
        
        target_hospital_id = valid_hospital[0]['id']
        target_hospital_name = valid_hospital[0]['hname']
        
        print(f"\n🎯 将问题患者记录更新为医院: {target_hospital_name} (ID: {target_hospital_id})")
        
        # 询问用户是否删除测试患者还是更新引用
        print("\n❓ 处理方式选择:")
        print("   1. 删除测试患者记录（推荐）")
        print("   2. 更新医院引用到您的实际医院")
        
        # 对于明显的测试数据，直接删除
        test_patient_numbers = [8888, 9999]
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已临时禁用外键约束")
            
            deleted_count = 0
            updated_count = 0
            
            for patient in problem_patients:
                patient_number = patient['bianhao']
                patient_name = patient['name']
                
                # 判断是否为测试数据
                is_test_data = (
                    patient_number in test_patient_numbers or
                    '测试' in patient_name or
                    'test' in patient_name.lower() or
                    'HDF5' in patient_name
                )
                
                if is_test_data:
                    # 删除测试患者
                    try:
                        # 先删除相关的治疗记录（检查可能的字段名）
                        treatment_deleted = 0
                        try:
                            # 尝试不同的可能字段名
                            cursor.execute("DELETE FROM zhiliao WHERE patient_number = ?", (patient_number,))
                            treatment_deleted = cursor.rowcount
                        except:
                            try:
                                cursor.execute("DELETE FROM zhiliao WHERE bingren_id = ?", (patient_number,))
                                treatment_deleted = cursor.rowcount
                            except:
                                try:
                                    cursor.execute("DELETE FROM zhiliao WHERE bianhao = ?", (patient_number,))
                                    treatment_deleted = cursor.rowcount
                                except:
                                    # 如果都失败了，跳过治疗记录删除
                                    pass

                        # 删除患者记录
                        cursor.execute("DELETE FROM bingren WHERE bianhao = ?", (patient_number,))
                        patient_deleted = cursor.rowcount

                        if patient_deleted > 0:
                            print(f"   ✅ 删除测试患者: {patient_name} (编号: {patient_number})")
                            if treatment_deleted > 0:
                                print(f"      同时删除了 {treatment_deleted} 条相关治疗记录")
                            deleted_count += 1
                        else:
                            print(f"   ❌ 删除患者失败: {patient_name}")

                    except Exception as e:
                        print(f"   ❌ 删除患者 {patient_name} 时出错: {e}")
                else:
                    # 更新医院引用
                    try:
                        cursor.execute(
                            "UPDATE bingren SET yiyuanid = ? WHERE bianhao = ?",
                            (target_hospital_id, patient_number)
                        )
                        if cursor.rowcount > 0:
                            print(f"   ✅ 更新患者医院引用: {patient_name} (编号: {patient_number})")
                            updated_count += 1
                        else:
                            print(f"   ❌ 更新患者失败: {patient_name}")
                            
                    except Exception as e:
                        print(f"   ❌ 更新患者 {patient_name} 时出错: {e}")
            
            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 提交事务
            conn.commit()
            
            print(f"\n📊 处理结果:")
            print(f"   删除测试患者: {deleted_count} 个")
            print(f"   更新医院引用: {updated_count} 个")
            
            return True
        
    except Exception as e:
        print(f"❌ 修复测试患者记录失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查外键约束
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"❌ 仍有 {len(violations)} 个外键约束违规:")
                for violation in violations:
                    print(f"   {list(violation)}")
                return False
            else:
                print("✅ 外键约束检查通过")
        
        # 检查患者分布
        patient_stats = db.execute_query("""
            SELECT 
                y.hname,
                COUNT(b.bianhao) as patient_count
            FROM yiyuan y
            LEFT JOIN bingren b ON y.id = b.yiyuanid
            GROUP BY y.id, y.hname
            ORDER BY y.id
        """)
        
        print("\n📊 患者分布统计:")
        print("-" * 40)
        print(f"{'医院名称':<20} {'患者数量':<10}")
        print("-" * 40)
        
        total_patients = 0
        for stat in patient_stats:
            print(f"{stat['hname']:<20} {stat['patient_count']:<10}")
            total_patients += stat['patient_count']
        
        print("-" * 40)
        print(f"{'总计':<20} {total_patients:<10}")
        
        # 检查是否还有孤立引用
        orphaned = db.execute_query("""
            SELECT COUNT(*) as count
            FROM bingren b
            LEFT JOIN yiyuan y ON b.yiyuanid = y.id
            WHERE y.id IS NULL AND b.yiyuanid IS NOT NULL
        """)
        
        if orphaned and orphaned[0]['count'] > 0:
            print(f"\n❌ 仍有 {orphaned[0]['count']} 个孤立引用")
            return False
        else:
            print(f"\n✅ 没有孤立引用")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证修复结果失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 修复测试患者记录工具")
    print("=" * 80)
    
    # 1. 修复测试患者记录
    if not fix_test_patients():
        print("❌ 修复测试患者记录失败，退出")
        return False
    
    # 2. 验证修复结果
    if not verify_fix():
        print("❌ 修复验证失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 测试患者记录修复完成！")
    print("✅ 测试患者记录已删除或更新")
    print("✅ 外键约束检查通过")
    print("✅ 数据库完整性恢复")
    print("✅ 治疗数据上传平台冲突问题彻底解决")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
