#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C2电刺激设备协议修复验证测试
根据C2说明书验证修复后的代码是否正确工作

🔧 主要修复内容：
1. 修正回调函数数据格式：从short数组改为字节数组，支持7字节格式
2. 修正电流设置流程：先切换到电流调节状态再设置电流
3. 修正刺激启动流程：确保设备状态正确切换
4. 添加详细的状态验证和诊断
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置详细日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('c2_protocol_test.log', encoding='utf-8')
        ]
    )

def test_c2_protocol_compliance():
    """测试C2协议合规性"""
    print("=" * 60)
    print("🔧 C2电刺激设备协议修复验证测试")
    print("=" * 60)

    # 创建设备实例（使用默认配置）
    device = StimulationDevice()

    try:
        # 步骤1：连接设备
        print("\n📡 步骤1: 连接设备...")
        if not device.connect(port_num=7):  # 使用端口7，根据原QT程序
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")

        # 步骤2：设置刺激参数
        print("\n⚙️ 步骤2: 设置刺激参数...")
        params = StimulationParameters(
            channel_num=1,      # A通道
            frequency=25.0,     # 25Hz
            pulse_width=250.0,  # 250μs
            relax_time=5.0,     # 5s
            climb_time=2.0,     # 2s
            work_time=10.0,     # 10s
            fall_time=2.0,      # 2s
            wave_type=0         # 单相波
        )

        if not device.set_stimulation_parameters(params):
            print("❌ 刺激参数设置失败")
            return False
        print("✅ 刺激参数设置成功")

        # 步骤3：按C2说明书流程设置电流
        print("\n⚡ 步骤3: 按C2说明书流程设置电流...")
        print("   说明书要求：先切换通道到电流调节状态，再设置电流")
        current_ma = 4.0  # 4mA
        if not device.set_current(1, current_ma):
            print("❌ 电流设置失败")
            return False
        print(f"✅ 电流设置成功: {current_ma}mA")

        # 等待观察回调数据
        print("\n📊 等待5秒观察回调数据格式...")
        for i in range(5):
            time.sleep(1)
            a_status = device.get_channel_status(1)
            b_status = device.get_channel_status(2)
            print(f"[{i+1}s] A通道状态: {a_status}, B通道状态: {b_status}")

        # 步骤4：按C2说明书流程触发刺激
        print("\n🚀 步骤4: 按C2说明书流程触发刺激...")
        print("   说明书要求：电流调节后，切换通道状态到正常工作状态")
        if not device.trigger_stimulation(1):
            print("❌ 刺激触发失败")
            return False
        print("✅ 刺激触发成功")

        # 步骤5：监控刺激状态
        print("\n⏱️ 监控刺激状态15秒...")
        stimulation_detected = False
        for i in range(15):
            time.sleep(1)
            a_status = device.get_channel_status(1)
            b_status = device.get_channel_status(2)
            device_status = device.get_status()

            status_text = ""
            if a_status == 3:  # 3 = 正常工作状态
                status_text = "✅ 正在刺激"
                stimulation_detected = True
            elif a_status == 2:  # 2 = 电流调节状态
                status_text = "⚠️ 电流调节状态"
            elif a_status == 1:  # 1 = 暂停状态
                status_text = "⏸️ 暂停状态"
            elif a_status == 0:  # 0 = 停止状态
                status_text = "❌ 停止状态"
            else:
                status_text = f"❓ 未知状态({a_status})"

            print(f"[{i+1:2d}s] 设备: {device_status.value}, A通道: {status_text}")

        # 步骤6：分析结果
        print("\n📊 结果分析:")
        if stimulation_detected:
            print("✅ 检测到刺激状态，回调函数正常工作")
        else:
            print("❌ 未检测到刺激状态，可能存在以下问题：")
            print("   1. 回调函数数据格式不正确")
            print("   2. 设备状态切换失败")
            print("   3. 电流设置流程不符合说明书要求")

        # 步骤7：停止刺激
        print("\n🛑 步骤7: 停止刺激...")
        if not device.stop_all_stimulation():
            print("❌ 停止刺激失败")
            return False
        print("✅ 刺激停止成功")

        return stimulation_detected

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        device.disconnect()
        print("✅ 资源清理完成")

def analyze_protocol_fixes():
    """分析协议修复内容"""
    print("\n" + "=" * 60)
    print("📋 C2协议修复内容分析")
    print("=" * 60)

    print("🔍 根据C2说明书发现的问题：")
    print("1. ❌ 回调函数数据格式错误：")
    print("   - 原代码：使用short数组，检查6字节")
    print("   - 说明书：应该是字节数组，7字节格式")
    print("   - 修复：改为字节数组，支持0x55AA+0x01帧头验证")

    print("\n2. ❌ 电流设置流程不符合说明书：")
    print("   - 原代码：直接调用CurrentSet")
    print("   - 说明书：先调用SwitchChannelState切换到电流调节状态")
    print("   - 修复：按说明书流程，先切换状态再设置电流")

    print("\n3. ❌ 刺激启动流程不完整：")
    print("   - 原代码：可能缺少状态验证")
    print("   - 说明书：电流调节后需切换到正常工作状态")
    print("   - 修复：添加完整的状态切换和验证")

    print("\n✅ 修复后的正确流程：")
    print("1. OpenRecPort - 打开串口")
    print("2. SwitchDeviceState(1) - 切换设备到循环刺激状态")
    print("3. StimPara - 设置刺激参数")
    print("4. SwitchChannelState(channel, 2) - 切换通道到电流调节状态")
    print("5. CurrentSet - 设置电流")
    print("6. SwitchChannelState(channel, 3) - 切换通道到正常工作状态")
    print("7. 回调函数接收7字节状态数据并验证")

def main():
    """主函数"""
    setup_logging()

    print("🔧 NK电刺激设备C2协议修复验证")
    print("解决'界面显示刺激中但实际无电流输出'问题")

    # 分析修复内容
    analyze_protocol_fixes()

    # 运行测试
    success = test_c2_protocol_compliance()

    print("\n" + "=" * 60)
    if success:
        print("✅ 协议修复验证成功！")
        print("🔍 关键改进：")
        print("   - 回调函数现在正确解析7字节数据格式")
        print("   - 电流设置严格按照C2说明书流程")
        print("   - 刺激启动包含完整的状态验证")
        print("📄 详细日志：c2_protocol_test.log")
    else:
        print("❌ 协议修复验证失败！")
        print("🔍 请检查：")
        print("   - 设备是否正确连接")
        print("   - DLL文件是否正确")
        print("   - 设备驱动是否正常")
        print("📄 错误日志：c2_protocol_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
