#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细时间分析测试 - 找出具体的耗时步骤
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

def test_detailed_timing():
    """详细时间分析"""
    print("🔍 详细时间分析测试")
    print("=" * 50)
    
    try:
        from core.stimulation_device import StimulationDevice
        
        device = StimulationDevice()
        
        print("\n📍 测试错误端口连接的详细时间分析")
        
        # 记录各个阶段的时间
        times = {}
        
        print("开始连接错误端口...")
        overall_start = time.time()
        
        result = device.connect(26)  # 错误端口
        
        overall_end = time.time()
        overall_time = overall_end - overall_start
        
        print(f"\n总耗时: {overall_time:.3f}秒")
        print(f"连接结果: {'成功' if result else '失败'}")
        
        # 分析日志输出，找出耗时步骤
        print("\n从日志可以看出各步骤的时间分布")
        
        return overall_time <= 8  # 期望在8秒内完成
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_detailed_timing()
    sys.exit(0 if success else 1)
