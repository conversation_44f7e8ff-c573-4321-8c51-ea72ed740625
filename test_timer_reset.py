#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预刺激定时器重置功能测试脚本

测试场景：
1. 连续调节电流时，定时器应该重置
2. 最后一次调节后3秒才停止预刺激
3. 验证定时器重置逻辑正确

作者: NK系统开发团队
日期: 2024-12-19
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device import StimulationDevice

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_timer_reset.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_timer_reset_logic():
    """测试定时器重置逻辑"""
    logger.info("=== 测试预刺激定时器重置逻辑 ===")

    device = StimulationDevice()

    # 测试连接
    port_num = 7  # 根据实际情况调整
    success = device.connect(port_num)

    if not success:
        logger.error("设备连接失败，跳过测试")
        return

    logger.info("✅ 设备连接成功")

    # 模拟连续快速调节电流的场景
    channel_num = 1
    adjustment_sequence = [
        (5, 0.5),   # 5mA，等待0.5秒
        (8, 0.8),   # 8mA，等待0.8秒
        (12, 1.0),  # 12mA，等待1.0秒
        (15, 0.6),  # 15mA，等待0.6秒
        (10, 0.4),  # 10mA，等待0.4秒
        # 最后一次调节后等待4秒，验证是否在3秒时停止
    ]

    logger.info("开始连续调节测试...")
    test_start_time = time.time()

    for i, (current_value, wait_time) in enumerate(adjustment_sequence):
        adjustment_time = time.time()
        logger.info(f"\n--- 第{i+1}次调节: {current_value}mA ---")

        # 启动预刺激（每次都应该重置定时器）
        success = device.start_pre_stimulation(channel_num, float(current_value), 3.0)

        if success:
            elapsed = time.time() - test_start_time
            logger.info(f"[{elapsed:.1f}s] 预刺激启动成功，定时器重置")
        else:
            logger.error(f"预刺激启动失败")

        # 等待指定时间后进行下一次调节
        logger.info(f"等待{wait_time}秒后进行下一次调节...")
        time.sleep(wait_time)

    # 最后一次调节完成，现在监控预刺激是否在3秒后停止
    last_adjustment_time = time.time()
    logger.info(f"\n=== 最后一次调节完成，监控预刺激状态 ===")
    logger.info(f"预期：应该在3秒后（即{time.strftime('%H:%M:%S', time.localtime(last_adjustment_time + 3))}）停止预刺激")

    # 监控4秒，看预刺激是否在第3秒停止
    for i in range(8):  # 每0.5秒检查一次，共4秒
        time.sleep(0.5)
        elapsed_since_last = time.time() - last_adjustment_time
        status = device.get_channel_status(channel_num)
        status_text = "刺激中" if status == 1 else "暂停"

        logger.info(f"[+{elapsed_since_last:.1f}s] 通道状态: {status_text}")

        # 检查关键时间点
        if 2.8 <= elapsed_since_last <= 3.2:  # 3秒左右
            if status != 1:
                logger.info("✅ 预刺激在预期时间停止（3秒后）")
            else:
                logger.warning("⚠️ 预刺激应该已经停止但仍在继续")
        elif elapsed_since_last > 3.5 and status == 1:
            logger.error("❌ 预刺激超时未停止")

    # 断开设备
    device.disconnect()
    logger.info("测试完成")

def test_rapid_adjustments():
    """测试快速连续调节 - 验证新的智能预刺激逻辑"""
    logger.info("\n=== 测试快速连续调节（验证智能预刺激逻辑） ===")

    device = StimulationDevice()

    if not device.connect(7):
        logger.error("设备连接失败")
        return

    channel_num = 1
    rapid_sequence = [3, 5, 8, 12, 15, 18, 20, 15, 10, 5]

    logger.info("开始快速连续调节（每0.3秒一次）...")
    logger.info("新逻辑：首次启动预刺激，后续只更新电流值")
    start_time = time.time()

    for i, current_value in enumerate(rapid_sequence):
        adjustment_time = time.time()
        elapsed = adjustment_time - start_time

        logger.info(f"[{elapsed:.1f}s] 第{i+1}次快速调节: {current_value}mA")

        if i == 0:
            # 首次调节：启动预刺激
            logger.info("  → 首次调节，启动预刺激")
            device.start_pre_stimulation(channel_num, float(current_value), 3.0)
        else:
            # 后续调节：只更新电流
            logger.info("  → 后续调节，只更新电流")
            device.set_current(channel_num, current_value)

        time.sleep(0.3)  # 快速调节间隔

    # 最后一次调节后的时间
    final_adjustment_time = time.time()
    logger.info(f"\n快速调节序列完成，最后调节时间: {time.strftime('%H:%M:%S', time.localtime(final_adjustment_time))}")
    logger.info("预期：由于只有首次启动了预刺激，应该在首次启动后3秒停止")
    logger.info("（而不是最后一次调节后3秒）")

    # 监控预刺激状态
    for i in range(10):
        time.sleep(0.5)
        elapsed_from_start = time.time() - start_time
        elapsed_from_final = time.time() - final_adjustment_time
        status = device.get_channel_status(channel_num)
        status_text = "刺激中" if status == 1 else "暂停"

        logger.info(f"[总时长+{elapsed_from_start:.1f}s, 距最后调节+{elapsed_from_final:.1f}s] 状态: {status_text}")

        # 检查是否在首次启动后3秒左右停止
        if 2.8 <= elapsed_from_start <= 3.5 and status != 1:
            logger.info("✅ 预刺激在首次启动后3秒停止（符合设备内置定时器逻辑）")
            break

    device.disconnect()

def test_ui_timer_logic():
    """测试UI定时器逻辑（模拟治疗界面的定时器重置）"""
    logger.info("\n=== 测试UI定时器逻辑 ===")

    device = StimulationDevice()

    if not device.connect(7):
        logger.error("设备连接失败")
        return

    channel_num = 1

    # 模拟UI定时器逻辑
    ui_timer_start = None
    ui_timer_duration = 3.0  # 3秒

    adjustment_sequence = [
        (5, 0.5),   # 5mA，等待0.5秒
        (8, 0.8),   # 8mA，等待0.8秒
        (12, 1.0),  # 12mA，等待1.0秒
        (15, 0.6),  # 15mA，等待0.6秒
        (10, 0.4),  # 10mA，等待0.4秒
    ]

    logger.info("模拟UI定时器重置逻辑...")

    for i, (current_value, wait_time) in enumerate(adjustment_sequence):
        adjustment_time = time.time()

        if i == 0:
            # 首次调节：启动设备预刺激和UI定时器
            logger.info(f"第{i+1}次调节: {current_value}mA - 启动设备预刺激和UI定时器")
            device.start_pre_stimulation(channel_num, float(current_value), 3.0)
            ui_timer_start = adjustment_time
        else:
            # 后续调节：只更新电流和重置UI定时器
            logger.info(f"第{i+1}次调节: {current_value}mA - 更新电流，重置UI定时器")
            device.set_current(channel_num, current_value)
            ui_timer_start = adjustment_time  # 重置UI定时器

        logger.info(f"  UI定时器重置，将在{ui_timer_duration}秒后触发停止")

        time.sleep(wait_time)

    # 监控UI定时器逻辑
    logger.info(f"\n所有调节完成，UI定时器应该在最后一次调节后{ui_timer_duration}秒触发停止")

    for i in range(8):
        time.sleep(0.5)
        current_time = time.time()
        ui_elapsed = current_time - ui_timer_start
        status = device.get_channel_status(channel_num)
        status_text = "刺激中" if status == 1 else "暂停"

        logger.info(f"[UI定时器+{ui_elapsed:.1f}s] 状态: {status_text}")

        # 模拟UI定时器触发
        if ui_elapsed >= ui_timer_duration:
            logger.info("🔔 UI定时器触发，应该停止预刺激")
            device.stop_stimulation(channel_num)
            logger.info("✅ UI定时器逻辑测试完成")
            break

    device.disconnect()

def main():
    """主测试函数"""
    logger.info("开始预刺激定时器重置功能测试")

    try:
        # 1. 测试定时器重置逻辑
        test_timer_reset_logic()

        # 2. 测试快速连续调节（验证智能预刺激逻辑）
        test_rapid_adjustments()

        # 3. 测试UI定时器逻辑
        test_ui_timer_logic()

        logger.info("\n所有测试完成")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
