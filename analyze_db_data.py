#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库原始数据分析工具
Database Raw Data Analysis Tool

通过数据库记录分析患者的原始脑电数据

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.eeg_data_loader import EEGDataLoader

def analyze_patient_data_from_db(patient_id: int):
    """从数据库分析患者数据"""
    print(f"🔍 分析患者 {patient_id} 的原始脑电数据")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return
        
        # 初始化数据加载器
        loader = EEGDataLoader(db_manager)
        
        # 1. 获取患者基本信息
        print("👤 患者基本信息:")
        patient_sql = "SELECT * FRO<PERSON> bingren WHERE bianhao = ?"
        patient_info = db_manager.execute_query(patient_sql, [patient_id])
        
        if not patient_info:
            print(f"❌ 未找到患者 {patient_id} 的信息")
            return
        
        patient = patient_info[0]
        print(f"  患者编号: {patient.get('bianhao', 'N/A')}")
        print(f"  患者姓名: {patient.get('name', 'N/A')}")
        print(f"  性别: {patient.get('xingbie', 'N/A')}")
        print(f"  年龄: {patient.get('age', 'N/A')}")
        print(f"  科室: {patient.get('keshi', 'N/A')}")
        print(f"  操作员: {patient.get('czy', 'N/A')}")
        
        # 2. 获取会话信息
        print(f"\n📊 会话信息:")
        sessions_sql = """
            SELECT * FROM eeg_sessions 
            WHERE patient_id = ? 
            ORDER BY start_time DESC
        """
        sessions = db_manager.execute_query(sessions_sql, [patient_id])
        
        if not sessions:
            print(f"❌ 患者 {patient_id} 没有脑电会话记录")
            return
        
        print(f"  会话总数: {len(sessions)}")
        
        total_trials = 0
        total_successful = 0
        
        for i, session in enumerate(sessions, 1):
            session_id = session.get('session_id', session.get('id', 'N/A'))
            session_type = session.get('session_type', 'N/A')
            start_time = session.get('start_time', 'N/A')
            end_time = session.get('end_time', 'N/A')
            trials = session.get('total_trials', 0)
            successful = session.get('successful_trials', 0)
            data_dir = session.get('data_directory', 'N/A')
            
            total_trials += trials
            total_successful += successful
            
            print(f"\n  会话 {i} (ID: {session_id}):")
            print(f"    类型: {session_type}")
            print(f"    开始时间: {start_time}")
            print(f"    结束时间: {end_time}")
            print(f"    试验数: {trials}")
            print(f"    成功试验: {successful}")
            print(f"    数据目录: {data_dir}")
        
        # 3. 获取试验详细信息
        print(f"\n🧠 试验详细信息:")
        
        # 使用新的表名查询
        trials_sql = """
            SELECT * FROM eeg_trials
            WHERE session_id IN (
                SELECT session_id FROM eeg_sessions WHERE patient_id = ?
            )
            ORDER BY session_id, trial_number
        """
        trials = db_manager.execute_query(trials_sql, [patient_id])
        
        if not trials:
            # 尝试旧的表名
            trials_sql = """
                SELECT * FROM eeg_raw_data 
                WHERE patient_id = ?
                ORDER BY session_id, trial_id
            """
            trials = db_manager.execute_query(trials_sql, [patient_id])
        
        if trials:
            print(f"  试验记录总数: {len(trials)}")
            
            # 统计分析
            motor_imagery_count = 0
            rest_count = 0
            quality_scores = []
            durations = []
            file_sizes = []
            
            print(f"\n  试验详情:")
            for i, trial in enumerate(trials, 1):
                trial_id = trial.get('trial_id', trial.get('id', 'N/A'))
                session_id = trial.get('session_id', 'N/A')
                label = trial.get('label', 'N/A')
                start_time = trial.get('start_time', 'N/A')
                duration = trial.get('duration_seconds', 0) or 0
                quality = trial.get('data_quality', 0) or 0
                file_path = trial.get('file_path', 'N/A')
                file_size = trial.get('file_size_bytes', 0) or 0
                
                # 统计标签
                if label == 1:
                    motor_imagery_count += 1
                    label_text = "运动想象"
                elif label == 0:
                    rest_count += 1
                    label_text = "休息"
                else:
                    label_text = f"未知({label})"
                
                quality_scores.append(quality)
                durations.append(duration)
                file_sizes.append(file_size)
                
                print(f"    试验 {i}:")
                print(f"      ID: {trial_id}, 会话: {session_id}")
                print(f"      标签: {label_text}")
                print(f"      时长: {duration:.2f} 秒")
                print(f"      质量: {quality:.3f}")
                print(f"      文件大小: {file_size / 1024:.2f} KB")
                print(f"      文件路径: {file_path}")
                print(f"      开始时间: {start_time}")
            
            # 4. 数据统计分析
            print(f"\n📈 数据统计分析:")
            print(f"运动想象试验: {motor_imagery_count} 个")
            print(f"休息状态试验: {rest_count} 个")
            
            if motor_imagery_count + rest_count > 0:
                balance_ratio = motor_imagery_count / (rest_count + 1e-6)
                print(f"标签平衡比: {balance_ratio:.2f}")
            
            if durations:
                total_duration = sum(durations)
                avg_duration = total_duration / len(durations)
                print(f"总时长: {total_duration:.2f} 秒 ({total_duration/60:.1f} 分钟)")
                print(f"平均时长: {avg_duration:.2f} 秒/试验")
            
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                min_quality = min(quality_scores)
                max_quality = max(quality_scores)
                print(f"平均质量: {avg_quality:.3f}")
                print(f"质量范围: {min_quality:.3f} - {max_quality:.3f}")
            
            if file_sizes:
                total_size = sum(file_sizes)
                avg_size = total_size / len(file_sizes)
                print(f"总数据大小: {total_size / 1024:.2f} KB ({total_size / 1024 / 1024:.2f} MB)")
                print(f"平均文件大小: {avg_size / 1024:.2f} KB")
            
            # 5. 数据质量评估
            print(f"\n✅ 数据质量评估:")
            
            # 数据量评估
            trial_count = len(trials)
            if trial_count >= 20:
                print("✅ 数据量: 充足 (≥20个试验)")
            elif trial_count >= 10:
                print("⚠️ 数据量: 基本够用 (10-19个试验)")
            elif trial_count >= 5:
                print("⚠️ 数据量: 较少 (5-9个试验)")
            else:
                print("❌ 数据量: 不足 (<5个试验)")
            
            # 标签平衡性评估
            if abs(motor_imagery_count - rest_count) <= 2:
                print("✅ 标签平衡性: 良好")
            elif abs(motor_imagery_count - rest_count) <= 5:
                print("⚠️ 标签平衡性: 轻微不平衡")
            else:
                print("❌ 标签平衡性: 严重不平衡")
            
            # 数据质量评估
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                if avg_quality >= 0.8:
                    print("✅ 数据质量: 优秀 (≥0.8)")
                elif avg_quality >= 0.6:
                    print("⚠️ 数据质量: 良好 (0.6-0.8)")
                elif avg_quality >= 0.4:
                    print("⚠️ 数据质量: 一般 (0.4-0.6)")
                else:
                    print("❌ 数据质量: 较差 (<0.4)")
            
            # 时长评估
            if durations:
                avg_duration = sum(durations) / len(durations)
                if avg_duration >= 3.0:
                    print("✅ 试验时长: 充足 (≥3秒)")
                elif avg_duration >= 2.0:
                    print("⚠️ 试验时长: 基本够用 (2-3秒)")
                else:
                    print("❌ 试验时长: 过短 (<2秒)")
            
            # 6. 文件状态检查
            print(f"\n📁 文件状态检查:")
            missing_files = 0
            accessible_files = 0
            
            for trial in trials:
                file_path = trial.get('file_path', '')
                if file_path:
                    path_obj = Path(file_path)
                    if path_obj.exists():
                        accessible_files += 1
                        print(f"✅ {path_obj.name}")
                    else:
                        missing_files += 1
                        print(f"❌ {path_obj.name} (文件不存在)")
                else:
                    missing_files += 1
                    print(f"❌ 无文件路径记录")
            
            print(f"\n文件状态总结:")
            print(f"  可访问文件: {accessible_files}")
            print(f"  缺失文件: {missing_files}")
            
            if missing_files == 0:
                print("✅ 所有数据文件完整")
            else:
                print(f"⚠️ 有 {missing_files} 个文件缺失或无法访问")
        
        else:
            print(f"❌ 患者 {patient_id} 没有试验记录")
        
        # 7. 最终建议
        print(f"\n🎯 最终建议:")
        
        if trials and len(trials) >= 10:
            if quality_scores and sum(quality_scores) / len(quality_scores) >= 0.6:
                if abs(motor_imagery_count - rest_count) <= 3:
                    print("🎉 数据状态优秀，可以用于模型训练和分析")
                else:
                    print("⚠️ 数据质量良好，但标签不平衡，建议补充数据")
            else:
                print("⚠️ 数据量充足，但质量需要改善")
        else:
            print("❌ 数据量不足，建议继续收集更多训练数据")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 分析指定患者的数据
    patient_id = **********  # 从目录名提取的患者ID
    analyze_patient_data_from_db(patient_id)

if __name__ == "__main__":
    main()
