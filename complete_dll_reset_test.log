2025-06-03 09:19:41,219 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:19:41,224 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:41,226 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:41,235 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:19:41,243 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:19:41,278 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:41,295 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:41,405 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:41,511 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:41,614 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:41,715 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:42,819 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:42,821 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:42,822 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:42,822 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:19:42,823 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:19:42,825 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:19:42,826 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:42,827 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:42,828 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:42,829 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:42,930 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:43,032 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:43,133 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:43,234 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:44,340 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:44,340 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:44,341 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:44,341 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:19:44,342 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:19:44,342 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:44,342 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:44,443 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:44,545 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:44,646 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:44,748 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:45,854 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:45,855 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:45,856 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:45,857 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:19:45,858 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:19:45,859 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:19:45,860 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:19:45,861 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:45,861 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:45,862 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:19:45,862 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:19:45,863 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:19:46,363 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:19:46,363 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:19:46,364 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:46,364 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:46,466 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:46,567 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:46,668 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:46,770 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:47,877 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:47,878 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:47,879 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:49,882 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:19:50,602 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:19:50,725 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:50,726 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,727 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,741 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,742 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,757 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,758 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,759 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,759 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,773 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,773 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:19:50,773 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,961 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:19:50,963 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:19:50,963 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:19:50,963 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:19:50,977 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,977 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,978 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:50,993 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,994 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:50,994 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:50,994 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,009 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,010 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,025 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,025 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,026 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,027 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,040 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,040 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,055 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:51,055 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,056 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,071 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,072 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,072 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,073 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,086 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,086 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,101 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,101 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,102 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,103 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,132 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:51,148 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,148 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,149 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:51,149 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:51,286 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:19:51,287 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:51,288 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:51,389 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:51,490 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:51,591 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:51,692 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:52,799 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:52,799 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:52,799 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:52,800 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:19:52,800 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:19:53,802 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:19:53,803 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:19:53,803 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:19:53,804 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:19:54,306 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:19:54,307 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:19:54,308 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:54,308 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:54,410 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:54,511 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:54,613 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:54,713 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:19:55,816 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:19:55,817 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:19:55,818 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:19:57,821 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:19:58,537 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:19:58,661 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:58,661 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,662 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,676 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,677 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,678 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,679 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,692 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,693 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,707 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:19:58,896 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:19:58,897 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:19:58,897 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:19:58,897 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:19:58,912 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,912 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,912 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:58,927 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,927 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,927 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,928 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,943 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,943 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,943 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,944 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,959 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,959 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:58,974 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:58,974 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,005 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:19:59,005 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,006 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,007 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,007 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,007 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,008 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,036 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,036 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,037 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,037 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,037 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,038 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,068 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,068 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,068 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,069 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,069 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,070 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,099 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,099 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,099 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:19:59,100 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:19:59,223 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:19:59,223 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:19:59,223 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:19:59,324 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:19:59,425 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:19:59,527 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:19:59,629 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:00,735 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:00,736 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:00,736 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:00,737 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:00,738 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:00,738 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:00,739 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:00,740 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:00,741 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:00,841 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:00,942 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:01,044 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:01,145 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:02,249 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:02,249 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:02,250 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:02,250 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:02,250 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:02,251 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:20:02,251 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:20:02,252 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:02,252 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:02,252 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:20:02,252 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:20:02,252 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:20:02,754 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:20:02,755 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:20:02,756 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:02,756 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:02,857 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:02,958 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:03,060 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:03,162 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:04,264 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:04,265 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:04,266 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:04,768 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:20:05,489 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:20:05,613 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:05,614 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,614 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,615 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,616 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,645 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,646 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,647 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,647 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,648 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,649 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,649 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,650 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,660 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:20:05,848 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:20:05,848 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:20:05,849 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:05,849 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:20:05,865 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,865 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,865 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:05,880 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,880 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,881 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,881 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,896 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,896 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,911 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,911 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,912 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,912 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,927 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,927 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,942 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,943 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,943 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:05,957 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,957 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,958 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,959 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,973 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,974 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,988 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,988 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:05,989 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:05,989 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:06,019 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:06,019 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:06,035 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:06,035 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:06,036 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:06,036 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:06,051 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:06,051 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:06,175 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:06,176 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:06,177 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:06,279 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:06,381 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:06,483 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:06,585 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:07,691 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:07,692 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:07,693 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:07,694 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:07,695 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:08,196 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:20:08,197 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:20:08,198 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:20:08,199 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:20:08,701 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:20:08,701 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:20:08,701 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:08,701 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:08,802 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:08,903 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:09,004 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:09,104 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:10,207 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:10,208 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:10,209 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:10,710 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:20:11,435 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:20:11,543 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:11,559 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,559 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,559 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,560 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,589 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,590 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,605 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:20:11,605 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,605 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,794 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:20:11,794 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:20:11,796 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:11,796 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:20:11,810 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,811 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,811 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:11,826 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,826 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,827 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,827 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,842 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,843 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,843 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,844 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,858 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,859 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,889 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,889 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,890 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:11,905 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,905 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,905 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,906 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,921 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,921 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,921 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,922 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,936 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,936 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,952 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,952 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,967 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,967 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,983 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,983 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,983 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,983 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,999 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:11,999 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:11,999 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:12,000 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:12,108 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:12,108 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:12,109 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:12,210 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:12,312 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:12,413 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:12,515 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:13,621 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:13,622 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:13,622 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:13,623 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:13,623 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:14,126 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:20:14,127 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:20:14,128 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:20:14,129 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:20:14,630 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:20:14,630 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:20:14,631 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:14,632 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:14,734 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:14,835 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:14,936 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:15,037 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:16,144 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:16,144 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:16,144 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:16,646 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:20:17,351 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:20:17,474 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:17,474 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,475 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,489 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,490 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,505 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,505 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,505 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,506 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,521 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:20:17,710 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:20:17,710 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:20:17,710 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:17,712 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:20:17,726 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,726 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,727 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:17,741 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,741 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,742 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,742 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,756 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,756 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,772 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,772 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,773 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,773 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,803 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:17,803 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,803 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,819 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,819 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,820 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,820 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,834 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,834 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,850 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,850 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,851 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,852 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,881 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,882 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,897 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,897 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,898 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,898 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:17,913 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:17,913 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:18,038 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:18,039 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:18,040 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:18,141 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:18,243 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:18,344 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:18,446 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:19,552 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:19,553 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:19,554 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:19,555 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:19,555 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:20,057 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:20:20,059 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:20:20,059 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:20:20,060 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:20:20,562 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:20:20,563 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:20:20,564 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:20,564 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:20,666 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:20,767 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:20,868 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:20,969 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:22,074 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:22,075 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:22,076 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:22,578 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:20:23,286 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:20:23,425 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:23,426 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,427 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,427 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,428 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,429 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,430 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,456 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,456 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:20:23,456 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,644 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:20:23,645 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:20:23,645 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:23,646 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:20:23,660 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,660 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,660 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:23,676 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,676 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,677 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,677 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,692 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,692 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,693 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,693 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,707 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,707 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,739 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,739 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,740 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:23,753 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,754 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,755 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,755 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,769 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,770 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,784 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,784 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,785 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,785 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,814 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,814 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,829 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,830 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,830 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,831 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,845 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:23,845 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:23,968 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:23,969 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:23,970 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:24,071 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:24,172 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:24,273 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:24,373 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:25,480 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:25,480 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:25,480 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:25,480 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:25,480 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:25,981 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:20:25,981 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:20:25,982 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:20:25,982 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 09:20:26,484 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 09:20:26,484 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:20:26,484 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:26,484 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:26,585 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:26,686 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:26,787 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:26,888 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:27,993 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:27,994 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:27,995 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:28,497 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:20:29,208 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:20:29,332 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:29,332 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,334 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,348 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,349 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,349 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,350 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,363 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,363 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,364 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,364 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,379 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:20:29,564 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:20:29,565 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:20:29,565 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:29,566 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:20:29,580 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,580 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,581 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:29,595 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,595 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,596 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,596 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,610 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,610 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,626 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,627 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,627 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,628 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,658 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,659 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,659 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:20:29,673 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,673 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,674 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,675 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,689 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,690 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,705 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,705 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,706 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,706 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,720 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,720 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,736 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,736 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,751 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,751 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,751 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,752 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,766 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:20:29,766 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:20:29,891 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:29,891 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:29,892 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:29,994 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:30,095 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:30,197 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:30,298 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:31,403 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:31,404 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:31,405 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:31,405 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:31,406 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:20:31,908 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:20:31,908 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:20:31,908 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 09:20:31,908 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:20:32,010 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 09:20:32,111 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 09:20:32,213 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 09:20:32,315 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 09:20:33,421 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:20:33,422 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:20:33,422 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 09:20:33,422 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:20:33,422 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:39:47,804 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 11:39:47,805 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:47,806 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:47,806 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:39:47,806 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:39:47,806 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:39:47,807 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:39:47,909 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:39:48,009 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:39:48,110 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:39:48,211 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:39:49,314 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:49,314 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:49,314 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:39:49,314 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:39:49,314 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:39:49,315 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 11:39:49,315 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:49,316 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:49,317 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:39:49,317 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:39:49,418 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:39:49,519 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:39:49,621 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:39:49,722 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:39:50,824 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:50,824 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:50,825 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:39:50,825 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:39:50,825 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:39:50,825 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:39:50,826 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:39:50,927 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:39:51,028 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:39:51,129 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:39:51,229 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:39:52,332 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:52,332 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:52,333 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:39:52,333 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:39:52,333 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:39:52,334 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 11:39:52,334 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:39:52,335 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:52,335 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:52,336 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:39:52,336 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:39:52,336 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:39:52,837 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:39:52,837 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:39:52,837 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:39:52,837 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:39:52,938 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:39:53,039 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:39:53,140 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:39:53,241 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:39:54,344 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:54,344 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:54,345 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:39:56,346 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:39:57,057 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:39:57,182 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:39:57,183 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,184 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,197 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,197 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,213 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,213 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,214 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,214 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,228 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:39:57,418 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:39:57,419 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:39:57,420 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:39:57,420 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:39:57,434 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:39:57,434 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:39:57,450 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,450 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,450 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,450 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,465 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,465 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,480 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,480 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,481 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,481 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,512 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,512 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,512 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:39:57,512 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,513 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,527 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,527 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,527 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,528 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,543 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,543 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,558 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,558 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,559 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,559 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,589 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,589 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,590 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,591 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,605 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,605 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,621 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,622 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,622 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:39:57,622 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:39:57,745 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:39:57,745 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:39:57,745 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:39:57,846 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:39:57,947 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:39:58,048 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:39:58,149 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:39:59,251 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:39:59,252 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:39:59,252 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:39:59,252 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:39:59,253 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:00,254 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:00,254 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:00,255 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:00,256 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:00,757 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:00,757 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:00,758 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:00,759 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:00,861 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:00,961 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:01,063 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:01,164 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:02,269 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:02,269 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:02,270 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:04,271 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:04,980 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:05,089 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:05,104 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,105 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,105 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,106 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,120 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,120 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,121 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,122 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,151 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:05,341 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:05,341 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:05,342 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:05,342 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:05,356 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,356 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,372 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:05,372 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,372 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,372 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,373 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,387 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,387 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,403 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,403 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,403 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,403 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,418 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,418 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,433 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,434 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,434 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:05,449 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,449 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,449 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,450 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,465 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,465 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,481 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,481 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,482 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,482 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,513 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,513 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,528 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,528 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,529 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,529 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,544 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:05,544 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:05,653 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:05,654 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:05,655 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:05,757 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:05,858 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:05,959 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:06,060 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:07,165 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:07,167 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:07,168 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:07,169 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:07,169 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:07,170 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:07,170 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:07,171 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:07,171 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:07,273 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:07,375 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:07,476 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:07,577 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:08,682 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:08,682 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:08,682 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:08,684 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:08,684 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:08,685 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 11:40:08,685 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:08,686 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:08,686 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:08,686 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:08,686 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:08,687 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:09,187 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:09,187 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:09,188 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:09,189 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:09,290 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:09,391 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:09,492 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:09,593 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:10,696 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:10,696 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:10,697 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:11,199 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:11,912 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:12,035 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:12,035 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,035 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,050 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,050 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,050 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,050 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,066 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,066 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,066 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,067 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,082 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:12,082 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,082 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,273 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:12,273 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:12,274 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:12,274 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:12,288 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,288 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,289 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:12,289 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,290 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,304 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,304 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,320 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,321 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,321 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,322 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,335 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,335 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,336 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,337 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,365 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,365 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,366 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:12,367 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,367 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,367 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,368 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,397 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,397 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,398 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,399 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,399 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,399 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,443 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:12,459 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,459 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,460 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,460 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,474 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:12,474 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:12,599 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:12,600 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:12,600 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:12,702 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:12,803 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:12,904 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:13,006 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:14,110 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:14,111 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:14,111 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:14,112 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:14,112 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:14,614 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:14,615 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:14,615 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:14,616 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:15,117 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:15,117 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:15,117 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:15,118 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:15,219 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:15,319 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:15,421 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:15,522 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:16,628 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:16,629 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:16,630 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:17,131 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:17,847 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:17,972 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:17,973 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:17,973 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:17,973 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:17,974 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:17,988 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:17,988 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,004 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,005 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,006 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,006 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,019 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:18,210 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:18,211 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:18,211 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:18,212 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:18,226 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,227 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,227 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:18,227 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,228 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,241 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,241 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,241 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,242 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,257 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,258 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,272 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,273 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,274 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,274 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,303 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,303 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,304 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:18,305 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,305 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,319 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,320 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,334 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,335 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,335 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,336 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,350 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,350 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,351 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,352 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,381 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,381 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,382 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,382 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,396 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,397 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,411 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,411 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,411 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:18,411 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:18,536 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:18,537 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:18,538 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:18,638 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:18,740 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:18,841 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:18,942 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:20,046 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:20,047 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:20,047 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:20,048 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:20,049 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:20,550 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:20,551 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:20,552 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:20,553 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:21,055 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:21,056 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:21,057 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:21,058 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:21,159 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:21,260 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:21,362 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:21,463 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:22,569 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:22,570 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:22,571 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:23,073 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:23,792 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:23,916 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:23,932 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:23,933 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:23,933 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:23,934 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:23,948 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:23,949 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:23,949 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:23,950 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:23,963 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:24,152 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:24,153 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:24,153 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:24,154 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:24,168 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,168 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,169 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,169 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,170 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:24,184 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,184 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,199 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,200 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,200 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,201 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,215 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,216 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,216 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,217 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,246 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,246 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,247 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:24,247 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,247 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,262 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,262 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,278 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,278 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,278 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,278 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,293 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,293 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,294 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,294 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,324 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,325 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,325 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,326 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,340 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,340 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,355 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,356 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,356 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:24,357 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:24,480 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:24,481 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:24,481 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:24,582 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:24,684 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:24,785 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:24,887 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:25,993 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:25,994 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:25,995 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:25,995 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:25,996 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:26,497 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:26,498 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:26,499 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:26,500 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:27,001 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:27,001 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:27,001 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:27,001 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:27,103 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:27,204 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:27,304 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:27,407 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:28,510 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:28,510 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:28,510 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:29,012 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:29,730 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:29,853 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:29,854 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,855 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:29,868 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,869 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:29,870 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,871 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:29,884 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,885 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:29,885 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,886 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:29,899 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:29,899 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:29,899 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,089 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:30,090 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:30,090 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:30,091 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:30,104 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,104 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,105 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:30,106 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,106 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,120 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,120 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,135 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,135 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,135 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,136 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,151 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,151 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,152 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,153 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,166 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,166 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,182 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,182 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,183 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:30,184 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,184 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,212 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,212 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,213 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,214 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,214 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,214 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,259 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,259 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,274 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,275 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,276 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,276 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,277 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:30,277 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:30,413 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:30,414 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:30,415 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:30,515 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:30,617 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:30,719 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:30,820 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:31,927 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:31,928 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:31,928 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:31,928 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:31,928 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:32,429 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 11:40:32,430 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 11:40:32,431 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 11:40:32,431 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 11:40:32,933 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 11:40:32,933 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 11:40:32,933 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:32,933 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:33,035 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:33,136 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:33,236 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:33,338 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:34,441 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:34,442 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:34,443 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:34,945 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 11:40:35,666 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 11:40:35,791 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:35,807 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:35,807 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:35,807 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:35,808 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:35,822 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:35,822 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:35,823 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:35,823 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:35,838 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:35,838 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 11:40:35,838 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,028 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 11:40:36,029 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 11:40:36,029 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:36,030 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 11:40:36,043 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,043 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,044 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,044 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,044 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:36,059 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,059 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,074 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,074 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,075 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,075 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,090 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,090 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,091 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,091 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,121 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,122 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,122 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 11:40:36,123 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,123 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,137 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,137 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,153 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,153 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,153 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,154 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,168 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,168 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,169 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,169 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,198 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,198 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,198 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,199 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,214 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,214 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,229 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,229 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,229 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 11:40:36,229 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 11:40:36,354 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:36,355 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:36,356 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:36,458 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:36,560 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:36,661 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:36,762 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:37,866 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:37,867 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:37,867 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:37,868 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:37,869 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 11:40:38,370 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 11:40:38,371 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 11:40:38,371 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 11:40:38,372 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 11:40:38,474 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 11:40:38,575 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 11:40:38,677 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 11:40:38,778 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 11:40:39,884 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 11:40:39,885 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 11:40:39,886 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 11:40:39,887 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 11:40:39,888 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
