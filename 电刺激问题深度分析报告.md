# 电刺激问题深度分析报告

## 问题现状

根据用户反馈和测试结果，电刺激设备存在以下关键问题：

1. **连接失败**：所有端口（1, 7, 2, 3）连接都失败，错误码为-1和0
2. **刺激无输出**：即使显示"刺激中"，实际没有刺激输出
3. **协议实现疑问**：需要对照C2-定制客户dll使用说明书检查实现

## 🔍 深度技术分析

### 1. OpenRecPort返回值分析

从测试日志看到的错误码：
```
错误码: -1  # 端口1, 7, 2
错误码: 0   # 端口3
```

**关键发现**：
- 错误码-1：通常表示设备未找到或端口无法打开
- 错误码0：根据头文件注释，0表示"命令执行正确"，但在OpenRecPort中可能有不同含义

**问题分析**：
1. **设备物理连接**：设备可能未正确连接到计算机
2. **端口占用**：串口可能被其他程序占用
3. **驱动问题**：设备驱动未正确安装或版本不匹配
4. **DLL版本**：RecoveryDLL.dll版本可能与设备不匹配

### 2. 协议实现对比分析

根据头文件`RecoveryModuleDLL.h`和电刺激问题修复报告：

#### OpenRecPort函数签名
```c
extern "C" __declspec(dllexport) int OpenRecPort(int portNum,int iReadSize,funDataProc fun,HANDLE pHandle);
```

**当前Python实现**：
```python
result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)
```

**对比原QT程序**：
```cpp
openf = OpenRecPort(portNum, 6, callf, pHandle);
if (1 == openf) {  // 1表示成功
```

**实现正确性检查**：
- ✅ 端口号：正确传递
- ✅ 缓冲区大小：6（与原QT程序一致）
- ✅ 回调函数：已正确实现ctypes.WINFUNCTYPE
- ✅ 句柄：传递None（与原QT程序的pHandle一致）
- ✅ 返回值判断：检查result == 1

### 3. 刺激输出问题分析

根据用户描述"开启刺激后显示刺激中实际并未有刺激输出"：

#### 可能原因1：参数设置问题
```c
extern "C" __declspec(dllexport) int StimPara(int ChanNum, double ActFreq, double PulseWidth,
                                              double RelaxTime, double ClimbTime, double WorkTime,
                                              double FallTime, int WaveType);
```

**检查点**：
- 频率范围：是否在设备支持范围内
- 脉宽设置：是否符合安全标准
- 时间参数：各时间段设置是否合理

#### 可能原因2：电流设置问题
```c
extern "C" __declspec(dllexport) int CurrentSet(int nChanNum, int nValue);
```

**检查点**：
- 电流值单位：可能是0.1mA为单位（需要确认）
- 电流范围：是否在设备安全范围内
- 设置时机：是否在正确的时机设置电流

#### 可能原因3：状态切换顺序
根据头文件注释，正确的启动顺序应该是：
1. `OpenRecPort` - 连接设备
2. `SwitchDeviceState(1)` - 切换到循环刺激状态
3. `StimPara` - 设置刺激参数
4. `CurrentSet` - 设置电流强度
5. `SwitchChannelState(channel, 3)` - 启动通道（3=正常工作）

### 4. 回调函数数据分析

```c
typedef int (__stdcall *funDataProc)(HANDLE pHandle ,short* lpBuffer, int nSize);
// 该回调函数返回-1时，停止读取数据发送数据;
```

**当前实现检查**：
```python
def _data_callback(self, pHandle, lpBuffer, nSize):
    if nSize == 6 and lpBuffer:
        buffer_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents
        # 更新A通道状态 (索引2)
        # 更新B通道状态 (索引4)
    return nSize
```

**潜在问题**：
- 缓冲区数据解析可能不正确
- 状态更新逻辑可能有误
- 返回值处理可能影响设备通信

## 🔧 问题修复方案

### 立即修复项

#### 1. 增强设备连接诊断
需要实现全面的连接诊断功能，检查DLL加载、端口可用性、设备检测等。

#### 2. 修正电流设置单位
电流值可能需要转换为设备内部单位（可能是0.1mA为单位）。

#### 3. 完善刺激启动流程
实现验证式的刺激启动流程，确保每个步骤都正确执行。

#### 4. 增强错误处理和日志
提供详细的错误信息和解决建议。

## 🎯 下一步行动计划

### 立即执行
1. **设备连接检查**：确认电刺激设备物理连接状态
2. **驱动验证**：检查设备管理器中的设备状态
3. **端口检测**：实现自动端口扫描功能
4. **原QT程序对比**：在相同环境下测试原QT程序是否正常工作

### 代码改进
1. **实现设备诊断功能**
2. **修正电流单位转换**
3. **完善刺激启动流程**
4. **增强错误处理和用户反馈**

### 测试验证
1. **无设备测试**：验证代码在无设备环境下的错误处理
2. **有设备测试**：在真实设备上验证修复效果
3. **长期稳定性测试**：验证连续使用的稳定性

## 🔍 关键发现：原QT程序协议分析

通过深入分析原QT程序代码（nk.cpp和nk.h），我发现了以下关键信息：

### 1. 电刺激启动流程差异

**原QT程序的实际流程**：
```cpp
// 在治疗开始时，原QT程序并没有立即启动电刺激
// 而是在特定条件触发时才启动电刺激

// 暂停电刺激（第5344行）
int nChanNum = 1;
int nStateNum = 1;  // 1 = 暂停状态
SwitchChannelState(nChanNum, nStateNum);

// 停止电刺激（第5295-5299行）
int nChanNum = 1;
int nStateNum = 0;  // 0 = 停止状态
SwitchChannelState(nChanNum, nStateNum);
SwitchDeviceState(0);  // 0 = 空闲状态
```

**关键发现**：
- 原QT程序使用`SwitchChannelState(channel, 1)`来**暂停**电刺激
- 使用`SwitchChannelState(channel, 0)`来**停止**电刺激
- 使用`SwitchChannelState(channel, 3)`来**启动**电刺激（正常工作状态）

### 2. 电刺激触发机制

**重要发现**：原QT程序中电刺激不是手动启动的，而是**基于脑电信号触发**的！

从代码分析可以看出：
1. 治疗开始时，设备连接并设置参数，但**不立即启动电刺激**
2. 电刺激的启动是通过**脑电信号分析结果触发**的
3. 当检测到特定的运动想象信号时，才会调用`SwitchChannelState(channel, 3)`启动电刺激

### 3. 回调函数的作用

```cpp
// 原QT程序中的回调函数声明（nk.h第113行）
static int _stdcall callf(HANDLE pHandle, short* lpBuffer, int nSize);
```

这个回调函数用于：
- 接收设备状态数据
- 更新通道状态（dcjtdzt和dcjtdztb变量）
- 控制电刺激指示灯显示

### 4. 电流设置单位确认

从原QT程序的UI代码可以看出：
```cpp
ui->spinBox_dianchiji_A->setSuffix("mA"); // 显示单位为mA
ui->spinBox_dianchiji_B->setSuffix("mA");
```

但实际传递给DLL的值可能需要转换。

## 🎯 问题根本原因分析

### 主要问题：刺激启动时机错误

**我们的实现**：
- 连接设备后立即尝试启动电刺激
- 手动调用`start_stimulation_verified`

**原QT程序的实现**：
- 连接设备后只是准备就绪
- 电刺激由**脑电信号触发机制**控制
- 只有在检测到特定脑电模式时才启动电刺激

### 次要问题：参数设置时机

**我们的实现**：
- 在启动刺激时设置参数

**原QT程序的实现**：
- 在设备连接时就设置好所有参数
- 启动时只需要切换通道状态

## 🔧 修复方案

### 1. 修正刺激启动逻辑

```python
def prepare_stimulation(self, channel_num: int, current_ma: float) -> bool:
    """准备刺激（设置参数但不启动）"""
    # 1. 设置刺激参数
    # 2. 设置电流
    # 3. 设备切换到循环刺激状态
    # 4. 但不启动通道（等待触发）

def trigger_stimulation(self, channel_num: int) -> bool:
    """触发刺激（基于脑电信号）"""
    # 只调用 SwitchChannelState(channel, 3)
    result = self._safe_dll_call('SwitchChannelState', channel_num, 3)
    return result == 0
```

### 2. 实现脑电触发机制

```python
def check_eeg_trigger_condition(self) -> bool:
    """检查是否满足电刺激触发条件"""
    # 基于脑电信号分析结果
    # 当检测到运动想象信号时返回True

def auto_stimulation_control(self):
    """自动电刺激控制"""
    if self.check_eeg_trigger_condition():
        self.trigger_stimulation(selected_channel)
    else:
        self.pause_stimulation(selected_channel)
```

### 3. 修正测试方法

当前的测试方法可能不适用于实际使用场景，因为：
- 实际使用中电刺激是**被动触发**的，不是主动启动的
- 需要有脑电信号输入才能触发电刺激
- 手动测试应该模拟这种触发机制

## 总结

**根本问题**：我们实现的是"主动启动电刺激"，但原QT程序实现的是"被动触发电刺激"。

**解决方案**：
1. 修改刺激控制逻辑，实现准备-触发分离
2. 集成脑电信号分析和触发机制
3. 修正测试方法，模拟真实使用场景

这解释了为什么我们的代码能够连接设备、设置参数，但实际没有刺激输出——因为我们缺少了脑电信号触发这个关键环节！
