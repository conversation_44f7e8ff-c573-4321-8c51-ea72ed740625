#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑电原始数据存储功能测试
Test EEG Raw Data Storage

测试原始数据存储和加载功能的完整性和正确性

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import numpy as np
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from core.eeg_raw_data_manager import EEGRawDataManager, TrialType
from core.eeg_data_loader import EEGDataLoader
from core.treatment_data_integration import TreatmentDataIntegration
from core.eeg_device import EEGDataPacket
from utils.app_config import AppConfig


def generate_mock_eeg_data(channels=8, duration_seconds=4, sampling_rate=125):
    """生成模拟脑电数据"""
    n_samples = int(duration_seconds * sampling_rate)
    
    # 生成基础噪声
    data = np.random.randn(channels, n_samples) * 1000
    
    # 添加一些频率成分
    t = np.linspace(0, duration_seconds, n_samples)
    
    # Alpha波 (8-12 Hz)
    alpha_freq = 10
    alpha_component = 500 * np.sin(2 * np.pi * alpha_freq * t)
    
    # Beta波 (13-30 Hz)
    beta_freq = 20
    beta_component = 300 * np.sin(2 * np.pi * beta_freq * t)
    
    # 在某些通道添加特定频率成分
    data[2, :] += alpha_component  # C3
    data[5, :] += beta_component   # C4
    
    return data.astype(np.float32)


def create_mock_eeg_packet(eeg_data, packet_index=0):
    """创建模拟脑电数据包"""
    # 从数据中提取4个样本点（每包4组数据）
    start_idx = packet_index * 4
    end_idx = min(start_idx + 4, eeg_data.shape[1])
    
    if start_idx >= eeg_data.shape[1]:
        return None
    
    # 组织数据包格式
    channel_data = []
    for i in range(start_idx, end_idx):
        if i < eeg_data.shape[1]:
            group_data = eeg_data[:, i].astype(int).tolist()
            channel_data.append(group_data)
    
    # 如果不足4组，用最后一组填充
    while len(channel_data) < 4:
        if channel_data:
            channel_data.append(channel_data[-1])
        else:
            channel_data.append([0] * 8)
    
    return EEGDataPacket(
        timestamp=datetime.now().timestamp(),
        channel_data=channel_data,
        packet_number=packet_index,
        is_valid=True
    )


def test_raw_data_manager():
    """测试原始数据管理器"""
    print("\n=== 测试原始数据管理器 ===")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        # 初始化原始数据管理器
        raw_data_manager = EEGRawDataManager(db_manager)
        
        # 测试开始会话
        patient_id = 123
        session_id = raw_data_manager.start_session(patient_id, session_type='test')
        
        assert session_id > 0, "会话创建失败"
        print(f"✅ 会话创建成功: {session_id}")
        
        # 测试保存试验数据
        for i in range(5):
            # 生成模拟数据
            eeg_data = generate_mock_eeg_data()
            label = i % 2  # 交替标签
            
            # 保存数据
            success = raw_data_manager.save_trial_data(eeg_data, label)
            assert success, f"试验{i}数据保存失败"
            print(f"✅ 试验{i}数据保存成功 (标签: {label})")
        
        # 测试结束会话
        success = raw_data_manager.end_session()
        assert success, "会话结束失败"
        print("✅ 会话结束成功")
        
        # 测试统计信息
        stats = raw_data_manager.get_storage_statistics()
        print(f"✅ 存储统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 原始数据管理器测试失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)


def test_data_loader():
    """测试数据加载器"""
    print("\n=== 测试数据加载器 ===")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 初始化组件
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        raw_data_manager = EEGRawDataManager(db_manager)
        data_loader = EEGDataLoader(db_manager)
        
        # 创建测试数据
        patient_id = 456
        session_id = raw_data_manager.start_session(patient_id)
        
        # 保存一些试验数据
        test_data = []
        test_labels = []
        
        for i in range(10):
            eeg_data = generate_mock_eeg_data()
            label = i % 2
            
            raw_data_manager.save_trial_data(eeg_data, label)
            test_data.append(eeg_data)
            test_labels.append(label)
        
        raw_data_manager.end_session()
        
        # 测试加载患者数据
        patient_data = data_loader.load_patient_data(patient_id)
        assert len(patient_data) == 10, f"期望10条记录，实际{len(patient_data)}条"
        print(f"✅ 患者数据加载成功: {len(patient_data)}条记录")
        
        # 测试加载会话数据
        session_data = data_loader.load_session_data(session_id, load_raw_data=True)
        assert 'session_info' in session_data, "会话信息缺失"
        assert len(session_data['trials']) == 10, "试验数量不匹配"
        print(f"✅ 会话数据加载成功: {len(session_data['trials'])}个试验")
        
        # 测试获取训练数据集
        dataset = data_loader.get_training_dataset([patient_id])
        assert len(dataset.train_data) > 0, "训练数据为空"
        print(f"✅ 训练数据集获取成功: 训练{len(dataset.train_data)}, "
              f"验证{len(dataset.val_data)}, 测试{len(dataset.test_data)}")
        
        # 测试数据统计
        stats = data_loader.get_data_statistics([patient_id])
        print(f"✅ 数据统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)


def test_treatment_integration():
    """测试治疗数据集成"""
    print("\n=== 测试治疗数据集成 ===")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 初始化组件
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        integration = TreatmentDataIntegration(db_manager)
        
        # 测试开始治疗会话
        patient_id = 789
        success = integration.start_treatment_session(patient_id)
        assert success, "治疗会话开始失败"
        print("✅ 治疗会话开始成功")
        
        # 模拟治疗过程
        for trial_idx in range(6):
            # 交替进行运动想象和休息试验
            trial_type = 'motor_imagery' if trial_idx % 2 == 0 else 'rest'
            
            # 开始试验记录
            success = integration.start_trial_recording(trial_type)
            assert success, f"试验{trial_idx}记录开始失败"
            
            # 生成并处理模拟数据包
            eeg_data = generate_mock_eeg_data()
            
            # 模拟数据包流
            for packet_idx in range(eeg_data.shape[1] // 4):
                packet = create_mock_eeg_packet(eeg_data, packet_idx)
                if packet:
                    integration.process_eeg_data(packet)
            
            # 结束试验记录
            success = integration.end_trial_recording()
            assert success, f"试验{trial_idx}记录结束失败"
            print(f"✅ 试验{trial_idx} ({trial_type}) 完成")
        
        # 获取会话统计
        stats = integration.get_session_statistics()
        print(f"✅ 会话统计: {stats}")
        
        # 获取会话数据用于训练
        session_data = integration.get_session_data_for_training()
        if session_data:
            data, labels = session_data
            print(f"✅ 会话训练数据: {len(data)}个试验")
        
        # 结束治疗会话
        success = integration.end_treatment_session()
        assert success, "治疗会话结束失败"
        print("✅ 治疗会话结束成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 治疗数据集成测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)


def test_data_integrity():
    """测试数据完整性"""
    print("\n=== 测试数据完整性 ===")
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 初始化组件
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        integration = TreatmentDataIntegration(db_manager)
        
        # 创建测试会话
        patient_id = 999
        integration.start_treatment_session(patient_id)
        
        # 添加一些试验数据
        for i in range(3):
            integration.start_trial_recording('motor_imagery' if i % 2 == 0 else 'rest')
            
            # 生成数据
            eeg_data = generate_mock_eeg_data()
            for packet_idx in range(eeg_data.shape[1] // 4):
                packet = create_mock_eeg_packet(eeg_data, packet_idx)
                if packet:
                    integration.process_eeg_data(packet)
            
            integration.end_trial_recording()
        
        session_id = integration.current_session.session_id
        integration.end_treatment_session()
        
        # 验证数据完整性
        validation_result = integration.validate_data_integrity(session_id)
        
        assert validation_result['valid'], f"数据完整性验证失败: {validation_result.get('error', '')}"
        print(f"✅ 数据完整性验证通过: {validation_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)


def main():
    """主测试函数"""
    print("开始脑电原始数据存储功能测试...")
    
    # 检查依赖
    try:
        import h5py
        print("✅ HDF5支持可用")
    except ImportError:
        print("❌ HDF5支持不可用，请安装h5py: pip install h5py")
        return False
    
    # 运行测试
    tests = [
        ("原始数据管理器", test_raw_data_manager),
        ("数据加载器", test_data_loader),
        ("治疗数据集成", test_treatment_integration),
        ("数据完整性", test_data_integrity),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 输出总结
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！原始数据存储功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
