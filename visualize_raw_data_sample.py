#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化原始数据样本
Visualize Raw Data Sample

可视化保存的原始脑电数据，验证8通道125Hz数据的质量

作者: AI Assistant
版本: 1.0.0
"""

import sys
import h5py
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def visualize_eeg_data_sample():
    """可视化脑电数据样本"""
    print("📊 可视化原始脑电数据样本")
    print("=" * 50)
    
    # 数据文件路径
    data_file = Path("data/raw_eeg_data/patients/patient_2343465467/sessions/2025-06-05/P2343465467_2025-06-05_222507_training.h5")
    
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    
    try:
        with h5py.File(data_file, 'r') as f:
            print(f"📁 打开文件: {data_file.name}")
            
            # 获取会话元数据
            session_meta = f['session_metadata']
            channels = session_meta.attrs['channels']
            sampling_rate = session_meta.attrs['sampling_rate']
            channel_names = session_meta.attrs['channel_names']
            
            print(f"📊 会话信息:")
            print(f"  通道数: {channels}")
            print(f"  采样率: {sampling_rate} Hz")
            print(f"  通道名称: {[name.decode() if isinstance(name, bytes) else name for name in channel_names]}")
            
            # 获取试验数据
            trials_group = f['trials']
            trial_names = sorted(trials_group.keys())
            
            print(f"\n🧠 分析前3个试验的数据:")
            
            # 创建图形
            fig, axes = plt.subplots(3, 2, figsize=(15, 12))
            fig.suptitle('原始脑电数据样本 - 8通道125Hz验证', fontsize=16)
            
            for i, trial_name in enumerate(trial_names[:3]):
                trial_group = trials_group[trial_name]
                
                if 'eeg_data' not in trial_group:
                    continue
                
                eeg_data = trial_group['eeg_data'][:]
                label = trial_group.attrs.get('label', 0)
                duration = trial_group.attrs.get('duration_seconds', 0)
                
                label_text = "运动想象" if label == 1 else "休息"
                
                print(f"\n  试验 {trial_name}:")
                print(f"    标签: {label_text}")
                print(f"    数据形状: {eeg_data.shape}")
                print(f"    时长: {duration:.3f} 秒")
                print(f"    数据范围: [{np.min(eeg_data):.1f}, {np.max(eeg_data):.1f}]")
                
                # 创建时间轴
                time_axis = np.linspace(0, duration, eeg_data.shape[1])
                
                # 绘制所有通道的数据
                ax1 = axes[i, 0]
                for ch in range(min(8, eeg_data.shape[0])):
                    ch_name = channel_names[ch].decode() if isinstance(channel_names[ch], bytes) else channel_names[ch]
                    ax1.plot(time_axis, eeg_data[ch, :] + ch * 2000, 
                            label=f'{ch_name}', linewidth=0.8)
                
                ax1.set_title(f'试验 {i+1}: {label_text} - 所有通道')
                ax1.set_xlabel('时间 (秒)')
                ax1.set_ylabel('幅值 (μV)')
                ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
                ax1.grid(True, alpha=0.3)
                
                # 绘制单个通道的详细数据 (C3通道)
                ax2 = axes[i, 1]
                c3_idx = 3  # C3通道索引
                if c3_idx < eeg_data.shape[0]:
                    ax2.plot(time_axis, eeg_data[c3_idx, :], 'b-', linewidth=1)
                    ax2.set_title(f'试验 {i+1}: C3通道详细数据')
                    ax2.set_xlabel('时间 (秒)')
                    ax2.set_ylabel('幅值 (μV)')
                    ax2.grid(True, alpha=0.3)
                    
                    # 添加统计信息
                    mean_val = np.mean(eeg_data[c3_idx, :])
                    std_val = np.std(eeg_data[c3_idx, :])
                    ax2.text(0.02, 0.98, f'均值: {mean_val:.1f}\n标准差: {std_val:.1f}', 
                            transform=ax2.transAxes, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图像
            output_file = "raw_data_visualization.png"
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            print(f"\n📊 可视化图像已保存: {output_file}")
            
            # 显示图像（如果在交互环境中）
            try:
                plt.show()
            except:
                pass
            
            # 数据质量分析
            print(f"\n🔍 数据质量分析:")
            
            # 分析所有试验的数据
            all_data = []
            all_labels = []
            
            for trial_name in trial_names:
                trial_group = trials_group[trial_name]
                if 'eeg_data' in trial_group:
                    eeg_data = trial_group['eeg_data'][:]
                    label = trial_group.attrs.get('label', 0)
                    all_data.append(eeg_data)
                    all_labels.append(label)
            
            if all_data:
                # 计算统计信息
                all_data_array = np.concatenate(all_data, axis=1)
                
                print(f"  总数据形状: {all_data_array.shape}")
                print(f"  总时长: {all_data_array.shape[1] / sampling_rate:.2f} 秒")
                print(f"  数据范围: [{np.min(all_data_array):.1f}, {np.max(all_data_array):.1f}]")
                print(f"  平均值: {np.mean(all_data_array):.2f}")
                print(f"  标准差: {np.std(all_data_array):.2f}")
                
                # 检查每个通道的数据
                print(f"\n  各通道统计:")
                for ch in range(min(8, all_data_array.shape[0])):
                    ch_name = channel_names[ch].decode() if isinstance(channel_names[ch], bytes) else channel_names[ch]
                    ch_data = all_data_array[ch, :]
                    ch_mean = np.mean(ch_data)
                    ch_std = np.std(ch_data)
                    ch_range = np.max(ch_data) - np.min(ch_data)
                    
                    print(f"    {ch_name}: 均值={ch_mean:.1f}, 标准差={ch_std:.1f}, 范围={ch_range:.1f}")
                
                # 标签分布
                motor_count = sum(1 for label in all_labels if label == 1)
                rest_count = len(all_labels) - motor_count
                print(f"\n  标签分布:")
                print(f"    运动想象: {motor_count} 个试验")
                print(f"    休息状态: {rest_count} 个试验")
                print(f"    平衡比例: {motor_count/(rest_count+1e-6):.2f}")
            
            return True
            
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_sampling_rate_accuracy():
    """分析采样率准确性"""
    print(f"\n⏱️ 采样率准确性分析:")
    print("=" * 30)
    
    data_file = Path("data/raw_eeg_data/patients/patient_2343465467/sessions/2025-06-05/P2343465467_2025-06-05_222507_training.h5")
    
    try:
        with h5py.File(data_file, 'r') as f:
            trials_group = f['trials']
            trial_names = sorted(trials_group.keys())
            
            sampling_rates = []
            durations = []
            sample_counts = []
            
            for trial_name in trial_names:
                trial_group = trials_group[trial_name]
                if 'eeg_data' in trial_group:
                    eeg_data = trial_group['eeg_data']
                    duration = trial_group.attrs.get('duration_seconds', 0)
                    
                    if duration > 0:
                        samples = eeg_data.shape[1]
                        rate = samples / duration
                        
                        sampling_rates.append(rate)
                        durations.append(duration)
                        sample_counts.append(samples)
            
            if sampling_rates:
                avg_rate = np.mean(sampling_rates)
                rate_std = np.std(sampling_rates)
                min_rate = np.min(sampling_rates)
                max_rate = np.max(sampling_rates)
                
                print(f"采样率统计:")
                print(f"  平均值: {avg_rate:.3f} Hz")
                print(f"  标准差: {rate_std:.3f} Hz")
                print(f"  范围: [{min_rate:.3f}, {max_rate:.3f}] Hz")
                print(f"  目标值: 125.0 Hz")
                print(f"  误差: {abs(avg_rate - 125):.3f} Hz")
                
                if abs(avg_rate - 125) < 0.1:
                    print("✅ 采样率精度优秀 (<0.1Hz误差)")
                elif abs(avg_rate - 125) < 1.0:
                    print("✅ 采样率精度良好 (<1Hz误差)")
                else:
                    print("⚠️ 采样率精度需要改善")
                
                print(f"\n时长统计:")
                print(f"  平均时长: {np.mean(durations):.3f} 秒")
                print(f"  时长范围: [{np.min(durations):.3f}, {np.max(durations):.3f}] 秒")
                
                print(f"\n样本数统计:")
                print(f"  平均样本数: {np.mean(sample_counts):.1f}")
                print(f"  样本数范围: [{np.min(sample_counts)}, {np.max(sample_counts)}]")
                
                return True
            
    except Exception as e:
        print(f"❌ 采样率分析失败: {e}")
        return False

def main():
    """主函数"""
    print("📊 原始数据可视化和质量验证")
    print("=" * 50)
    
    # 1. 可视化数据样本
    success1 = visualize_eeg_data_sample()
    
    # 2. 分析采样率准确性
    success2 = analyze_sampling_rate_accuracy()
    
    # 总结
    print("\n" + "=" * 50)
    print("验证总结")
    print("=" * 50)
    
    if success1 and success2:
        print("✅ 原始数据可视化和验证完成！")
        print("🎉 确认保存了完整的8通道125Hz脑电数据")
        print("📊 数据质量良好，可用于后续分析")
        return 0
    else:
        print("❌ 验证过程中出现问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
