#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备深度诊断测试
专门分析为什么SwitchChannelState(channel, 3)命令执行成功但状态不变的问题
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置详细日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('stimulation_diagnosis.log', encoding='utf-8')
        ]
    )

def test_channel_state_transitions():
    """测试通道状态转换的详细过程"""
    print("=" * 60)
    print("🔬 电刺激设备通道状态转换诊断")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        # 步骤1：连接设备
        print("\n📡 连接设备...")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")
        
        # 步骤2：设置刺激参数
        print("\n⚙️ 设置刺激参数...")
        params = StimulationParameters(
            channel_num=1,
            frequency=25.0,
            pulse_width=250.0,
            relax_time=5.0,
            climb_time=2.0,
            work_time=10.0,
            fall_time=2.0,
            wave_type=0
        )
        
        if not device.set_stimulation_parameters(params):
            print("❌ 刺激参数设置失败")
            return False
        print("✅ 刺激参数设置成功")
        
        # 步骤3：详细测试每个状态转换
        channel = 1
        
        print(f"\n🔍 开始详细测试通道{channel}的状态转换...")
        
        # 测试状态0 -> 2 (停止 -> 电流调节)
        print("\n--- 测试状态转换: 停止(0) -> 电流调节(2) ---")
        initial_status = device.get_channel_status(channel)
        print(f"初始状态: {initial_status} ({device._get_channel_status_text(initial_status)})")
        
        result = device._safe_dll_call('SwitchChannelState', channel, 2)
        print(f"SwitchChannelState({channel}, 2) 返回值: {result}")
        
        time.sleep(0.5)  # 等待状态更新
        new_status = device.get_channel_status(channel)
        print(f"切换后状态: {new_status} ({device._get_channel_status_text(new_status)})")
        
        if new_status == 2:
            print("✅ 状态转换成功: 停止 -> 电流调节")
        else:
            print("❌ 状态转换失败: 期望2，实际" + str(new_status))
        
        # 设置电流
        print("\n--- 设置电流 ---")
        current_ma = 5.0
        current_value = int(current_ma * 10)
        result = device._safe_dll_call('CurrentSet', channel, current_value)
        print(f"CurrentSet({channel}, {current_value}) 返回值: {result}")
        
        time.sleep(0.3)
        status_after_current = device.get_channel_status(channel)
        print(f"设置电流后状态: {status_after_current} ({device._get_channel_status_text(status_after_current)})")
        
        # 测试状态2 -> 3 (电流调节 -> 正常工作)
        print("\n--- 测试状态转换: 电流调节(2) -> 正常工作(3) ---")
        before_status = device.get_channel_status(channel)
        print(f"转换前状态: {before_status} ({device._get_channel_status_text(before_status)})")
        
        # 确保设备处于循环刺激状态
        device_result = device._safe_dll_call('SwitchDeviceState', 1)
        print(f"SwitchDeviceState(1) 返回值: {device_result}")
        time.sleep(0.2)
        
        result = device._safe_dll_call('SwitchChannelState', channel, 3)
        print(f"SwitchChannelState({channel}, 3) 返回值: {result}")
        
        # 多次检查状态变化
        print("监控状态变化...")
        for i in range(10):
            time.sleep(0.5)
            current_status = device.get_channel_status(channel)
            status_text = device._get_channel_status_text(current_status)
            print(f"[{i+1:2d}] 状态: {current_status} ({status_text})")
            
            if current_status == 3:
                print("✅ 检测到正常工作状态！")
                break
            elif current_status != before_status:
                print(f"⚠️ 状态发生变化: {before_status} -> {current_status}")
                before_status = current_status
        else:
            print("❌ 10次检查后仍未检测到正常工作状态")
        
        # 测试其他状态转换
        print("\n--- 测试其他状态转换 ---")
        
        # 测试暂停状态
        print("测试暂停状态(1)...")
        result = device._safe_dll_call('SwitchChannelState', channel, 1)
        print(f"SwitchChannelState({channel}, 1) 返回值: {result}")
        time.sleep(0.5)
        pause_status = device.get_channel_status(channel)
        print(f"暂停状态: {pause_status} ({device._get_channel_status_text(pause_status)})")
        
        # 再次尝试正常工作状态
        print("再次尝试正常工作状态(3)...")
        result = device._safe_dll_call('SwitchChannelState', channel, 3)
        print(f"SwitchChannelState({channel}, 3) 返回值: {result}")
        time.sleep(0.5)
        final_status = device.get_channel_status(channel)
        print(f"最终状态: {final_status} ({device._get_channel_status_text(final_status)})")
        
        # 分析结果
        print("\n📊 诊断结果分析:")
        if final_status == 3:
            print("✅ 通道能够进入正常工作状态")
            return True
        else:
            print("❌ 通道无法进入正常工作状态")
            print("🔍 可能的原因:")
            print("   1. 设备固件限制：可能需要特定的前置条件")
            print("   2. 参数设置问题：某些参数可能阻止状态切换")
            print("   3. 设备硬件问题：设备可能无法实际输出电流")
            print("   4. 协议理解错误：可能存在未知的状态切换要求")
            return False
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        print("\n🧹 清理资源...")
        device.disconnect()
        print("✅ 资源清理完成")

def main():
    """主函数"""
    setup_logging()
    
    print("🔬 NK电刺激设备深度诊断")
    print("专门分析通道状态转换问题")
    
    success = test_channel_state_transitions()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 诊断完成：设备能够正常工作")
    else:
        print("❌ 诊断完成：发现关键问题")
        print("📄 详细诊断日志：stimulation_diagnosis.log")
        print("🔧 建议：")
        print("   1. 检查设备硬件连接")
        print("   2. 验证设备固件版本")
        print("   3. 对比原QT程序的详细调用序列")
        print("   4. 检查是否需要额外的设备初始化步骤")
    print("=" * 60)

if __name__ == "__main__":
    main()
