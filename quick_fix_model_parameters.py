#!/usr/bin/env python3
"""
快速修复模型参数设置
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_fix_parameters():
    """快速修复模型参数"""
    print("=" * 60)
    print("快速修复EEGNet模型参数")
    print("=" * 60)
    
    try:
        from core.ml_model import ModelManager
        
        print("1. 加载当前模型...")
        manager = ModelManager()
        model = manager.load_model("MI_Model_1748771308")
        
        if not model:
            print("   ❌ 无法加载模型")
            return False
        
        print(f"   ✅ 模型加载成功: {model.model_name}")
        
        # 获取模型信息
        info = model.get_model_info()
        print(f"   - 当前决策阈值: {info.decision_threshold:.3f}")
        print(f"   - 当前置信度阈值: {info.confidence_threshold:.3f}")
        print(f"   - 当前难度等级: {info.difficulty_level}")
        
        print("\n2. 应用优化参数...")
        
        # 基于观察到的置信度范围(0.514-0.516)进行优化
        optimal_decision_threshold = 0.45   # 低于观察到的最低置信度
        optimal_confidence_threshold = 0.55  # 适中的置信度要求
        optimal_difficulty = 2              # 提高难度等级
        
        # 应用新参数
        info.decision_threshold = optimal_decision_threshold
        info.confidence_threshold = optimal_confidence_threshold
        model.adjust_difficulty(optimal_difficulty)
        
        print(f"   ✅ 新决策阈值: {info.decision_threshold:.3f}")
        print(f"   ✅ 新置信度阈值: {info.confidence_threshold:.3f}")
        print(f"   ✅ 新难度等级: {info.difficulty_level}")
        
        print("\n3. 测试新参数效果...")
        import numpy as np
        
        # 测试不同类型的数据
        test_cases = [
            ("休息状态模拟", np.random.randn(8, 250) * 50),
            ("运动想象模拟", np.random.randn(8, 250) * 100 + np.sin(2 * np.pi * 10 * np.linspace(0, 2, 250)) * 150),
        ]
        
        for name, test_data in test_cases:
            pred, conf, status = model.predict_with_adjustment(test_data)
            print(f"   {name}: 类别={pred}, 置信度={conf:.3f}, 状态={status}")
        
        print("\n4. 保存优化后的模型...")
        save_success = manager.save_model(model, "MI_Model_1748771308")
        if save_success:
            print("   ✅ 模型参数已保存")
        else:
            print("   ⚠️  模型保存失败，但参数在当前会话中有效")
        
        print("\n5. 使用建议...")
        print("   💡 参数优化完成，建议:")
        print("   1. 重新启动在线分类测试新参数")
        print("   2. 观察分类结果是否有改善")
        print("   3. 如果仍然单一，考虑重新训练模型")
        print("   4. 收集更多高质量的训练数据")
        
        print("\n6. 长期解决方案...")
        print("   🎯 为了获得更好的分类效果:")
        print("   1. 增加训练数据量（目标：50-100个样本）")
        print("   2. 改善数据质量：")
        print("      - 运动想象指导更清晰")
        print("      - 确保患者理解任务")
        print("      - 延长运动想象时间")
        print("   3. 考虑使用数据增强技术")
        print("   4. 尝试不同的预处理方法")
        
        print("\n" + "=" * 60)
        print("🎉 参数优化完成！")
        print("=" * 60)
        
        print("\n📋 总结:")
        print(f"✅ 决策阈值: {info.decision_threshold:.3f} (降低以适应当前模型)")
        print(f"✅ 置信度阈值: {info.confidence_threshold:.3f} (合理设置)")
        print(f"✅ 难度等级: {info.difficulty_level} (适度提高)")
        print("✅ 参数已保存，可以立即使用")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 参数优化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_fix_parameters()
    if success:
        print("\n🎯 参数优化成功！请重新测试在线分类功能。")
    else:
        print("\n⚠️  参数优化失败，请检查模型状态。")
    
    sys.exit(0 if success else 1)
