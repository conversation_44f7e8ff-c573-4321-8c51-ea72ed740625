#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动速度测试工具
对比优化前后的启动性能
"""

import time
import sys
import os
import subprocess
import psutil
from pathlib import Path

def test_startup_performance():
    """测试启动性能"""
    print("🚀 启动性能测试工具")
    print("=" * 60)
    
    # 测试原版启动
    print("\n📊 测试原版启动性能...")
    original_time, original_memory = measure_startup("python main.py --test-mode")
    
    # 测试优化版启动
    print("\n📊 测试优化版启动性能...")
    optimized_time, optimized_memory = measure_startup("python main_optimized.py --test-mode")
    
    # 对比结果
    print("\n📋 性能对比结果:")
    print("=" * 60)
    
    if original_time and optimized_time:
        time_improvement = ((original_time - optimized_time) / original_time) * 100
        print(f"⏱️ 启动时间:")
        print(f"   原版: {original_time:.2f}s")
        print(f"   优化版: {optimized_time:.2f}s")
        print(f"   改善: {time_improvement:.1f}%")
    
    if original_memory and optimized_memory:
        memory_improvement = ((original_memory - optimized_memory) / original_memory) * 100
        print(f"\n💾 内存使用:")
        print(f"   原版: {original_memory:.1f}MB")
        print(f"   优化版: {optimized_memory:.1f}MB")
        print(f"   改善: {memory_improvement:.1f}%")
    
    # 生成建议
    print(f"\n💡 优化建议:")
    if time_improvement > 20:
        print("✅ 启动时间显著改善，优化效果良好")
    elif time_improvement > 10:
        print("✅ 启动时间有所改善，可继续优化")
    else:
        print("⚠️ 启动时间改善不明显，需要进一步优化")

def measure_startup(command):
    """测量启动性能"""
    try:
        print(f"   执行命令: {command}")
        
        start_time = time.time()
        start_memory = get_system_memory()
        
        # 启动进程
        process = subprocess.Popen(
            command.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待进程启动完成（或超时）
        max_wait = 30  # 最大等待30秒
        waited = 0
        
        while process.poll() is None and waited < max_wait:
            time.sleep(0.1)
            waited += 0.1
        
        end_time = time.time()
        end_memory = get_system_memory()
        
        # 终止进程
        if process.poll() is None:
            process.terminate()
            process.wait(timeout=5)
        
        startup_time = end_time - start_time
        memory_usage = end_memory - start_memory
        
        print(f"   启动时间: {startup_time:.2f}s")
        print(f"   内存增长: {memory_usage:.1f}MB")
        
        return startup_time, memory_usage
        
    except Exception as e:
        print(f"   ❌ 测量失败: {e}")
        return None, None

def get_system_memory():
    """获取系统内存使用量"""
    try:
        return psutil.virtual_memory().used / 1024 / 1024  # MB
    except:
        return 0

def create_test_mode_support():
    """为main.py添加测试模式支持"""
    
    test_mode_code = '''
# 测试模式支持
if "--test-mode" in sys.argv:
    print("启动测试模式...")
    
    # 模拟启动过程但不显示界面
    class TestNKApplication(NKApplication):
        def show_splash_screen(self):
            pass  # 跳过启动画面
            
        def show_main_window(self):
            # 记录启动完成时间
            total_startup_time = time.time() - self.startup_time
            print(f"测试模式启动完成: {total_startup_time:.2f}s")
            
            # 立即退出
            if self.app:
                self.app.quit()
    
    # 使用测试版本
    def test_main():
        nk_app = TestNKApplication()
        try:
            exit_code = nk_app.run()
        except KeyboardInterrupt:
            exit_code = 0
        except Exception as e:
            print(f"测试启动失败: {e}")
            exit_code = 1
        finally:
            nk_app.cleanup()
        sys.exit(exit_code)
    
    if __name__ == "__main__":
        test_main()
'''
    
    # 检查main.py是否已有测试模式支持
    main_file = Path('main.py')
    if main_file.exists():
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "--test-mode" not in content:
            # 在文件末尾添加测试模式支持
            with open(main_file, 'a', encoding='utf-8') as f:
                f.write('\n' + test_mode_code)
            print("✅ 已为main.py添加测试模式支持")
        else:
            print("✅ main.py已支持测试模式")
    
    # 为优化版本添加测试模式支持
    optimized_file = Path('main_optimized.py')
    if optimized_file.exists():
        with open(optimized_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "--test-mode" not in content:
            with open(optimized_file, 'a', encoding='utf-8') as f:
                f.write('\n' + test_mode_code)
            print("✅ 已为main_optimized.py添加测试模式支持")

def generate_optimization_report():
    """生成优化报告"""
    
    report_content = f"""# NK系统启动优化报告

## 🎯 优化目标
- 减少系统启动时间
- 降低内存使用
- 提升用户体验

## 🔧 实施的优化措施

### 1. 环境变量优化
```python
os.environ['PYTHONOPTIMIZE'] = '1'  # 启用Python优化
os.environ['QT_LOGGING_RULES'] = '*.debug=false'  # 禁用Qt调试日志
os.environ['QT_QUICK_CONTROLS_STYLE'] = 'Basic'  # 使用基础样式
```

### 2. 数据库性能优化
```sql
PRAGMA journal_mode = WAL      -- 写前日志模式
PRAGMA synchronous = NORMAL    -- 正常同步模式
PRAGMA cache_size = 10000      -- 增加缓存大小
PRAGMA temp_store = MEMORY     -- 临时存储在内存
PRAGMA mmap_size = 268435456   -- 内存映射大小 (256MB)
```

### 3. UI组件延迟加载
- 主窗口启动时只创建占位符
- 实际组件在用户访问时才创建
- 预加载最常用的患者管理页面

### 4. 模块导入优化
- 延迟导入大型模块（matplotlib, scipy等）
- 预加载关键模块（PySide6, sqlite3等）
- 使用多线程预加载

## 📊 性能测试结果

运行 `python test_startup_speed.py` 查看详细对比结果

## 💡 进一步优化建议

1. **缓存机制**: 实现启动缓存，存储常用数据
2. **代码分析**: 使用profiler分析热点代码
3. **资源优化**: 压缩图片资源，优化样式表
4. **异步加载**: 使用异步方式加载非关键组件

## 🎉 预期效果

- 启动时间减少: 30-50%
- 内存使用优化: 20-30%
- 用户体验提升: 显著改善

## 📋 使用方法

1. **使用优化版启动**:
   ```bash
   python main_optimized.py
   ```

2. **性能测试**:
   ```bash
   python test_startup_speed.py
   ```

3. **性能分析**:
   ```bash
   python startup_performance_analyzer.py
   ```

---
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('启动优化报告.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✅ 优化报告已生成: 启动优化报告.md")

def main():
    """主函数"""
    print("🔧 NK系统启动优化测试工具")
    print("=" * 80)
    
    # 1. 添加测试模式支持
    print("1. 准备测试环境...")
    create_test_mode_support()
    
    # 2. 测试启动性能
    print("\n2. 测试启动性能...")
    test_startup_performance()
    
    # 3. 生成优化报告
    print("\n3. 生成优化报告...")
    generate_optimization_report()
    
    print("\n" + "=" * 80)
    print("🎉 启动优化测试完成！")
    print("\n📋 后续步骤:")
    print("1. 查看优化报告: 启动优化报告.md")
    print("2. 使用优化版本: python main_optimized.py")
    print("3. 继续性能分析: python startup_performance_analyzer.py")
    print("=" * 80)

if __name__ == "__main__":
    main()
