#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试迁移学习用户反馈功能
Test Transfer Learning User Feedback

测试训练完成后的迁移学习使用提示和性能对比

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


def test_transfer_learning_feedback():
    """测试迁移学习反馈功能"""
    print("🧪 测试迁移学习用户反馈功能")
    print("=" * 60)
    
    try:
        # 测试1: 使用迁移学习训练
        print("\n📋 测试1: 迁移学习训练")
        print("-" * 40)
        
        model1 = MotorImageryModel("Transfer_Test_Model")
        
        # 生成测试数据
        print("🔄 生成测试数据...")
        n_samples = 20
        for i in range(n_samples):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model1.add_training_data(data, label)
        
        print(f"✅ 添加训练数据: {n_samples} 个样本")
        
        # 启用迁移学习
        model_info1 = model1.get_model_info()
        model_info1.transfer_learning = True
        model_info1.finetune_layers = 3
        print("✅ 启用迁移学习，微调层数: 3")
        
        # 训练模型
        print("🚀 开始迁移学习训练...")
        
        def progress_callback1(message, progress):
            if progress % 20 == 0:  # 只显示关键进度
                print(f"  [{progress:3d}%] {message}")
        
        config1 = TrainingConfig(epochs=5, batch_size=8, learning_rate=0.001)
        
        start_time1 = time.time()
        success1 = model1.train_model(config=config1, progress_callback=progress_callback1)
        training_time1 = time.time() - start_time1
        
        if success1:
            print(f"✅ 迁移学习训练完成，耗时: {training_time1:.1f}秒")
            
            # 检查迁移学习标记
            model_info1 = model1.get_model_info()
            if hasattr(model_info1, 'used_transfer_learning') and model_info1.used_transfer_learning:
                print("🎯 确认使用了迁移学习！")
                print(f"   📁 预训练模型: {getattr(model_info1, 'pretrained_model_path', '未知')}")
                print(f"   🔧 微调层数: {getattr(model_info1, 'finetune_layers', '未知')}")
            else:
                print("⚠️ 迁移学习可能失败，回退到普通训练")
            
            # 显示性能
            if hasattr(model_info1, 'performance') and model_info1.performance:
                perf1 = model_info1.performance
                print(f"📊 训练准确率: {perf1.accuracy*100:.1f}%")
                print(f"📊 验证准确率: {perf1.val_accuracy*100:.1f}%")
        else:
            print("❌ 迁移学习训练失败")
            return False
        
        # 测试2: 普通训练对比
        print("\n📋 测试2: 普通训练对比")
        print("-" * 40)
        
        model2 = MotorImageryModel("Normal_Test_Model")
        
        # 使用相同的测试数据
        print("🔄 使用相同的测试数据...")
        for i in range(n_samples):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model2.add_training_data(data, label)
        
        # 禁用迁移学习
        model_info2 = model2.get_model_info()
        model_info2.transfer_learning = False
        print("🔧 禁用迁移学习，使用从头训练")
        
        # 训练模型
        print("🚀 开始普通训练...")
        
        def progress_callback2(message, progress):
            if progress % 20 == 0:  # 只显示关键进度
                print(f"  [{progress:3d}%] {message}")
        
        config2 = TrainingConfig(epochs=5, batch_size=8, learning_rate=0.001)
        
        start_time2 = time.time()
        success2 = model2.train_model(config=config2, progress_callback=progress_callback2)
        training_time2 = time.time() - start_time2
        
        if success2:
            print(f"✅ 普通训练完成，耗时: {training_time2:.1f}秒")
            
            # 检查训练方式
            model_info2 = model2.get_model_info()
            if hasattr(model_info2, 'used_transfer_learning') and not model_info2.used_transfer_learning:
                print("🔧 确认使用了从头训练！")
            else:
                print("⚠️ 训练方式标记可能有问题")
            
            # 显示性能
            if hasattr(model_info2, 'performance') and model_info2.performance:
                perf2 = model_info2.performance
                print(f"📊 训练准确率: {perf2.accuracy*100:.1f}%")
                print(f"📊 验证准确率: {perf2.val_accuracy*100:.1f}%")
        else:
            print("❌ 普通训练失败")
            return False
        
        # 测试3: 性能对比
        print("\n📋 测试3: 性能对比分析")
        print("-" * 40)
        
        if success1 and success2:
            print("🔍 训练方式对比:")
            print(f"   迁移学习: {'✅ 已使用' if getattr(model_info1, 'used_transfer_learning', False) else '❌ 未使用'}")
            print(f"   从头训练: {'✅ 已使用' if not getattr(model_info2, 'used_transfer_learning', True) else '❌ 未使用'}")
            
            print(f"\n⏱️ 训练时间对比:")
            print(f"   迁移学习: {training_time1:.1f}秒")
            print(f"   从头训练: {training_time2:.1f}秒")
            time_improvement = (training_time2 - training_time1) / training_time2 * 100
            print(f"   时间节省: {time_improvement:.1f}%")
            
            if hasattr(model_info1, 'performance') and hasattr(model_info2, 'performance'):
                perf1 = model_info1.performance
                perf2 = model_info2.performance
                
                print(f"\n📈 准确率对比:")
                print(f"   迁移学习: {perf1.accuracy*100:.1f}%")
                print(f"   从头训练: {perf2.accuracy*100:.1f}%")
                acc_improvement = (perf1.accuracy - perf2.accuracy) * 100
                print(f"   准确率差异: {acc_improvement:+.1f}%")
                
                print(f"\n🎯 综合评估:")
                if time_improvement > 0:
                    print(f"   ✅ 迁移学习训练速度提升 {time_improvement:.1f}%")
                else:
                    print(f"   ⚠️ 迁移学习训练时间增加 {-time_improvement:.1f}%")
                
                if acc_improvement > 0:
                    print(f"   ✅ 迁移学习准确率提升 {acc_improvement:.1f}%")
                elif acc_improvement > -5:
                    print(f"   ✅ 迁移学习准确率基本持平 ({acc_improvement:+.1f}%)")
                else:
                    print(f"   ⚠️ 迁移学习准确率下降 {acc_improvement:.1f}%")
        
        # 测试4: UI提示信息测试
        print("\n📋 测试4: UI提示信息")
        print("-" * 40)
        
        def get_training_method_info(model):
            """模拟UI中的训练方式信息获取"""
            try:
                model_info = model.get_model_info()
                
                if hasattr(model_info, 'used_transfer_learning') and model_info.used_transfer_learning:
                    pretrained_path = getattr(model_info, 'pretrained_model_path', '预训练模型')
                    finetune_layers = getattr(model_info, 'finetune_layers', 3)
                    return f"迁移学习 (预训练模型: {pretrained_path}, 微调层数: {finetune_layers})"
                elif hasattr(model_info, 'transfer_learning') and model_info.transfer_learning:
                    return "迁移学习 (可能回退到普通训练)"
                else:
                    return "从头训练"
            except Exception as e:
                return f"未知 (错误: {e})"
        
        print("🔍 模拟UI提示信息:")
        print(f"   模型1训练方式: {get_training_method_info(model1)}")
        print(f"   模型2训练方式: {get_training_method_info(model2)}")
        
        print("\n🎉 迁移学习反馈功能测试完成！")
        print("\n📋 用户体验总结:")
        print("   ✅ 训练完成后会明确显示是否使用了迁移学习")
        print("   ✅ 会显示预训练模型路径和微调层数")
        print("   ✅ 会显示训练时间和准确率对比")
        print("   ✅ 用户可以清楚地知道训练过程中发生了什么")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 迁移学习用户反馈功能测试")
    print("=" * 70)
    print("测试目标:")
    print("1. 验证迁移学习使用提示")
    print("2. 验证训练方式对比显示")
    print("3. 验证性能对比功能")
    print("4. 验证UI提示信息")
    print()
    
    success = test_transfer_learning_feedback()
    
    if success:
        print("\n🎊 所有测试通过！")
        print("您的系统现在具备完善的迁移学习用户反馈功能！")
    else:
        print("\n❌ 测试失败，请检查系统配置")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
