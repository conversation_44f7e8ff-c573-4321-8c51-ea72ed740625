#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电刺激启动修复
Test Stimulation Start Fix

验证电刺激启动逻辑修复：
1. 只启动有电流设置的通道
2. 从治疗界面获取电流设置
3. 正确的电刺激启动方法

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stimulation_logic():
    """测试电刺激启动逻辑"""
    print("=== 测试电刺激启动逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面
        class MockTreatmentUI:
            def __init__(self, current_a=2, current_b=0):
                # 模拟电流控件
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_current = MockSpinBox(current_a)
                self.channel_b_current = MockSpinBox(current_b)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.start_calls = []
                
            def is_connected(self):
                return self.connected
                
            def start_stimulation(self, channel_num):
                self.start_calls.append(channel_num)
                print(f"模拟启动通道{channel_num}刺激")
                return True
        
        # 设置模拟组件
        mock_ui = MockTreatmentUI(current_a=2, current_b=0)  # A通道2mA，B通道0mA
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        # 测试启动刺激方法
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"B通道电流: {mock_ui.channel_b_current.value()}mA")
        
        success = controller._start_stimulation()
        
        print(f"启动结果: {success}")
        print(f"启动的通道: {mock_device.start_calls}")
        
        # 验证结果
        if success and len(mock_device.start_calls) == 1 and mock_device.start_calls[0] == 1:
            print("✓ 电刺激启动逻辑正确：只启动了A通道（有电流）")
            return True
        else:
            print("✗ 电刺激启动逻辑错误")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_zero_current_scenario():
    """测试零电流场景"""
    print("\n=== 测试零电流场景 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A和B通道都为0）
        class MockTreatmentUI:
            def __init__(self):
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_current = MockSpinBox(0)
                self.channel_b_current = MockSpinBox(0)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.start_calls = []
                
            def is_connected(self):
                return self.connected
                
            def start_stimulation(self, channel_num):
                self.start_calls.append(channel_num)
                return True
        
        # 设置模拟组件
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        # 测试启动刺激方法
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"B通道电流: {mock_ui.channel_b_current.value()}mA")
        
        success = controller._start_stimulation()
        
        print(f"启动结果: {success}")
        print(f"启动的通道: {mock_device.start_calls}")
        
        # 验证结果
        if not success and len(mock_device.start_calls) == 0:
            print("✓ 零电流场景处理正确：没有启动任何通道")
            return True
        else:
            print("✗ 零电流场景处理错误")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_dual_channel_scenario():
    """测试双通道场景"""
    print("\n=== 测试双通道场景 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A和B通道都有电流）
        class MockTreatmentUI:
            def __init__(self):
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_current = MockSpinBox(5)
                self.channel_b_current = MockSpinBox(3)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.start_calls = []
                
            def is_connected(self):
                return self.connected
                
            def start_stimulation(self, channel_num):
                self.start_calls.append(channel_num)
                return True
        
        # 设置模拟组件
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        # 测试启动刺激方法
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"B通道电流: {mock_ui.channel_b_current.value()}mA")
        
        success = controller._start_stimulation()
        
        print(f"启动结果: {success}")
        print(f"启动的通道: {mock_device.start_calls}")
        
        # 验证结果
        if success and len(mock_device.start_calls) == 2 and 1 in mock_device.start_calls and 2 in mock_device.start_calls:
            print("✓ 双通道场景处理正确：启动了A和B通道")
            return True
        else:
            print("✗ 双通道场景处理错误")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_fallback_scenario():
    """测试降级处理场景"""
    print("\n=== 测试降级处理场景 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 不设置治疗界面引用，测试降级处理
        controller.treatment_ui = None
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.start_calls = []
                self.channel_a_current = 0  # 设备记录的电流
                self.channel_b_current = 0
                
            def is_connected(self):
                return self.connected
                
            def start_stimulation(self, channel_num):
                self.start_calls.append(channel_num)
                return True
        
        # 设置模拟组件
        mock_device = MockStimulationDevice()
        controller.stimulation_device = mock_device
        
        # 测试启动刺激方法
        success = controller._start_stimulation()
        
        print(f"启动结果: {success}")
        print(f"启动的通道: {mock_device.start_calls}")
        
        # 验证结果（降级处理应该尝试启动A通道）
        if success and len(mock_device.start_calls) == 1 and mock_device.start_calls[0] == 1:
            print("✓ 降级处理场景正确：尝试启动A通道")
            return True
        else:
            print("✗ 降级处理场景错误")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试电刺激启动修复...")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("电刺激启动逻辑", test_stimulation_logic),
        ("零电流场景", test_zero_current_scenario),
        ("双通道场景", test_dual_channel_scenario),
        ("降级处理场景", test_fallback_scenario),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n修复总结:")
        print("1. ✅ 只启动有电流设置的通道")
        print("2. ✅ 从治疗界面正确获取电流设置")
        print("3. ✅ 零电流时不启动刺激")
        print("4. ✅ 双通道都有电流时启动两个通道")
        print("5. ✅ 降级处理机制正常工作")
        return True
    else:
        print("❌ 部分测试失败，请检查代码修改。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
