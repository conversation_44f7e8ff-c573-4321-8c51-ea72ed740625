#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统设置界面用户管理标签页移除
Test Settings UI User Management Tab Removal

作者: AI Assistant
版本: 2.1.2
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_ui_structure():
    """测试系统设置界面结构"""
    print("🔍 测试系统设置界面结构...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.settings_ui import SettingsWidget
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建设置界面
        settings_widget = SettingsWidget()
        
        # 检查是否还有用户管理标签页
        tab_widget = None
        for child in settings_widget.children():
            if hasattr(child, 'tabText'):  # QTabWidget
                tab_widget = child
                break
        
        if tab_widget:
            tab_count = tab_widget.count()
            tab_names = []
            
            for i in range(tab_count):
                tab_name = tab_widget.tabText(i)
                tab_names.append(tab_name)
            
            print(f"✅ 找到标签页控件，共 {tab_count} 个标签页")
            print(f"📋 标签页列表: {tab_names}")
            
            # 检查是否还有用户管理标签页
            if "用户管理" in tab_names:
                print("❌ 用户管理标签页仍然存在")
                return False
            else:
                print("✅ 用户管理标签页已成功移除")
                
            # 检查预期的标签页是否存在
            expected_tabs = ["系统配置", "设备配置", "数据库管理"]
            missing_tabs = []
            
            for expected_tab in expected_tabs:
                if expected_tab not in tab_names:
                    missing_tabs.append(expected_tab)
            
            if missing_tabs:
                print(f"❌ 缺少预期的标签页: {missing_tabs}")
                return False
            else:
                print("✅ 所有预期的标签页都存在")
                
            return True
        else:
            print("❌ 未找到标签页控件")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_settings_ui_methods():
    """测试系统设置界面方法"""
    print("\n🔧 测试系统设置界面方法...")
    
    try:
        from ui.settings_ui import SettingsWidget
        
        # 检查create_user_management_tab方法是否已移除
        if hasattr(SettingsWidget, 'create_user_management_tab'):
            print("❌ create_user_management_tab 方法仍然存在")
            return False
        else:
            print("✅ create_user_management_tab 方法已成功移除")
        
        # 检查其他必要方法是否存在
        required_methods = [
            'create_system_config_tab',
            'create_device_config_tab', 
            'create_database_management_tab'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(SettingsWidget, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少必要的方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必要的方法都存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_main_window_navigation():
    """测试主窗口导航"""
    print("\n🖥️ 测试主窗口导航...")
    
    try:
        from core.main_window import MainWindow
        
        # 检查导航配置
        # 这里我们只检查类是否可以正常导入
        print("✅ MainWindow 类导入成功")
        
        # 检查是否有用户管理导航项
        # 注意：用户管理应该保留在导航中，因为有专门的用户管理页面
        print("✅ 主窗口导航检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_user_management_page():
    """测试专门的用户管理页面"""
    print("\n👥 测试专门的用户管理页面...")
    
    try:
        from ui.user_management_ui import UserManagementWidget
        
        print("✅ UserManagementWidget 类导入成功")
        
        # 检查关键方法是否存在
        required_methods = [
            'init_ui',
            'create_user_list_widget',
            'create_user_details_widget',
            'refresh_user_list'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(UserManagementWidget, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 用户管理页面缺少必要的方法: {missing_methods}")
            return False
        else:
            print("✅ 用户管理页面所有必要的方法都存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 开始测试系统设置界面用户管理标签页移除")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 测试系统设置界面结构
    if test_settings_ui_structure():
        success_count += 1
    
    # 测试系统设置界面方法
    if test_settings_ui_methods():
        success_count += 1
    
    # 测试主窗口导航
    if test_main_window_navigation():
        success_count += 1
    
    # 测试专门的用户管理页面
    if test_user_management_page():
        success_count += 1
    
    print("=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 系统设置界面用户管理标签页移除成功!")
        
        print("\n✅ 修改内容:")
        print("1. 移除了系统设置中的用户管理标签页")
        print("2. 删除了create_user_management_tab方法")
        print("3. 保留了专门的用户管理页面")
        print("4. 保持了其他标签页的完整性")
        
        print("\n🎯 现在的系统设置标签页:")
        print("- 📊 系统配置")
        print("- 🔧 设备配置") 
        print("- 💾 数据库管理")
        
        print("\n👥 用户管理功能:")
        print("- 通过左侧导航栏的'用户管理'访问")
        print("- 功能完整，包含用户列表和详情管理")
        print("- 支持添加、编辑、停用、激活用户")
        
        return True
    else:
        print("❌ 测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()
