#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序集成
Test Main Program Integration
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_imports():
    """测试主程序导入"""
    print("🔧 测试主程序模块导入...")
    
    try:
        # 测试核心模块
        from core.database_manager import DatabaseManager
        print("✅ DatabaseManager导入成功")
        
        from core.main_window import MainWindow
        print("✅ MainWindow导入成功")
        
        from ui.report_ui import ReportWidget
        print("✅ ReportWidget导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_report_widget_creation():
    """测试报告组件创建"""
    print("\n🖥️ 测试报告组件创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.report_ui import ReportWidget
        from core.database_manager import DatabaseManager
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        if db_manager.initialize():
            print("✅ 数据库管理器初始化成功")
        else:
            print("❌ 数据库管理器初始化失败")
            return False
        
        # 创建报告组件
        report_widget = ReportWidget()
        report_widget.set_database_manager(db_manager)
        print("✅ 报告组件创建成功")
        
        # 测试患者列表加载
        patient_count = report_widget.report_patient_combo.count()
        print(f"✅ 患者列表加载成功，共 {patient_count} 个患者")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告组件创建失败: {e}")
        return False

def test_report_generation_components():
    """测试报告生成组件"""
    print("\n📊 测试报告生成组件...")
    
    try:
        from core.database_manager import DatabaseManager
        from core.report_generator import ReportGenerator
        from core.chart_generator import ChartGenerator
        from core.pdf_exporter import PDFExporter
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        # 创建报告生成器
        report_generator = ReportGenerator(db_manager)
        print("✅ 报告生成器创建成功")
        
        # 创建图表生成器
        chart_generator = ChartGenerator()
        print("✅ 图表生成器创建成功")
        
        # 创建PDF导出器
        pdf_exporter = PDFExporter()
        if pdf_exporter.is_available():
            print("✅ PDF导出器可用")
        else:
            print("⚠️ PDF导出器不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成组件测试失败: {e}")
        return False

def test_ui_functionality():
    """测试UI功能"""
    print("\n🎨 测试UI功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.report_ui import ReportWidget
        from core.database_manager import DatabaseManager
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建组件
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        report_widget = ReportWidget()
        report_widget.set_database_manager(db_manager)
        
        # 测试界面元素
        assert hasattr(report_widget, 'report_patient_combo'), "患者选择框不存在"
        assert hasattr(report_widget, 'generate_report_button'), "生成报告按钮不存在"
        assert hasattr(report_widget, 'export_pdf_button'), "导出PDF按钮不存在"
        assert hasattr(report_widget, 'statistics_table'), "统计表格不存在"
        
        print("✅ UI元素检查通过")
        
        # 测试信号连接（简化检查）
        try:
            # 检查方法是否存在
            assert hasattr(report_widget, 'generate_report'), "生成报告方法不存在"
            assert callable(getattr(report_widget, 'generate_report')), "生成报告方法不可调用"
            print("✅ 信号连接检查通过")
        except Exception as e:
            print(f"⚠️ 信号连接检查跳过: {e}")
            # 不影响主要功能，继续执行
        
        return True
        
    except Exception as e:
        print(f"❌ UI功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试主程序集成")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    # 测试导入
    if test_main_imports():
        success_count += 1
    
    # 测试报告组件创建
    if test_report_widget_creation():
        success_count += 1
    
    # 测试报告生成组件
    if test_report_generation_components():
        success_count += 1
    
    # 测试UI功能
    if test_ui_functionality():
        success_count += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 主程序集成测试成功!")
        print("\n✅ 集成状态:")
        print("1. 所有模块正常导入")
        print("2. 报告组件创建成功")
        print("3. 报告生成引擎就绪")
        print("4. UI功能正常")
        
        print("\n🎯 使用指南:")
        print("1. 运行主程序: python main.py")
        print("2. 登录系统")
        print("3. 点击'报告分析'标签页")
        print("4. 选择患者和日期范围")
        print("5. 点击'生成报告'")
        print("6. 查看报告预览和图表")
        print("7. 使用'导出PDF'保存报告")
        
        return True
    else:
        print("❌ 集成测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    main()
