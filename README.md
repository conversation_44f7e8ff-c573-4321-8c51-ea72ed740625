# NK脑机接口系统 (Python版本)

## 项目概述

NK脑机接口系统是一个医疗级的脑机接口系统，用于神经康复治疗。本项目是从原有的QT C++系统迁移到Python平台的版本，保持了原有的所有功能，并新增了日志系统和权限管理等功能。

## 系统特性

### 核心功能
- **患者管理**: 患者信息录入、查询、编辑和管理
- **脑电信号处理**: 实时脑电信号采集、预处理、特征提取
- **运动想象训练**: 二分类运动想象训练（平静状态/运动想象状态）
- **电刺激治疗**: 多通道电刺激控制和参数设置
- **治疗记录**: 完整的治疗过程记录和数据存储
- **报告生成**: 自动生成治疗报告和统计分析
- **网络通信**: HTTP数据上传和UDP VR系统通信

### 新增功能
- **日志系统**: 完整的操作日志记录和查询
- **权限管理**: 多级用户权限控制和操作审计
- **数据备份**: 自动数据库备份和恢复功能
- **系统监控**: 设备状态监控和异常处理

### 技术特点
- **医疗级标准**: 符合医疗器械软件标准
- **模块化设计**: 便于后续功能升级和维护
- **高性能**: 实时数据处理，延迟小于100ms
- **高可靠性**: 完善的错误处理和恢复机制

## 系统要求

### 硬件要求
- CPU: Intel i5或同等性能处理器
- 内存: 8GB RAM (推荐16GB)
- 存储: 至少2GB可用空间
- 串口: 用于脑电设备连接
- 网络: 用于数据上传和VR通信

### 软件要求
- 操作系统: Windows 10/11 (64位)
- Python: 3.9或更高版本
- 脑电设备驱动程序
- 电刺激设备DLL库

## 安装说明

### 1. 环境准备

确保已安装Python 3.9或更高版本：
```bash
python --version
```

### 2. 安装依赖

安装项目依赖包：
```bash
pip install -r requirements.txt
```

### 3. 系统测试

运行系统测试以验证安装：
```bash
python test_system.py
```

### 4. 启动系统

运行主程序：
```bash
python main.py
```

## 项目结构

```
Python_NK_System/
├── main.py                     # 主程序入口
├── requirements.txt            # Python依赖包
├── README.md                   # 项目说明文档
├── test_system.py             # 系统测试脚本
├── 项目迁移需求文档.md         # 迁移需求文档
├── core/                      # 核心模块
│   ├── __init__.py
│   ├── main_window.py         # 主窗口
│   ├── database_manager.py    # 数据库管理
│   └── logger_system.py       # 日志系统
├── ui/                        # 用户界面模块
│   ├── __init__.py
│   ├── patient_management_ui.py # 患者管理界面
│   ├── treatment_ui.py        # 治疗系统界面
│   ├── report_ui.py           # 报告分析界面
│   └── settings_ui.py         # 系统设置界面
├── utils/                     # 工具模块
│   ├── __init__.py
│   ├── app_config.py          # 应用配置
│   └── single_instance.py     # 单实例检查
├── resources/                 # 资源文件
│   ├── images/                # 图片资源
│   ├── styles/                # 样式文件
│   │   └── main_style.qss     # 主样式表
│   └── templates/             # 报告模板
├── data/                      # 数据目录
│   ├── nk_system.db          # 主数据库
│   └── backup/               # 数据库备份
├── logs/                      # 日志目录
│   ├── nk_system.log         # 主日志文件
│   └── error.log             # 错误日志
└── libs/                      # 第三方库
    └── RecoveryDLL.dll       # 电刺激控制库
```

## 使用说明

### 1. 系统启动

1. 运行 `python main.py` 启动系统
2. 系统会显示启动画面并初始化各个模块
3. 启动完成后显示主界面

### 2. 用户登录

1. 点击左下角的"登录"按钮
2. 输入用户名和密码
3. 系统会根据用户权限显示相应功能

默认管理员账户：
- 用户名: admin
- 密码: 123456

### 3. 患者管理

1. 点击"患者管理"标签页
2. 可以添加、编辑、查询患者信息
3. 查看患者的治疗记录

### 4. 脑电训练

1. 点击"治疗系统"标签页
2. 连接脑电设备
3. 选择患者并设置训练参数
4. 开始运动想象训练

### 5. 电刺激治疗

1. 在治疗系统中切换到"电刺激治疗"标签
2. 连接电刺激设备
3. 设置刺激参数
4. 开始电刺激治疗

### 6. 报告生成

1. 点击"报告分析"标签页
2. 选择患者和时间范围
3. 生成治疗报告
4. 可以预览、打印或导出PDF

### 7. 系统设置

1. 点击"系统设置"标签页
2. 配置系统参数
3. 管理用户权限
4. 进行数据库维护

## 配置说明

### 数据库配置

系统使用SQLite数据库，配置文件位于 `utils/app_config.py`：

```python
DATABASE_CONFIG = {
    'type': 'sqlite',
    'path': PROJECT_ROOT / 'data' / 'nk_system.db',
    'backup_path': PROJECT_ROOT / 'data' / 'backup',
    'auto_backup': True,
    'backup_interval': 24 * 60 * 60,  # 24小时
}
```

### 脑电设备配置

```python
EEG_CONFIG = {
    'serial_port': 'COM8',
    'baud_rate': 115200,
    'sample_rate': 125.0,  # Hz
    'channels': 8,
    'packet_size': 100,
    'timeout': 5.0,
}
```

### 网络通信配置

```python
NETWORK_CONFIG = {
    'http': {
        'base_url': 'http://*************:8082/shdekf/Api/',
        'timeout': 30,
    },
    'udp': {
        'vr_host': '127.0.0.1',
        'vr_port': 8888,
    }
}
```

## 故障排除

### 常见问题

1. **系统无法启动**
   - 检查Python版本是否为3.9+
   - 确认所有依赖包已正确安装
   - 运行 `python test_system.py` 检查系统状态

2. **脑电设备连接失败**
   - 检查串口号是否正确
   - 确认设备驱动已安装
   - 检查串口是否被其他程序占用

3. **数据库错误**
   - 检查数据库文件权限
   - 尝试重新初始化数据库
   - 从备份恢复数据库

4. **界面显示异常**
   - 检查样式文件是否存在
   - 重启系统
   - 检查系统DPI设置

### 日志查看

系统日志位于 `logs/` 目录：
- `nk_system.log`: 主日志文件
- `error.log`: 错误日志文件

可以通过菜单栏的"工具" -> "日志查看"功能查看日志。

## 开发说明

### 代码结构

- `core/`: 核心业务逻辑
- `ui/`: 用户界面组件
- `utils/`: 工具函数和配置
- `resources/`: 静态资源文件

### 扩展开发

1. 新增功能模块时，在相应目录下创建新文件
2. 遵循现有的代码风格和命名规范
3. 添加适当的日志记录和错误处理
4. 编写单元测试验证功能

### 测试

运行完整测试：
```bash
python test_system.py
```

## 技术支持

如有技术问题，请联系开发团队或查看项目文档。

## 版本历史

- v1.0.0: 初始版本，完成从QT系统的迁移
  - 实现所有原有功能
  - 新增日志系统和权限管理
  - 优化用户界面和交互体验

## 许可证

本项目为医疗设备软件，版权所有。未经授权不得复制、分发或修改。
