#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不勾选迁移学习时的多轮训练行为
Test Normal Training Multi-Round Behavior

验证普通训练（从头训练）在多轮训练中的行为

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


def test_normal_training_multi_round():
    """测试普通训练的多轮训练行为"""
    print("🧪 测试普通训练多轮训练行为")
    print("=" * 60)
    
    try:
        # 创建模型
        model = MotorImageryModel("Normal_Multi_Round_Test")
        print("✅ 创建模型成功")
        
        # 禁用迁移学习
        model_info = model.get_model_info()
        model_info.transfer_learning = False
        print("🔧 禁用迁移学习（使用普通训练）")
        
        # 模拟多轮训练
        total_rounds = 3
        samples_per_round = 10
        
        for round_num in range(1, total_rounds + 1):
            print(f"\n📋 第{round_num}轮训练")
            print("-" * 40)
            
            # 添加本轮训练数据
            print(f"🔄 添加第{round_num}轮训练数据...")
            for i in range(samples_per_round):
                data = np.random.randn(8, 250) * 50
                label = i % 2
                model.add_training_data(data, label)
            
            print(f"✅ 添加训练数据: {samples_per_round} 个样本")
            print(f"📊 累积样本数: {len(model.eegnet_model.training_data)}")
            
            # 检查模型状态
            if model.eegnet_model.model is not None:
                print(f"🔍 当前模型状态: 已存在（第{round_num-1}轮训练后）")
            else:
                print(f"🔍 当前模型状态: 未创建")
            
            # 训练配置
            config = TrainingConfig(epochs=3, batch_size=8, learning_rate=0.001)
            
            # 训练模型
            print(f"🚀 开始第{round_num}轮训练...")
            
            def progress_callback(message, progress):
                if progress % 25 == 0:
                    print(f"  [{progress:3d}%] {message}")
            
            start_time = time.time()
            success = model.train_model(config=config, progress_callback=progress_callback)
            training_time = time.time() - start_time
            
            if success:
                print(f"✅ 第{round_num}轮训练完成，耗时: {training_time:.1f}秒")
                
                # 检查训练策略
                model_info = model.get_model_info()
                if hasattr(model_info, 'used_transfer_learning'):
                    if model_info.used_transfer_learning:
                        print(f"⚠️ 意外使用了迁移学习")
                    else:
                        print(f"🔧 确认使用了从头训练")
                else:
                    print(f"❓ 无法确定训练方式")
                
                # 显示性能
                if hasattr(model_info, 'performance') and model_info.performance:
                    perf = model_info.performance
                    print(f"📊 训练准确率: {perf.accuracy*100:.1f}%")
                    print(f"📊 验证准确率: {perf.val_accuracy*100:.1f}%")
                
                # 检查训练轮次
                print(f"📈 训练轮次: {model_info.training_rounds}")
                
                # 关键问题：检查模型是否被重新创建
                print(f"🔍 模型对象ID: {id(model.eegnet_model.model)}")
                
            else:
                print(f"❌ 第{round_num}轮训练失败")
                return False
        
        print(f"\n🎉 多轮训练完成！")
        print(f"📊 最终统计:")
        print(f"   - 总轮次: {total_rounds}")
        print(f"   - 总样本: {len(model.eegnet_model.training_data)}")
        print(f"   - 迁移学习: {'❌ 未使用' if not getattr(model_info, 'used_transfer_learning', True) else '✅ 已使用'}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_recreation_behavior():
    """测试模型重新创建行为"""
    print("\n📋 测试模型重新创建行为")
    print("=" * 60)
    
    try:
        model = MotorImageryModel("Model_Recreation_Test")
        model_info = model.get_model_info()
        model_info.transfer_learning = False
        
        model_ids = []
        
        # 进行3轮训练，记录模型对象ID
        for round_num in range(1, 4):
            print(f"\n🔄 第{round_num}轮训练")
            
            # 添加数据
            for i in range(10):
                data = np.random.randn(8, 250) * 50
                label = i % 2
                model.add_training_data(data, label)
            
            # 记录训练前的模型状态
            before_model_id = id(model.eegnet_model.model) if model.eegnet_model.model else None
            print(f"   训练前模型ID: {before_model_id}")
            
            # 训练
            config = TrainingConfig(epochs=2, batch_size=8, learning_rate=0.001)
            success = model.train_model(config=config)
            
            if success:
                # 记录训练后的模型状态
                after_model_id = id(model.eegnet_model.model)
                print(f"   训练后模型ID: {after_model_id}")
                
                model_ids.append({
                    'round': round_num,
                    'before': before_model_id,
                    'after': after_model_id,
                    'recreated': before_model_id != after_model_id if before_model_id else True
                })
        
        # 分析结果
        print(f"\n📊 模型重新创建分析:")
        for info in model_ids:
            status = "🔄 重新创建" if info['recreated'] else "♻️ 复用现有"
            print(f"   第{info['round']}轮: {status}")
        
        # 结论
        recreated_count = sum(1 for info in model_ids if info['recreated'])
        if recreated_count == len(model_ids):
            print(f"\n🔍 结论: 每轮都重新创建模型（从头训练）")
            print(f"   ❌ 不会累积前面轮次的学习成果")
            print(f"   ❌ 每轮都是全新的模型")
        elif recreated_count == 1:  # 只有第一轮重新创建
            print(f"\n🔍 结论: 只有第一轮创建模型，后续轮次复用")
            print(f"   ✅ 会累积前面轮次的学习成果")
            print(f"   ✅ 在现有模型基础上继续训练")
        else:
            print(f"\n🔍 结论: 混合行为（{recreated_count}/{len(model_ids)}轮重新创建）")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_accumulation():
    """测试数据累积行为"""
    print("\n📋 测试数据累积行为")
    print("=" * 60)
    
    try:
        model = MotorImageryModel("Data_Accumulation_Test")
        model_info = model.get_model_info()
        model_info.transfer_learning = False
        
        data_counts = []
        
        # 进行3轮训练，观察数据累积
        for round_num in range(1, 4):
            print(f"\n🔄 第{round_num}轮训练")
            
            # 添加本轮数据
            samples_this_round = 5
            for i in range(samples_this_round):
                data = np.random.randn(8, 250) * 50
                label = i % 2
                model.add_training_data(data, label)
            
            total_samples = len(model.eegnet_model.training_data)
            print(f"   本轮添加: {samples_this_round} 个样本")
            print(f"   累积总数: {total_samples} 个样本")
            
            data_counts.append({
                'round': round_num,
                'added': samples_this_round,
                'total': total_samples
            })
            
            # 训练（使用少量epoch以节省时间）
            config = TrainingConfig(epochs=1, batch_size=4, learning_rate=0.001)
            model.train_model(config=config)
        
        # 分析数据累积模式
        print(f"\n📊 数据累积分析:")
        expected_total = 0
        for info in data_counts:
            expected_total += info['added']
            if info['total'] == expected_total:
                status = "✅ 正常累积"
            else:
                status = f"⚠️ 异常（期望{expected_total}，实际{info['total']}）"
            print(f"   第{info['round']}轮: {status}")
        
        # 结论
        final_expected = sum(info['added'] for info in data_counts)
        final_actual = data_counts[-1]['total']
        
        if final_actual == final_expected:
            print(f"\n🔍 结论: 数据正常累积")
            print(f"   ✅ 每轮训练都使用所有历史数据")
            print(f"   ✅ 数据不会丢失")
        else:
            print(f"\n🔍 结论: 数据累积异常")
            print(f"   ❌ 期望{final_expected}个样本，实际{final_actual}个")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 普通训练多轮训练行为测试")
    print("=" * 70)
    print("测试目标:")
    print("1. 验证不勾选迁移学习时的多轮训练行为")
    print("2. 检查模型是否会重新创建")
    print("3. 验证数据累积机制")
    print("4. 分析学习成果保持情况")
    print()
    
    # 运行测试
    test1_success = test_normal_training_multi_round()
    test2_success = test_model_recreation_behavior()
    test3_success = test_data_accumulation()
    
    print(f"\n📊 测试结果总结:")
    print(f"   多轮训练测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   模型重新创建测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   数据累积测试: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if test1_success and test2_success and test3_success:
        print("\n🎊 所有测试通过！")
        print("\n📋 普通训练多轮训练行为总结:")
        print("✅ 数据累积：每轮都会累积所有历史数据")
        print("❓ 模型行为：需要根据测试结果确定")
        print("❓ 学习保持：需要根据模型行为确定")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步分析")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
