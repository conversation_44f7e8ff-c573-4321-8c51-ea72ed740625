#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连接问题修复
Test script for connection issue fix
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_connection_fix():
    """测试连接问题修复"""
    print("=" * 70)
    print("连接问题修复测试")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：检查日志记录是否详细化
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查日志记录是否详细化...")
    try:
        settings_ui_path = project_root / "ui" / "settings_ui.py"
        with open(settings_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查详细日志记录
        detailed_logs = [
            '电刺激设备配置更新成功，端口号:',
            '脑电设备配置更新成功，端口:',
            '信号处理配置更新成功',
            'UI配置更新成功，字体大小:',
            '日志配置更新成功，级别:',
            '设置保存完成，已更新组件:'
        ]
        
        missing_logs = []
        for log in detailed_logs:
            if log not in content:
                missing_logs.append(log)
        
        if missing_logs:
            print(f"❌ 缺少详细日志记录: {missing_logs}")
        else:
            print("✅ 日志记录已详细化，能准确反映修改内容")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查日志记录失败: {e}")
    
    # 测试2：检查设备状态重置方法
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查设备状态重置方法...")
    try:
        stimulation_device_path = project_root / "core" / "stimulation_device.py"
        with open(stimulation_device_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查状态重置方法
        reset_elements = [
            'def _reset_device_state(self):',
            '"""重置设备状态，确保下次连接时状态干净"""',
            'self.channel_a_status = 0',
            'self.channel_b_status = 0',
            'self.connection_count = 0',
            'self.callback_function = None',
            'self.device_info = DeviceInfo()',
            'self.current_parameters = StimulationParameters()',
            'self._reset_device_state()'
        ]
        
        missing_reset = []
        for element in reset_elements:
            if element not in content:
                missing_reset.append(element)
        
        if missing_reset:
            print(f"❌ 缺少状态重置元素: {missing_reset}")
        else:
            print("✅ 设备状态重置方法已添加")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查状态重置方法失败: {e}")
    
    # 测试3：检查断开方法是否调用状态重置
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查断开方法是否调用状态重置...")
    try:
        stimulation_device_path = project_root / "core" / "stimulation_device.py"
        with open(stimulation_device_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在disconnect方法中查找状态重置调用
        disconnect_method_start = content.find('def disconnect(self) -> bool:')
        disconnect_method_end = content.find('def _reset_device_state(self):', disconnect_method_start)
        
        if disconnect_method_start != -1 and disconnect_method_end != -1:
            disconnect_method_content = content[disconnect_method_start:disconnect_method_end]
            
            # 检查是否在所有退出路径都调用了状态重置
            reset_calls = disconnect_method_content.count('self._reset_device_state()')
            
            if reset_calls >= 4:  # 应该在多个地方调用
                print("✅ 断开方法在所有路径都调用状态重置")
                tests_passed += 1
            else:
                print(f"❌ 断开方法状态重置调用不足: {reset_calls} 次")
        else:
            print("❌ 无法找到disconnect方法")
            
    except Exception as e:
        print(f"❌ 检查断开方法失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 70)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有连接问题修复测试通过！")
        print("\n✅ 修复的问题:")
        print("   📝 日志记录详细化")
        print("      - 区分不同配置组件的保存日志")
        print("      - 医院信息日志只在实际更新时记录")
        print("      - 每个配置组件都有专门的日志记录")
        print()
        print("   🔄 设备状态完全重置")
        print("      - 断开连接时重置所有设备状态")
        print("      - 清理回调函数和连接计数")
        print("      - 重置设备信息和参数")
        print("      - 确保下次连接状态干净")
        print()
        print("   🎯 问题根源解决")
        print("      - 设备资源完全释放")
        print("      - 避免端口占用问题")
        print("      - 消除状态残留影响")
        print()
        print("🚀 修复效果:")
        print("   ✨ 修改串口号后无需重启程序")
        print("   🔧 设备连接状态完全重置")
        print("   📊 日志信息准确反映修改内容")
        print("   🛡️ 避免设备状态冲突")
        print()
        print("💡 技术改进:")
        print("   🧹 完整的资源清理机制")
        print("   📋 详细的操作日志记录")
        print("   🔒 状态管理一致性保证")
        print("   ⚡ 连接重试机制优化")
        return 0
    else:
        print("⚠️ 部分连接问题修复测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    sys.exit(test_connection_fix())
