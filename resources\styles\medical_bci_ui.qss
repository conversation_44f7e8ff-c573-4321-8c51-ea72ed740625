/* 
脑机接口康复训练系统 - 现代化医疗UI样式表 v3.0
Brain-Computer Interface Medical UI Stylesheet
专为医疗器械设计的现代化界面样式
*/

/* ==================== 全局样式 ==================== */
* {
    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Segoe UI", "Roboto", Arial, sans-serif;
    outline: none;
}

/* 主窗口样式 - 医疗级渐变背景 */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8fafb, stop:0.3 #e8f4f8, stop:0.7 #f0f8ff, stop:1 #f8fafb);
    color: #2c3e50;
}

/* ==================== 菜单栏样式 ==================== */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e3a8a, stop:0.5 #1e40af, stop:1 #1d4ed8);
    color: white;
    border: none;
    padding: 4px;
    font-weight: 600;
    font-size: 13px;
    border-bottom: 2px solid #3b82f6;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 2px 4px;
    font-weight: 600;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

QMenuBar::item:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #06b6d4, stop:1 #0891b2);
}

QMenu {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 8px;
}

QMenu::item {
    padding: 12px 24px;
    border-radius: 8px;
    margin: 2px;
    color: #374151;
    font-weight: 500;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
}

/* ==================== 状态栏样式 ==================== */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f9fafb);
    border-top: 2px solid #3b82f6;
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    padding: 3px;
}

QStatusBar::item {
    border: none;
    padding: 3px 6px;
    margin: 0 3px;
    border-radius: 4px;
    background-color: rgba(59, 130, 246, 0.1);
}

QStatusBar QLabel {
    color: #4b5563;
    font-weight: 500;
    padding: 2px 4px;
    border-radius: 3px;
}

/* ==================== 导航区域样式 ==================== */
QFrame#navigation_frame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1e293b, stop:0.3 #334155, stop:0.7 #475569, stop:1 #1e293b);
    border-right: 4px solid #3b82f6;
    border-radius: 0px 12px 12px 0px;
}

/* ==================== 导航按钮样式 ==================== */
QToolButton {
    background-color: transparent;
    border: none;
    color: #e2e8f0;
    padding: 12px 8px;
    text-align: center;
    border-radius: 8px;
    margin: 4px 6px;
    font-size: 13px;
    font-weight: 600;
    min-height: 32px;
    min-width: 100px;
}

QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

QToolButton:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #06b6d4, stop:1 #0891b2);
    color: white;
    border: 3px solid #ffffff;
    font-weight: 700;
}

QToolButton:disabled {
    color: #64748b;
    background-color: transparent;
}

/* ==================== 按钮样式 ==================== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    min-width: 80px;
    min-height: 28px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1d4ed8, stop:1 #1e40af);
}

QPushButton:disabled {
    background: #d1d5db;
    color: #9ca3af;
}

/* 医疗主要按钮样式 */
QPushButton.primary {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #06b6d4, stop:1 #0891b2);
}

QPushButton.primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0891b2, stop:1 #0e7490);
    border: 2px solid rgba(255, 255, 255, 0.4);
}

/* 危险按钮样式 */
QPushButton.danger {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ef4444, stop:1 #dc2626);
}

QPushButton.danger:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #dc2626, stop:1 #b91c1c);
    border: 2px solid rgba(255, 255, 255, 0.4);
}

/* 成功按钮样式 */
QPushButton.success {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #10b981, stop:1 #059669);
}

QPushButton.success:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #059669, stop:1 #047857);
    border: 2px solid rgba(255, 255, 255, 0.4);
}

/* 警告按钮样式 */
QPushButton.warning {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f59e0b, stop:1 #d97706);
}

QPushButton.warning:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #d97706, stop:1 #b45309);
    border: 2px solid rgba(255, 255, 255, 0.4);
}

/* ==================== 输入框样式 ==================== */
QLineEdit {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: #374151;
    font-weight: 500;
    selection-background-color: #3b82f6;
    selection-color: white;
}

QLineEdit:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 3px;
}

QLineEdit:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

QLineEdit:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    border-color: #d1d5db;
}

/* 只读输入框样式 */
QLineEdit[readOnly="true"] {
    background-color: #f9fafb;
    color: #6b7280;
    border-color: #d1d5db;
}

/* ==================== 文本区域样式 ==================== */
QTextEdit {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px;
    font-size: 13px;
    color: #374151;
    font-weight: 500;
    selection-background-color: #3b82f6;
    selection-color: white;
}

QTextEdit:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 3px;
}

QTextEdit:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

/* 系统日志文本区域特殊样式 */
QTextEdit#system_log {
    background-color: #1e293b;
    color: #e2e8f0;
    border: 3px solid #475569;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 13px;
    line-height: 1.4;
}

/* ==================== 表格样式 ==================== */
QTableWidget {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    gridline-color: #f3f4f6;
    selection-background-color: #3b82f6;
    selection-color: white;
    alternate-background-color: #f8fafc;
    font-size: 12px;
}

QTableWidget::item {
    padding: 8px 6px;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    font-weight: 500;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

QTableWidget::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1e3a8a);
    color: white;
    padding: 8px 6px;
    border: none;
    font-weight: 600;
    font-size: 12px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QHeaderView::section:first {
    border-top-left-radius: 10px;
}

QHeaderView::section:last {
    border-top-right-radius: 10px;
}

/* ==================== 列表样式 ==================== */
QListWidget {
    background-color: white;
    border: 3px solid #e5e7eb;
    border-radius: 12px;
    padding: 8px;
}

QListWidget::item {
    padding: 14px 18px;
    border-radius: 8px;
    margin: 3px;
    color: #374151;
    font-weight: 500;
    border: 1px solid transparent;
}

QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

QListWidget::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

/* ==================== 组合框样式 ==================== */
QComboBox {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 12px 18px;
    font-size: 14px;
    color: #374151;
    font-weight: 500;
    min-width: 160px;
    min-height: 24px;
}

QComboBox:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 3px;
}

QComboBox:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

QComboBox::drop-down {
    border: none;
    width: 35px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
}

QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #6b7280;
    width: 0;
    height: 0;
}

QComboBox QAbstractItemView {
    background-color: white;
    border: 3px solid #e5e7eb;
    border-radius: 10px;
    selection-background-color: #3b82f6;
    selection-color: white;
    padding: 6px;
}

QComboBox QAbstractItemView::item {
    padding: 12px 18px;
    border-radius: 6px;
    margin: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
}

/* ==================== 复选框样式 ==================== */
QCheckBox {
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    spacing: 12px;
}

QCheckBox::indicator {
    width: 22px;
    height: 22px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-color: #3b82f6;
    image: none;
}

QCheckBox::indicator:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

QCheckBox::indicator:disabled {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

/* ==================== 分组框样式 ==================== */
QGroupBox {
    font-weight: 600;
    font-size: 13px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 12px;
    color: #374151;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 4px 12px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e40af, stop:1 #3b82f6);
    color: white;
    border-radius: 6px;
    font-weight: 600;
    font-size: 12px;
}

/* ==================== 进度条样式 ==================== */
QProgressBar {
    border: 3px solid #e5e7eb;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 13px;
    color: #374151;
    background-color: #f9fafb;
    min-height: 24px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #06b6d4, stop:0.5 #3b82f6, stop:1 #2563eb);
    border-radius: 10px;
}

/* ==================== 滚动条样式 ==================== */
QScrollBar:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    width: 16px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 7px;
    min-height: 40px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
    background: transparent;
}

QScrollBar:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    height: 16px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 7px;
    min-width: 40px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
    background: transparent;
}

/* ==================== 标签页样式 ==================== */
QTabWidget::pane {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background-color: white;
    padding: 4px;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    border: 2px solid #e5e7eb;
    padding: 8px 16px;
    margin-right: 3px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 600;
    color: #6b7280;
    font-size: 13px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1e3a8a);
    color: white;
    border-bottom-color: #1e40af;
    font-weight: 700;
}

QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #eff6ff, stop:1 #dbeafe);
    color: #1e40af;
}

/* ==================== 分割器样式 ==================== */
QSplitter::handle {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e5e7eb, stop:0.5 #3b82f6, stop:1 #e5e7eb);
    border-radius: 3px;
}

QSplitter::handle:horizontal {
    width: 6px;
    margin: 6px 0;
}

QSplitter::handle:vertical {
    height: 6px;
    margin: 0 6px;
}

/* ==================== 数值输入框样式 ==================== */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    min-width: 100px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 3px;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-top-right-radius: 8px;
    width: 24px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-bottom-right-radius: 8px;
    width: 24px;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid white;
    width: 0;
    height: 0;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid white;
    width: 0;
    height: 0;
}

/* ==================== 医疗设备状态指示器 ==================== */

/* 设备连接状态标签 */
QLabel.device_status {
    font-weight: 700;
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 8px;
    border: 2px solid transparent;
}

QLabel.device_connected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #10b981, stop:1 #059669);
    color: white;
    border-color: #10b981;
}

QLabel.device_disconnected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ef4444, stop:1 #dc2626);
    color: white;
    border-color: #ef4444;
}

QLabel.device_connecting {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f59e0b, stop:1 #d97706);
    color: white;
    border-color: #f59e0b;
}

/* 实时数据显示区域 */
QFrame.realtime_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e293b, stop:1 #334155);
    border: 4px solid #3b82f6;
    border-radius: 16px;
    padding: 16px;
}

/* 脑电信号显示区域 */
QFrame.eeg_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0f172a, stop:1 #1e293b);
    border: 3px solid #06b6d4;
    border-radius: 12px;
    padding: 12px;
}

/* 控制面板区域 */
QFrame.control_panel {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #ffffff);
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    padding: 20px;
}

/* 电极阻抗显示 */
QLabel.electrode_impedance {
    font-weight: 700;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    min-width: 50px;
    text-align: center;
}

QLabel.impedance_good {
    background-color: #10b981;
    color: white;
}

QLabel.impedance_warning {
    background-color: #f59e0b;
    color: white;
}

QLabel.impedance_bad {
    background-color: #ef4444;
    color: white;
}

QLabel.impedance_unknown {
    background-color: #6b7280;
    color: white;
}

/* 治疗状态指示器 */
QLabel.treatment_status {
    font-weight: 700;
    font-size: 15px;
    padding: 10px 20px;
    border-radius: 10px;
    border: 3px solid transparent;
}

QLabel.treatment_active {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #06b6d4, stop:1 #0891b2);
    color: white;
    border-color: #06b6d4;
}

QLabel.treatment_paused {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f59e0b, stop:1 #d97706);
    color: white;
    border-color: #f59e0b;
}

QLabel.treatment_stopped {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #6b7280, stop:1 #4b5563);
    color: white;
    border-color: #6b7280;
}

/* ==================== 对话框样式 ==================== */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #ffffff, stop:1 #f8fafc);
    border: 3px solid #3b82f6;
    border-radius: 16px;
}

QMessageBox {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #ffffff, stop:1 #f8fafc);
    border: 3px solid #3b82f6;
    border-radius: 16px;
}

QMessageBox QLabel {
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    padding: 12px;
}

/* ==================== 滑块样式 ==================== */
QSlider::groove:horizontal {
    border: 2px solid #e5e7eb;
    height: 10px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    border-radius: 5px;
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border: 3px solid #ffffff;
    width: 24px;
    height: 24px;
    margin: -10px 0;
    border-radius: 15px;
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 5px;
}

/* ==================== 特殊医疗标签样式 ==================== */
QLabel.medical_title {
    font-size: 20px;
    font-weight: 700;
    color: #1e40af;
    padding: 12px 0;
    border-bottom: 3px solid #3b82f6;
    margin-bottom: 12px;
}

QLabel.medical_subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    padding: 6px 0;
}

QLabel.medical_value {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    background-color: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    min-width: 80px;
    text-align: center;
}

/* ==================== 工具提示样式 ==================== */
QToolTip {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e293b, stop:1 #334155);
    color: white;
    border: 3px solid #3b82f6;
    border-radius: 10px;
    padding: 10px 16px;
    font-size: 13px;
    font-weight: 500;
}

/* ==================== 响应式设计支持 ==================== */

/* 小尺寸按钮 */
QPushButton.small {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 28px;
    min-width: 80px;
}

/* 大尺寸按钮 */
QPushButton.large {
    padding: 18px 36px;
    font-size: 16px;
    min-height: 52px;
    min-width: 140px;
}

/* 紧凑布局 */
QWidget.compact {
    margin: 4px;
    padding: 6px;
}

/* 宽松布局 */
QWidget.spacious {
    margin: 12px;
    padding: 20px;
}

/* ==================== 登录界面特殊样式 ==================== */
QWidget#login_widget {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.3 #e8f4f8, stop:0.7 #f0f8ff, stop:1 #f8fafc);
    border-radius: 20px;
}

QLabel#app_title {
    font-size: 28px;
    font-weight: 700;
    color: #1e40af;
    padding: 20px;
}

QLabel#app_subtitle {
    font-size: 16px;
    font-weight: 500;
    color: #6b7280;
    padding: 10px;
}

/* ==================== 医疗警告样式 ==================== */
QFrame.medical_warning {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #fef3c7, stop:1 #fde68a);
    border: 2px solid #f59e0b;
    border-radius: 10px;
    padding: 12px;
}

QFrame.medical_error {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #fee2e2, stop:1 #fecaca);
    border: 2px solid #ef4444;
    border-radius: 10px;
    padding: 12px;
}

QFrame.medical_success {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #d1fae5, stop:1 #a7f3d0);
    border: 2px solid #10b981;
    border-radius: 10px;
    padding: 12px;
}
