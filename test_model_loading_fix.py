#!/usr/bin/env python3
"""
测试模型加载修复
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_loading_fix():
    """测试模型加载修复"""
    print("=" * 60)
    print("测试模型加载修复")
    print("=" * 60)
    
    try:
        print("1. 创建和训练模型...")
        from core.ml_model import MotorImageryModel, ModelManager
        from core.eegnet_model import TrainingConfig
        
        # 创建模型
        original_model = MotorImageryModel("LoadTest_Model")
        print(f"   ✅ 原始模型创建成功: {original_model.model_name}")
        
        # 添加训练数据
        for i in range(20):
            data = np.random.randn(8, 250) * 100
            label = i % 2
            original_model.add_training_data(data, label)
        
        # 训练模型
        config = TrainingConfig(epochs=2, batch_size=4)
        success = original_model.train_model("eegnet", config)
        
        if success:
            print("   ✅ 模型训练成功")
            
            # 测试原始模型的model_info
            original_info = original_model.get_model_info()
            print(f"   - 原始模型信息: {original_info.name}")
            print(f"   - 训练轮次: {original_info.training_rounds}")
            print(f"   - 总样本数: {original_info.total_samples}")
        else:
            print("   ❌ 模型训练失败")
            return False
        
        print("\n2. 保存模型...")
        manager = ModelManager()
        save_success = manager.save_model(original_model, "LoadTest_Model")
        
        if save_success:
            print("   ✅ 模型保存成功")
        else:
            print("   ❌ 模型保存失败")
            return False
        
        print("\n3. 加载模型...")
        loaded_model = manager.load_model("LoadTest_Model")
        
        if loaded_model:
            print("   ✅ 模型加载成功")
            print(f"   - 加载的模型名称: {loaded_model.model_name}")
            print(f"   - 使用EEGNet: {loaded_model.use_eegnet}")
            print(f"   - 已训练: {loaded_model.is_trained}")
        else:
            print("   ❌ 模型加载失败")
            return False
        
        print("\n4. 测试加载后的model_info属性...")
        try:
            # 这是关键测试 - 检查model_info属性
            loaded_info = loaded_model.get_model_info()
            print("   ✅ 加载后model_info访问成功")
            print(f"   - 模型名称: {loaded_info.name}")
            print(f"   - 训练轮次: {loaded_info.training_rounds}")
            print(f"   - 总样本数: {loaded_info.total_samples}")
            
            # 检查是否有model_info属性（直接访问）
            if hasattr(loaded_model, 'model_info'):
                print("   ✅ loaded_model.model_info 属性存在")
            else:
                print("   ⚠️  loaded_model.model_info 属性不存在，但get_model_info()工作正常")
            
        except AttributeError as e:
            print(f"   ❌ model_info属性错误: {e}")
            return False
        
        print("\n5. 测试加载后的预测功能...")
        try:
            test_data = np.random.randn(8, 250) * 100
            
            # 测试预测
            prediction, confidence = loaded_model.predict(test_data)
            print(f"   ✅ 预测成功: 类别={prediction}, 置信度={confidence:.3f}")
            
            # 测试带调整的预测
            adj_pred, adj_conf, status = loaded_model.predict_with_adjustment(test_data)
            print(f"   ✅ 调整预测成功: 类别={adj_pred}, 置信度={adj_conf:.3f}, 状态={status}")
            
        except Exception as e:
            print(f"   ❌ 预测功能测试失败: {e}")
            return False
        
        print("\n6. 测试参数调整功能...")
        try:
            # 获取模型信息
            info = loaded_model.get_model_info()
            
            # 调整决策阈值
            original_threshold = info.decision_threshold
            info.decision_threshold = 0.8
            print(f"   ✅ 决策阈值调整: {original_threshold:.3f} → 0.800")
            
            # 调整难度等级
            loaded_model.adjust_difficulty(4)
            print(f"   ✅ 难度等级调整: {info.difficulty_level}")
            
            # 测试调整后的预测
            test_data = np.random.randn(8, 250) * 100
            prediction, confidence = loaded_model.predict(test_data)
            print(f"   ✅ 参数调整后预测: 类别={prediction}, 置信度={confidence:.3f}")
            
        except Exception as e:
            print(f"   ❌ 参数调整测试失败: {e}")
            return False
        
        print("\n7. 模拟在线分类场景...")
        try:
            # 模拟在线分类中的典型操作序列
            print("   模拟在线分类操作序列:")
            
            # 1. 获取模型信息（在线分类开始时）
            info = loaded_model.get_model_info()
            print(f"     - 获取模型信息: {info.name}")
            
            # 2. 设置参数（用户调整）
            info.decision_threshold = 0.6
            info.confidence_threshold = 0.7
            loaded_model.adjust_difficulty(2)
            print("     - 参数设置完成")
            
            # 3. 连续预测（模拟实时分类）
            for i in range(5):
                test_data = np.random.randn(8, 250) * 100
                prediction, confidence = loaded_model.predict(test_data)
                print(f"     - 预测 {i+1}: 类别={prediction}, 置信度={confidence:.3f}")
            
            print("   ✅ 在线分类场景模拟成功")
            
        except Exception as e:
            print(f"   ❌ 在线分类场景模拟失败: {e}")
            return False
        
        print("\n8. 测试多次加载...")
        try:
            # 测试多次加载同一个模型
            for i in range(3):
                test_loaded = manager.load_model("LoadTest_Model")
                if test_loaded:
                    info = test_loaded.get_model_info()
                    print(f"   ✅ 第{i+1}次加载成功: {info.name}")
                else:
                    print(f"   ❌ 第{i+1}次加载失败")
                    return False
            
        except Exception as e:
            print(f"   ❌ 多次加载测试失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 模型加载修复测试完成！")
        print("=" * 60)
        
        # 总结
        print("\n📊 修复结果:")
        print("✅ 模型加载 - 正常工作")
        print("✅ model_info属性 - 正常访问")
        print("✅ 预测功能 - 正常工作")
        print("✅ 参数调整 - 正常工作")
        print("✅ 在线分类场景 - 完全正常")
        print("✅ 多次加载 - 稳定可靠")
        
        print("\n🎯 修复状态:")
        print("- 模型加载时的model_info属性错误已修复")
        print("- 在线分类功能现在可以正常使用")
        print("- 加载的模型具有完整的功能")
        print("- 系统已准备好进行在线分类")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading_fix()
    if success:
        print("\n🎯 模型加载修复成功，在线分类功能完全正常！")
    else:
        print("\n⚠️  修复仍有问题，需要进一步调试。")
    
    sys.exit(0 if success else 1)
