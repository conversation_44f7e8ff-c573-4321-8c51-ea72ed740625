#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查脚本
Dependencies Check Script

作者: AI Assistant
版本: 1.0.0
"""

import sys
import importlib
from pathlib import Path


def check_dependency(module_name, package_name=None, description=""):
    """检查单个依赖"""
    if package_name is None:
        package_name = module_name
    
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name} - {description}")
        return True
    except ImportError:
        print(f"❌ {package_name} - {description} (缺失)")
        return False


def check_all_dependencies():
    """检查所有依赖"""
    print("NK脑机接口系统 - 依赖检查")
    print("=" * 50)
    
    # 核心依赖（Python标准库）
    print("\n核心依赖 (Python标准库):")
    core_deps = [
        ("sqlite3", "sqlite3", "SQLite数据库"),
        ("logging", "logging", "日志系统"),
        ("pathlib", "pathlib", "路径处理"),
        ("threading", "threading", "多线程"),
        ("datetime", "datetime", "日期时间"),
        ("json", "json", "JSON处理"),
        ("os", "os", "操作系统接口"),
        ("sys", "sys", "系统参数"),
        ("tempfile", "tempfile", "临时文件"),
        ("shutil", "shutil", "文件操作"),
        ("subprocess", "subprocess", "子进程"),
        ("unittest", "unittest", "单元测试"),
    ]
    
    core_success = 0
    for module, package, desc in core_deps:
        if check_dependency(module, package, desc):
            core_success += 1
    
    # 必需的第三方依赖
    print("\n必需的第三方依赖:")
    required_deps = [
        ("PySide6", "PySide6", "Qt界面框架"),
        ("numpy", "numpy", "数值计算"),
        ("matplotlib", "matplotlib", "图形绘制"),
        ("serial", "pyserial", "串口通信"),
    ]
    
    required_success = 0
    for module, package, desc in required_deps:
        if check_dependency(module, package, desc):
            required_success += 1
    
    # 可选的第三方依赖
    print("\n可选的第三方依赖:")
    optional_deps = [
        ("scipy", "scipy", "科学计算"),
        ("pandas", "pandas", "数据处理"),
        ("sklearn", "scikit-learn", "机器学习"),
        ("joblib", "joblib", "并行处理"),
        ("pyttsx3", "pyttsx3", "语音合成"),
        ("tqdm", "tqdm", "进度条"),
        ("pytest", "pytest", "测试框架"),
        ("psutil", "psutil", "系统监控"),
    ]
    
    optional_success = 0
    for module, package, desc in optional_deps:
        if check_dependency(module, package, desc):
            optional_success += 1
    
    # 输出总结
    print("\n" + "=" * 50)
    print("依赖检查总结")
    print("=" * 50)
    
    print(f"核心依赖: {core_success}/{len(core_deps)} ✅")
    print(f"必需依赖: {required_success}/{len(required_deps)} {'✅' if required_success == len(required_deps) else '❌'}")
    print(f"可选依赖: {optional_success}/{len(optional_deps)} ({'✅' if optional_success > len(optional_deps)//2 else '⚠️'})")
    
    # 给出建议
    if core_success < len(core_deps):
        print("\n❌ 核心依赖缺失，请检查Python安装！")
        return False
    
    if required_success < len(required_deps):
        print("\n⚠️  必需依赖缺失，请安装以下包:")
        for module, package, desc in required_deps:
            if not check_dependency(module, package, ""):
                print(f"   pip install {package}")
        print("\n或运行: python install_dependencies.py")
        return False
    
    if optional_success < len(optional_deps) // 2:
        print("\n⚠️  建议安装更多可选依赖以获得完整功能:")
        missing_optional = []
        for module, package, desc in optional_deps:
            try:
                importlib.import_module(module)
            except ImportError:
                missing_optional.append(package)
        
        if missing_optional:
            print(f"   pip install {' '.join(missing_optional)}")
    
    print("\n✅ 依赖检查通过！系统可以正常运行。")
    return True


def check_system_compatibility():
    """检查系统兼容性"""
    print("\n" + "=" * 50)
    print("系统兼容性检查")
    print("=" * 50)
    
    import platform
    
    print(f"操作系统: {platform.system()}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.architecture()[0]}")
    print(f"Python版本: {sys.version}")
    
    # 检查Python版本
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python版本过低，需要Python 3.9或更高版本")
        return False
    
    # 检查是否为Windows
    if not sys.platform.startswith('win'):
        print("⚠️  当前系统不是Windows，可能存在兼容性问题")
    
    print("✅ 系统兼容性检查通过")
    return True


def generate_install_command():
    """生成安装命令"""
    print("\n" + "=" * 50)
    print("安装建议")
    print("=" * 50)
    
    # 检查缺失的包
    required_packages = ["PySide6", "numpy", "matplotlib", "pyserial"]
    optional_packages = ["scipy", "pandas", "scikit-learn", "joblib", "pyttsx3", "tqdm", "pytest"]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        module_name = package.lower()
        if package == "pyserial":
            module_name = "serial"
        elif package == "scikit-learn":
            module_name = "sklearn"
        
        try:
            importlib.import_module(module_name)
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        module_name = package.lower()
        if package == "scikit-learn":
            module_name = "sklearn"
        
        try:
            importlib.import_module(module_name)
        except ImportError:
            missing_optional.append(package)
    
    if missing_required:
        print("必需包安装命令:")
        print(f"pip install {' '.join(missing_required)}")
        print()
    
    if missing_optional:
        print("可选包安装命令:")
        print(f"pip install {' '.join(missing_optional)}")
        print()
    
    if not missing_required and not missing_optional:
        print("✅ 所有依赖都已安装！")
    else:
        print("或者运行自动安装脚本:")
        print("python install_dependencies.py")


def main():
    """主函数"""
    try:
        # 检查系统兼容性
        if not check_system_compatibility():
            return 1
        
        # 检查依赖
        if not check_all_dependencies():
            generate_install_command()
            return 1
        
        # 生成安装建议
        generate_install_command()
        
        return 0
        
    except Exception as e:
        print(f"\n检查过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
