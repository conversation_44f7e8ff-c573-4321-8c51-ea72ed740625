#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动缓存机制
"""

import pickle
import time
from pathlib import Path
import hashlib

class StartupCache:
    """启动缓存"""
    
    def __init__(self, cache_dir="cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "startup_cache.pkl"
        
    def get_cache_key(self, *args):
        """生成缓存键"""
        content = str(args).encode('utf-8')
        return hashlib.md5(content).hexdigest()
    
    def get(self, key):
        """获取缓存"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    return cache_data.get(key)
        except Exception:
            pass
        return None
    
    def set(self, key, value):
        """设置缓存"""
        try:
            cache_data = {}
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
            
            cache_data[key] = {
                'value': value,
                'timestamp': time.time()
            }
            
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            print(f"缓存设置失败: {e}")
    
    def is_valid(self, key, max_age=3600):
        """检查缓存是否有效"""
        cached = self.get(key)
        if cached and 'timestamp' in cached:
            age = time.time() - cached['timestamp']
            return age < max_age
        return False

# 全局缓存实例
startup_cache = StartupCache()
