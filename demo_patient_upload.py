#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
患者数据上传功能演示脚本
Demo Script for Patient Data Upload Functionality

演示如何在患者管理系统中集成数据上传功能

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader, UploadStatus
from core.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def demo_patient_upload():
    """演示患者数据上传功能"""
    print("🏥 患者数据上传功能演示")
    print("=" * 50)
    
    # 1. 初始化组件
    print("\n📋 1. 初始化系统组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    print("✅ 系统组件初始化完成")
    
    # 2. 获取医院信息
    print("\n🏥 2. 获取医院信息...")
    hospital_info = db_manager.get_hospital_info()
    if not hospital_info:
        print("❌ 未找到医院信息")
        return
    
    print(f"   医院名称: {hospital_info.get('hname', 'N/A')}")
    print(f"   医院编号: {hospital_info.get('id', 'N/A')}")
    print(f"   科室: {hospital_info.get('keshi', 'N/A')}")
    print(f"   设备编号: {hospital_info.get('shebeiid', 'N/A')}")
    
    # 3. 创建演示患者数据
    print("\n👤 3. 创建演示患者数据...")
    demo_patient = {
        'bianhao': 888888,
        'name': '演示患者',
        'age': 35,
        'xingbie': '女',
        'cardid': '110101198901011234',
        'zhenduan': '脑卒中后遗症',
        'bingshi': '高血压病史5年',
        'brhc': '右侧',
        'zhuzhi': '张医生',
        'czy': '李操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1)
    }
    
    print(f"   患者编号: {demo_patient['bianhao']}")
    print(f"   患者姓名: {demo_patient['name']}")
    print(f"   年龄: {demo_patient['age']}")
    print(f"   性别: {demo_patient['xingbie']}")
    print(f"   诊断: {demo_patient['zhenduan']}")
    
    # 4. 演示上传过程
    print("\n🌐 4. 演示数据上传过程...")
    
    # 4.1 准备JSON数据
    print("   📝 准备上传数据...")
    json_data = uploader._prepare_json_data(demo_patient, hospital_info)
    print(f"   JSON数据: {json_data}")
    
    # 4.2 执行上传
    print("   📤 执行数据上传...")
    upload_result = uploader.upload_patient_data(demo_patient, hospital_info)
    
    print(f"   上传结果: {'成功' if upload_result.success else '失败'}")
    print(f"   状态: {upload_result.status.value}")
    print(f"   消息: {upload_result.message}")
    
    # 5. 保存到本地数据库
    print("\n💾 5. 保存到本地数据库...")
    
    # 设置上传状态
    demo_patient['status'] = upload_result.status.value
    
    # 检查是否已存在
    existing = db_manager.execute_query(
        "SELECT COUNT(*) as count FROM bingren WHERE bianhao = ?",
        (demo_patient['bianhao'],)
    )
    
    if existing and existing[0]['count'] > 0:
        print("   ⚠️ 患者已存在，跳过保存")
    else:
        success = db_manager.add_patient(demo_patient)
        if success:
            print("   ✅ 患者数据保存成功")
            print(f"   📊 上传状态: {demo_patient['status']}")
        else:
            print("   ❌ 患者数据保存失败")
    
    # 6. 验证保存结果
    print("\n🔍 6. 验证保存结果...")
    saved_patients = db_manager.execute_query(
        "SELECT bianhao, name, status FROM bingren WHERE bianhao = ?",
        (demo_patient['bianhao'],)
    )
    
    if saved_patients:
        patient = saved_patients[0]
        print(f"   ✅ 找到患者: {patient['name']} (编号: {patient['bianhao']})")
        print(f"   📊 上传状态: {patient.get('status', 'N/A')}")
    else:
        print("   ❌ 未找到保存的患者数据")
    
    # 7. 显示功能特点
    print("\n🎯 7. 功能特点总结:")
    print("   ✅ 按照QT程序的JSON格式组织数据")
    print("   ✅ 支持网络连接检测和重试机制")
    print("   ✅ 无论上传成功与否都保存到本地数据库")
    print("   ✅ 在status字段中记录上传状态")
    print("   ✅ 提供详细的日志记录")
    print("   ✅ 完整的错误处理机制")
    
    print("\n" + "=" * 50)
    print("🎉 演示完成！患者数据上传功能已成功集成到系统中。")
    print("=" * 50)

def main():
    """主函数"""
    setup_logging()
    
    try:
        demo_patient_upload()
    except Exception as e:
        print(f"\n💥 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
