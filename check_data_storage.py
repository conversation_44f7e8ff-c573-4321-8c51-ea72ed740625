#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据存储检查工具
Data Storage Check Tool

检查脑电原始数据存储的状态和配置

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from core.eeg_data_loader import EEGDataLoader
from core.training_data_integration import TrainingDataIntegration
from utils.app_config import AppConfig


def check_configuration():
    """检查配置"""
    print("🔧 检查配置...")
    print("-" * 50)
    
    try:
        # 检查原始数据配置
        raw_data_config = AppConfig.get_config('raw_data')
        if raw_data_config:
            print("✅ 原始数据配置存在")
            print(f"   启用状态: {raw_data_config.get('enabled', False)}")
            print(f"   存储格式: {raw_data_config.get('storage_format', 'unknown')}")
            print(f"   压缩启用: {raw_data_config.get('compression', False)}")
            print(f"   质量阈值: {raw_data_config.get('quality_threshold', 0.0)}")
        else:
            print("❌ 原始数据配置不存在")
            return False
        
        # 检查路径配置
        paths_config = AppConfig.get_config('paths')
        if paths_config:
            data_path = paths_config.get('data')
            raw_data_path = paths_config.get('raw_eeg_data')
            print(f"   数据路径: {data_path}")
            print(f"   原始数据路径: {raw_data_path}")
            
            # 检查路径是否存在
            if data_path and Path(data_path).exists():
                print("   ✅ 数据目录存在")
            else:
                print("   ❌ 数据目录不存在")
            
            if raw_data_path and Path(raw_data_path).exists():
                print("   ✅ 原始数据目录存在")
            else:
                print("   ❌ 原始数据目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False


def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖...")
    print("-" * 50)
    
    try:
        # 检查HDF5支持
        import h5py
        print(f"✅ HDF5支持可用 (版本: {h5py.version.version})")
    except ImportError:
        print("❌ HDF5支持不可用，请安装: pip install h5py")
        return False
    
    try:
        # 检查numpy
        import numpy as np
        print(f"✅ NumPy可用 (版本: {np.__version__})")
    except ImportError:
        print("❌ NumPy不可用")
        return False
    
    return True


def check_database():
    """检查数据库"""
    print("\n🗄️ 检查数据库...")
    print("-" * 50)
    
    try:
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 检查原始数据相关表
        tables_to_check = [
            ('eeg_sessions', '脑电会话表'),
            ('eeg_raw_data', '脑电原始数据表'),
            ('bingren', '患者表'),
            ('zhiliao', '治疗记录表')
        ]
        
        for table_name, table_desc in tables_to_check:
            try:
                result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                if result:
                    count = result[0]['count']
                    print(f"   ✅ {table_desc} ({table_name}): {count} 条记录")
                else:
                    print(f"   ⚠️ {table_desc} ({table_name}): 查询失败")
            except Exception as e:
                print(f"   ❌ {table_desc} ({table_name}): 不存在或错误 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False


def check_data_files():
    """检查数据文件"""
    print("\n📁 检查数据文件...")
    print("-" * 50)
    
    try:
        # 获取数据根目录
        paths_config = AppConfig.get_config('paths')
        if not paths_config:
            print("❌ 路径配置不存在")
            return False
        
        raw_data_path = Path(paths_config.get('raw_eeg_data', ''))
        if not raw_data_path.exists():
            print(f"❌ 原始数据目录不存在: {raw_data_path}")
            return False
        
        print(f"✅ 原始数据根目录: {raw_data_path}")
        
        # 检查患者目录
        patients_dir = raw_data_path / 'patients'
        if patients_dir.exists():
            patient_dirs = list(patients_dir.iterdir())
            print(f"   患者目录数量: {len(patient_dirs)}")
            
            total_files = 0
            total_size = 0
            
            for patient_dir in patient_dirs:
                if patient_dir.is_dir():
                    sessions_dir = patient_dir / 'sessions'
                    if sessions_dir.exists():
                        h5_files = list(sessions_dir.rglob('*.h5'))
                        file_count = len(h5_files)
                        
                        dir_size = sum(f.stat().st_size for f in h5_files)
                        total_files += file_count
                        total_size += dir_size
                        
                        print(f"   📂 {patient_dir.name}: {file_count} 个文件, "
                              f"{dir_size / 1024 / 1024:.2f} MB")
                        
                        # 显示最近的文件
                        if h5_files:
                            latest_file = max(h5_files, key=lambda f: f.stat().st_mtime)
                            mtime = datetime.fromtimestamp(latest_file.stat().st_mtime)
                            print(f"      最新文件: {latest_file.name} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
            
            print(f"\n   📊 总计: {total_files} 个文件, {total_size / 1024 / 1024:.2f} MB")
            
            if total_files > 0:
                print("   ✅ 发现数据文件")
                return True
            else:
                print("   ⚠️ 没有发现数据文件")
                return False
        else:
            print("   ❌ 患者目录不存在")
            return False
        
    except Exception as e:
        print(f"❌ 检查数据文件失败: {e}")
        return False


def check_data_statistics():
    """检查数据统计"""
    print("\n📊 检查数据统计...")
    print("-" * 50)
    
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        data_loader = EEGDataLoader(db_manager)
        stats = data_loader.get_data_statistics()
        
        if stats:
            print("✅ 数据统计可用")
            print(f"   总试验数: {stats.get('total_trials', 0)}")
            print(f"   总患者数: {stats.get('total_patients', 0)}")
            print(f"   总会话数: {stats.get('total_sessions', 0)}")
            
            avg_quality = stats.get('avg_quality')
            if avg_quality is not None:
                print(f"   平均质量: {avg_quality:.3f}")
            else:
                print("   平均质量: 无数据")
            
            total_size = stats.get('total_size')
            if total_size:
                print(f"   总数据大小: {total_size / 1024 / 1024:.2f} MB")
            
            # 标签分布
            label_dist = stats.get('label_distribution', {})
            if label_dist:
                print("   标签分布:")
                for label, count in label_dist.items():
                    label_name = "运动想象" if label == "1" else "休息"
                    print(f"     {label_name}: {count}")
            
            # 质量分布
            quality_dist = stats.get('quality_distribution', {})
            if quality_dist:
                print("   质量分布:")
                for quality, count in quality_dist.items():
                    print(f"     {quality}: {count}")
            
            return True
        else:
            print("⚠️ 没有数据统计信息")
            return False
        
    except Exception as e:
        print(f"❌ 检查数据统计失败: {e}")
        return False


def check_integration_status():
    """检查集成状态"""
    print("\n🔗 检查集成状态...")
    print("-" * 50)
    
    try:
        # 检查训练数据集成
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        integration = TrainingDataIntegration(db_manager)
        status = integration.check_data_directory()
        
        if 'error' in status:
            print(f"❌ 集成检查失败: {status['error']}")
            return False
        
        print("✅ 训练数据集成可用")
        print(f"   数据根目录存在: {status.get('data_root_exists', False)}")
        print(f"   数据根路径: {status.get('data_root_path', 'unknown')}")
        print(f"   患者目录数: {len(status.get('patient_dirs', []))}")
        print(f"   总文件数: {status.get('total_files', 0)}")
        print(f"   总大小: {status.get('total_size_mb', 0):.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查集成状态失败: {e}")
        return False


def provide_recommendations():
    """提供建议"""
    print("\n💡 建议和下一步...")
    print("-" * 50)
    
    print("1. 如果数据没有保存，请按照以下步骤操作：")
    print("   a) 确认配置已启用 (raw_data.enabled = true)")
    print("   b) 在训练界面中应用增强补丁")
    print("   c) 进行一次完整的训练流程")
    
    print("\n2. 集成到现有系统：")
    print("   a) 使用增强补丁: python training_integration_patch.py")
    print("   b) 或手动集成: 参考 docs/训练阶段数据存储集成指南.md")
    
    print("\n3. 验证数据存储：")
    print("   a) 进行训练后检查 data/raw_eeg_data/ 目录")
    print("   b) 查看数据库中的 eeg_sessions 和 eeg_raw_data 表")
    print("   c) 使用数据加载器查询数据")
    
    print("\n4. 数据使用：")
    print("   a) 使用 EEGDataLoader 查询和加载数据")
    print("   b) 获取训练数据集用于模型训练")
    print("   c) 分析数据质量和分布")


def main():
    """主函数"""
    print("🔍 脑电原始数据存储检查工具")
    print("=" * 60)
    
    checks = [
        ("配置检查", check_configuration),
        ("依赖检查", check_dependencies),
        ("数据库检查", check_database),
        ("数据文件检查", check_data_files),
        ("数据统计检查", check_data_statistics),
        ("集成状态检查", check_integration_status),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ {check_name}异常: {e}")
    
    # 输出总结
    print(f"\n{'='*60}")
    print(f"检查总结: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有检查通过！原始数据存储功能正常。")
    elif passed >= total * 0.7:
        print("⚠️ 大部分检查通过，但有一些问题需要解决。")
    else:
        print("❌ 多项检查失败，需要进行配置和集成。")
    
    # 提供建议
    provide_recommendations()
    
    return passed >= total * 0.7


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
