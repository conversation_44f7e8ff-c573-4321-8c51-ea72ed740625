#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电刺激设备测试脚本 - 基于原QT程序协议分析的修复版本
测试电刺激设备的连接、参数设置和触发机制
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_stimulation_fixed.log', encoding='utf-8')
        ]
    )

def test_device_connection():
    """测试设备连接"""
    print("=" * 50)
    print("测试1: 设备连接")
    print("=" * 50)

    device = StimulationDevice()

    # 测试连接
    success = device.connect(port_num=7)  # 使用端口7
    print(f"连接结果: {'成功' if success else '失败'}")

    if success:
        print(f"设备状态: {device.status}")
        print(f"连接次数: {device.connection_count}")

        # 获取设备信息
        info = device.get_device_info()
        print(f"设备信息: {info}")

        # 断开连接
        device.disconnect()
        print("设备已断开连接")

    return success

def test_stimulation_preparation():
    """测试刺激准备（新的准备-触发分离模式）"""
    print("\n" + "=" * 50)
    print("测试2: 刺激准备（准备-触发分离模式）")
    print("=" * 50)

    device = StimulationDevice()

    if not device.connect(port_num=7):
        print("设备连接失败，跳过准备测试")
        return False

    try:
        # 准备刺激（设置参数但不启动）
        print("准备通道1刺激，电流10mA...")
        success = device.prepare_stimulation(1, 10.0)
        print(f"准备结果: {'成功' if success else '失败'}")

        if success:
            print("刺激已准备就绪，等待触发...")
            time.sleep(2)

            # 模拟脑电信号触发
            print("模拟脑电信号触发刺激...")
            trigger_success = device.trigger_stimulation(1)
            print(f"触发结果: {'成功' if trigger_success else '失败'}")

            if trigger_success:
                print("刺激已触发，运行3秒...")
                time.sleep(3)

                # 暂停刺激
                print("暂停刺激...")
                pause_success = device.pause_stimulation_new(1)
                print(f"暂停结果: {'成功' if pause_success else '失败'}")

                time.sleep(2)

                # 再次触发
                print("再次触发刺激...")
                device.trigger_stimulation(1)
                time.sleep(2)

                # 停止所有刺激
                print("停止所有刺激...")
                device.stop_all_stimulation()

    except Exception as e:
        print(f"准备测试过程中发生错误: {e}")
        success = False

    finally:
        device.disconnect()

    return success

def test_traditional_stimulation():
    """测试传统刺激模式（兼容性测试）"""
    print("\n" + "=" * 50)
    print("测试3: 传统刺激模式（兼容性）")
    print("=" * 50)

    device = StimulationDevice()

    if not device.connect(port_num=7):
        print("设备连接失败，跳过传统测试")
        return False

    try:
        # 使用传统的启动方式
        print("使用传统方式启动通道1刺激，电流8mA...")
        success = device.start_stimulation_verified(1, 8.0)
        print(f"启动结果: {'成功' if success else '失败'}")

        if success:
            print("刺激运行中，等待3秒...")
            time.sleep(3)

            # 停止刺激
            print("停止刺激...")
            device.stop_all_stimulation()
            print("刺激已停止")

            # 等待2秒后再次测试启动（这是关键测试）
            print("\n🔄 关键测试：停止后重新启动")
            time.sleep(2)

            print("再次启动通道1刺激，电流10mA...")
            success2 = device.start_stimulation_verified(1, 10.0)
            print(f"第二次启动结果: {'成功' if success2 else '失败'}")

            if success2:
                print("第二次刺激运行中，等待2秒...")
                time.sleep(2)

                print("停止第二次刺激...")
                device.stop_all_stimulation()
                print("第二次刺激已停止")

                success = success and success2
            else:
                print("❌ 第二次启动失败 - 这是我们要修复的问题！")
                success = False

    except Exception as e:
        print(f"传统测试过程中发生错误: {e}")
        success = False

    finally:
        device.disconnect()

    return success

def test_multi_channel_control():
    """测试多通道控制"""
    print("\n" + "=" * 50)
    print("测试4: 多通道控制")
    print("=" * 50)

    device = StimulationDevice()

    if not device.connect(port_num=7):
        print("设备连接失败，跳过多通道测试")
        return False

    try:
        # 准备两个通道
        print("准备通道1和通道2...")
        success1 = device.prepare_stimulation(1, 8.0)
        success2 = device.prepare_stimulation(2, 6.0)

        print(f"通道1准备: {'成功' if success1 else '失败'}")
        print(f"通道2准备: {'成功' if success2 else '失败'}")

        if success1 and success2:
            # 交替触发
            print("交替触发两个通道...")

            print("触发通道1...")
            device.trigger_stimulation(1)
            time.sleep(2)

            print("触发通道2...")
            device.trigger_stimulation(2)
            time.sleep(2)

            print("暂停通道1...")
            device.pause_stimulation_new(1)
            time.sleep(1)

            print("暂停通道2...")
            device.pause_stimulation_new(2)
            time.sleep(1)

            print("停止所有刺激...")
            device.stop_all_stimulation()

        success = success1 and success2

    except Exception as e:
        print(f"多通道测试过程中发生错误: {e}")
        success = False

    finally:
        device.disconnect()

    return success

def test_eeg_simulation():
    """测试脑电信号模拟触发"""
    print("\n" + "=" * 50)
    print("测试5: 脑电信号模拟触发")
    print("=" * 50)

    device = StimulationDevice()

    if not device.connect(port_num=7):
        print("设备连接失败，跳过脑电模拟测试")
        return False

    try:
        # 准备刺激
        print("准备刺激系统...")
        if not device.prepare_stimulation(1, 12.0):
            print("刺激准备失败")
            return False

        print("模拟脑电信号检测和触发过程...")

        # 模拟多次触发（类似真实的脑电控制）
        trigger_times = [1, 3, 5, 7, 9]  # 在这些时间点触发
        pause_times = [2, 4, 6, 8]       # 在这些时间点暂停

        start_time = time.time()

        for i in range(10):  # 运行10秒
            current_time = time.time() - start_time

            if int(current_time) in trigger_times:
                print(f"[{current_time:.1f}s] 检测到运动想象信号，触发刺激...")
                device.trigger_stimulation(1)
                trigger_times.remove(int(current_time))

            elif int(current_time) in pause_times:
                print(f"[{current_time:.1f}s] 信号消失，暂停刺激...")
                device.pause_stimulation_new(1)
                pause_times.remove(int(current_time))

            time.sleep(1)

        print("停止所有刺激...")
        device.stop_all_stimulation()
        success = True

    except Exception as e:
        print(f"脑电模拟测试过程中发生错误: {e}")
        success = False

    finally:
        device.disconnect()

    return success

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 50)
    print("测试6: 错误处理")
    print("=" * 50)

    device = StimulationDevice()

    # 测试未连接时的操作
    print("测试未连接时准备刺激...")
    success = device.prepare_stimulation(1, 10.0)
    print(f"结果: {'正确拒绝' if not success else '意外接受'}")

    print("测试未连接时触发刺激...")
    success = device.trigger_stimulation(1)
    print(f"结果: {'正确拒绝' if not success else '意外接受'}")

    # 测试无效参数
    if device.connect(port_num=7):
        print("测试无效电流值...")
        success = device.prepare_stimulation(1, 100.0)  # 超出安全范围
        print(f"结果: {'正确拒绝' if not success else '意外接受'}")

        print("测试无效通道号...")
        success = device.trigger_stimulation(5)  # 无效通道
        print(f"结果: {'正确拒绝' if not success else '意外接受'}")

        device.disconnect()

    return True

def main():
    """主测试函数"""
    setup_logging()

    print("🔬 电刺激设备测试 - 基于原QT程序协议分析的修复版本")
    print("📋 本测试验证准备-触发分离的电刺激控制机制")
    print("🔌 请确保设备已连接到COM7端口")
    input("按回车键开始测试...")

    # 运行所有测试
    tests = [
        ("设备连接", test_device_connection),
        ("刺激准备（准备-触发分离）", test_stimulation_preparation),
        ("传统刺激模式（兼容性）", test_traditional_stimulation),
        ("多通道控制", test_multi_channel_control),
        ("脑电信号模拟触发", test_eeg_simulation),
        ("错误处理", test_error_handling)
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"测试 {test_name} 发生异常: {e}")
            results[test_name] = False

    # 输出测试结果
    print("\n" + "=" * 60)
    print("🏁 测试结果汇总")
    print("=" * 60)

    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    passed = sum(results.values())
    total = len(results)
    print(f"\n📊 总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！电刺激功能修复成功！")
        print("💡 关键改进：")
        print("   - 实现了准备-触发分离的控制机制")
        print("   - 支持基于脑电信号的自动触发")
        print("   - 修正了设备状态切换逻辑")
        print("   - 增强了多通道控制能力")
    else:
        print("⚠️  部分测试失败，请检查：")
        print("   - 设备连接和电源状态")
        print("   - COM7端口是否正确")
        print("   - DLL文件是否完整")
        print("   - 设备驱动是否正常")

if __name__ == "__main__":
    main()
