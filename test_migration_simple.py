#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激治疗页面迁移简单测试脚本
Simple test script for electrical stimulation treatment page migration
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import_only():
    """仅测试导入，不创建GUI实例"""
    print("=" * 60)
    print("电刺激治疗页面迁移测试（仅导入测试）")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：导入TreatmentWidget
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 导入 TreatmentWidget...")
    try:
        from ui.treatment_ui import TreatmentWidget
        print("✅ TreatmentWidget 导入成功")
        tests_passed += 1
        
        # 检查类是否有必需的方法
        required_methods = [
            'add_training_log',
            'add_stimulation_log',
            'toggle_stimulation_connection',
            'start_stimulation',
            'stop_stimulation',
            'on_channel_a_current_changed',
            'on_channel_b_current_changed',
            'create_control_panel',
            'create_display_panel'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(TreatmentWidget, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"⚠️ 缺少方法: {missing_methods}")
        else:
            print("✅ 所有必需的方法都存在")
            
    except Exception as e:
        print(f"❌ 导入失败: {e}")
    
    # 测试2：导入电刺激设备
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 导入 StimulationDevice...")
    try:
        from core.stimulation_device import StimulationDevice
        print("✅ StimulationDevice 导入成功")
        tests_passed += 1
        
        # 检查设备类方法
        required_methods = [
            'connect',
            'disconnect', 
            'start_stimulation',
            'stop_stimulation',
            'set_current',
            'is_connected'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(StimulationDevice, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"⚠️ StimulationDevice 缺少方法: {missing_methods}")
        else:
            print("✅ StimulationDevice 所有必需的方法都存在")
            
    except Exception as e:
        print(f"❌ StimulationDevice 导入失败: {e}")
    
    # 测试3：检查文件结构
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查文件结构...")
    try:
        # 检查treatment_ui.py文件内容
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        if treatment_ui_path.exists():
            with open(treatment_ui_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含电刺激相关内容
            stimulation_keywords = [
                'stimulation_connect_button',
                'stimulation_status_label',
                'channel_a_current',
                'channel_b_current',
                'start_stimulation_button',
                'stop_stimulation_button',
                'add_stimulation_log'
            ]
            
            missing_keywords = []
            for keyword in stimulation_keywords:
                if keyword not in content:
                    missing_keywords.append(keyword)
            
            if missing_keywords:
                print(f"⚠️ 文件中缺少关键词: {missing_keywords}")
            else:
                print("✅ 文件包含所有电刺激相关内容")
                tests_passed += 1
            
            # 检查是否还有create_stimulation_tab方法（应该已删除）
            if 'def create_stimulation_tab(' in content:
                print("⚠️ 仍然存在 create_stimulation_tab 方法，应该已删除")
            else:
                print("✅ create_stimulation_tab 方法已正确删除")
                
        else:
            print("❌ treatment_ui.py 文件不存在")
            
    except Exception as e:
        print(f"❌ 文件结构检查失败: {e}")
    
    # 测试4：检查信号连接
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查信号连接...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查电刺激相关的信号连接
        signal_connections = [
            'stimulation_connect_button.clicked.connect',
            'start_stimulation_button.clicked.connect',
            'stop_stimulation_button.clicked.connect',
            'channel_a_current.valueChanged.connect',
            'channel_b_current.valueChanged.connect'
        ]
        
        missing_connections = []
        for connection in signal_connections:
            if connection not in content:
                missing_connections.append(connection)
        
        if missing_connections:
            print(f"⚠️ 缺少信号连接: {missing_connections}")
        else:
            print("✅ 所有电刺激信号连接都存在")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 信号连接检查失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 60)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！电刺激功能迁移成功！")
        print("\n✅ 迁移完成的功能:")
        print("   - 电刺激设备连接控制")
        print("   - AB通道电流调节")
        print("   - 刺激开始/停止控制")
        print("   - 预刺激功能")
        print("   - 通道状态显示")
        print("   - 刺激日志记录")
        print("   - 所有功能已整合到脑电训练页面")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查迁移结果")
        return 1

if __name__ == "__main__":
    sys.exit(test_import_only())
