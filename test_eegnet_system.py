#!/usr/bin/env python3
"""
EEGNet系统综合测试
Comprehensive Test for EEGNet System Integration
"""

import sys
import os
import numpy as np
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_eegnet_system():
    """测试完整的EEGNet系统集成"""
    print("=" * 70)
    print("EEGNet系统综合测试")
    print("=" * 70)
    
    test_results = {}
    
    try:
        print("🔍 系统环境检查...")
        
        # 1. 检查TensorFlow
        try:
            import tensorflow as tf
            print(f"   ✅ TensorFlow: {tf.__version__}")
            test_results['tensorflow'] = True
        except ImportError:
            print("   ❌ TensorFlow: 未安装")
            test_results['tensorflow'] = False
        
        # 2. 检查其他依赖
        try:
            import numpy as np
            print(f"   ✅ NumPy: {np.__version__}")
            test_results['numpy'] = True
        except ImportError:
            print("   ❌ NumPy: 未安装")
            test_results['numpy'] = False
        
        print("\n📦 模块导入测试...")
        
        # 3. 测试核心模块导入
        modules_to_test = [
            ('core.ml_model', 'ML模型接口适配器'),
            ('core.eegnet_model', 'EEGNet深度学习模型'),
            ('core.dataset_manager', '数据集管理器'),
            ('core.transfer_learning', '迁移学习模块'),
        ]
        
        for module_name, description in modules_to_test:
            try:
                __import__(module_name)
                print(f"   ✅ {description}")
                test_results[module_name] = True
            except ImportError as e:
                print(f"   ❌ {description}: {e}")
                test_results[module_name] = False
        
        print("\n🧪 功能测试...")
        
        # 4. 测试ML接口适配器
        print("\n4.1 ML接口适配器测试:")
        try:
            from core.ml_model import MotorImageryModel, ModelManager
            
            # 创建模型
            model = MotorImageryModel("System_Test_Model")
            manager = ModelManager()
            
            print(f"     ✅ 模型创建成功，使用EEGNet: {model.use_eegnet}")
            print(f"     ✅ 管理器创建成功，使用EEGNet: {manager.use_eegnet}")
            
            # 添加测试数据
            for i in range(10):
                data = np.random.randn(8, 250) * 100
                label = i % 2
                model.add_training_data(data, label)
            
            print(f"     ✅ 添加训练数据: {len(model.training_data)} 个样本")
            
            # 获取模型信息
            info = model.get_model_info()
            print(f"     ✅ 模型信息: {info.name}, 样本数: {info.total_samples}")
            
            test_results['ml_adapter'] = True
            
        except Exception as e:
            print(f"     ❌ ML接口适配器测试失败: {e}")
            test_results['ml_adapter'] = False
        
        # 5. 测试实时参数调整
        print("\n4.2 实时参数调整测试:")
        try:
            if 'model' in locals():
                # 测试参数调整
                original_threshold = model.get_model_info().decision_threshold
                
                # 调整决策阈值
                model.get_model_info().decision_threshold = 0.7
                print(f"     ✅ 决策阈值调整: {original_threshold:.3f} → 0.700")
                
                # 调整难度等级
                model.adjust_difficulty(3)
                print(f"     ✅ 难度等级调整: {model.get_model_info().difficulty_level}")
                
                # 测试预测（如果可能）
                test_data = np.random.randn(8, 250) * 100
                prediction, confidence = model.predict(test_data)
                print(f"     ✅ 预测测试: 类别={prediction}, 置信度={confidence:.3f}")
                
                test_results['realtime_adjustment'] = True
            else:
                test_results['realtime_adjustment'] = False
                
        except Exception as e:
            print(f"     ❌ 实时参数调整测试失败: {e}")
            test_results['realtime_adjustment'] = False
        
        # 6. 测试数据集管理
        print("\n4.3 数据集管理测试:")
        try:
            from core.dataset_manager import DatasetManager
            
            dataset_manager = DatasetManager()
            datasets = dataset_manager.list_available_datasets()
            
            print(f"     ✅ 数据集管理器创建成功")
            print(f"     ✅ 可用数据集: {len(datasets)} 个")
            
            for name, info in list(datasets.items())[:2]:  # 只显示前2个
                print(f"       - {name}: {info.subjects}个受试者, {info.channels}通道")
            
            test_results['dataset_manager'] = True
            
        except Exception as e:
            print(f"     ❌ 数据集管理测试失败: {e}")
            test_results['dataset_manager'] = False
        
        # 7. 测试治疗系统集成
        print("\n4.4 治疗系统集成测试:")
        try:
            # 检查治疗系统是否能正确导入ML模型
            from ui.treatment_ui import TreatmentWidget
            print(f"     ✅ 治疗系统界面导入成功")
            
            # 检查算法选择是否包含EEGNet
            # 这里只是检查类是否能导入，不实际创建UI
            test_results['treatment_integration'] = True
            
        except Exception as e:
            print(f"     ❌ 治疗系统集成测试失败: {e}")
            test_results['treatment_integration'] = False
        
        # 8. 性能测试
        print("\n4.5 性能测试:")
        try:
            if 'model' in locals():
                # 测试预测性能
                start_time = time.time()
                
                for i in range(10):
                    test_data = np.random.randn(8, 250) * 100
                    prediction, confidence = model.predict(test_data)
                
                end_time = time.time()
                avg_time = (end_time - start_time) / 10
                
                print(f"     ✅ 平均预测时间: {avg_time*1000:.1f}ms")
                
                # 测试参数调整性能
                start_time = time.time()
                
                for i in range(10):
                    model.get_model_info().decision_threshold = 0.3 + i * 0.05
                    model.adjust_difficulty((i % 5) + 1)
                
                end_time = time.time()
                avg_adjust_time = (end_time - start_time) / 10
                
                print(f"     ✅ 平均参数调整时间: {avg_adjust_time*1000:.1f}ms")
                
                test_results['performance'] = True
            else:
                test_results['performance'] = False
                
        except Exception as e:
            print(f"     ❌ 性能测试失败: {e}")
            test_results['performance'] = False
        
        # 9. 生成测试报告
        print("\n" + "=" * 70)
        print("📊 测试报告")
        print("=" * 70)
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过 ({passed_tests/total_tests*100:.1f}%)")
        
        print("\n详细结果:")
        test_descriptions = {
            'tensorflow': 'TensorFlow环境',
            'numpy': 'NumPy环境',
            'core.ml_model': 'ML模型接口适配器',
            'core.eegnet_model': 'EEGNet深度学习模型',
            'core.dataset_manager': '数据集管理器',
            'core.transfer_learning': '迁移学习模块',
            'ml_adapter': 'ML接口适配器功能',
            'realtime_adjustment': '实时参数调整',
            'dataset_manager': '数据集管理功能',
            'treatment_integration': '治疗系统集成',
            'performance': '性能测试'
        }
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            description = test_descriptions.get(test_name, test_name)
            print(f"  {status} {description}")
        
        # 10. 系统状态评估
        print("\n🎯 系统状态评估:")
        
        if test_results.get('tensorflow', False) and test_results.get('core.eegnet_model', False):
            print("  ✅ EEGNet深度学习功能：可用")
        else:
            print("  ⚠️  EEGNet深度学习功能：部分可用（依赖问题）")
        
        if test_results.get('ml_adapter', False):
            print("  ✅ 接口兼容性：良好")
        else:
            print("  ❌ 接口兼容性：有问题")
        
        if test_results.get('realtime_adjustment', False):
            print("  ✅ 实时调整功能：正常")
        else:
            print("  ❌ 实时调整功能：异常")
        
        if test_results.get('treatment_integration', False):
            print("  ✅ 治疗系统集成：成功")
        else:
            print("  ❌ 治疗系统集成：失败")
        
        # 11. 建议
        print("\n💡 使用建议:")
        
        if not test_results.get('tensorflow', False):
            print("  - 安装TensorFlow以启用完整的EEGNet功能")
        
        if test_results.get('ml_adapter', False):
            print("  - 系统已准备好使用EEGNet进行运动想象分类")
        
        if test_results.get('realtime_adjustment', False):
            print("  - 可以使用实时参数调整功能优化分类性能")
        
        if test_results.get('dataset_manager', False):
            print("  - 可以使用公开数据集进行迁移学习")
        
        print("\n" + "=" * 70)
        print("🎉 EEGNet系统综合测试完成！")
        print("=" * 70)
        
        return passed_tests >= total_tests * 0.7  # 70%通过率认为成功
        
    except Exception as e:
        print(f"\n❌ 系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_eegnet_system()
    sys.exit(0 if success else 1)
