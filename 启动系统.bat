@echo off
chcp 65001 >nul
title NK脑机接口系统启动器

echo ========================================
echo NK脑机接口系统 v1.0.0
echo Medical-grade Brain-Computer Interface System
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python 3.9或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 请选择操作：
echo 1. 检查依赖包
echo 2. 安装依赖包
echo 3. 核心功能测试
echo 4. 完整系统测试
echo 5. 快速功能演示
echo 6. 启动完整系统
echo 7. 退出
echo.

set /p choice=请输入选择 (1-7):

if "%choice%"=="1" goto check_deps
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto run_core_test
if "%choice%"=="4" goto run_full_test
if "%choice%"=="5" goto run_demo
if "%choice%"=="6" goto run_system
if "%choice%"=="7" goto exit
goto invalid_choice

:check_deps
echo.
echo ========================================
echo 正在检查依赖包...
echo ========================================
python check_dependencies.py
pause
goto menu

:install_deps
echo.
echo ========================================
echo 正在安装依赖包...
echo ========================================
python install_dependencies.py
pause
goto menu

:run_core_test
echo.
echo ========================================
echo 正在运行核心功能测试...
echo ========================================
python test_core_functions.py
pause
goto menu

:run_full_test
echo.
echo ========================================
echo 正在运行完整系统测试...
echo ========================================
python test_full_system.py
pause
goto menu

:run_demo
echo.
echo ========================================
echo 正在运行快速演示...
echo ========================================
python quick_start.py
pause
goto menu

:run_system
echo.
echo ========================================
echo 正在启动NK脑机接口系统...
echo ========================================
python main.py
pause
goto menu

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
echo.
goto menu

:menu
echo.
echo ========================================
echo 请选择操作：
echo 1. 检查依赖包
echo 2. 安装依赖包
echo 3. 核心功能测试
echo 4. 完整系统测试
echo 5. 快速功能演示
echo 6. 启动完整系统
echo 7. 退出
echo.
set /p choice=请输入选择 (1-7):
if "%choice%"=="1" goto check_deps
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto run_core_test
if "%choice%"=="4" goto run_full_test
if "%choice%"=="5" goto run_demo
if "%choice%"=="6" goto run_system
if "%choice%"=="7" goto exit
goto invalid_choice

:exit
echo.
echo 感谢使用NK脑机接口系统！
echo.
pause
exit /b 0
