#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的报告功能测试
Simple Report Function Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🔧 测试模块导入...")
    
    try:
        from core.database_manager import DatabaseManager
        print("✅ DatabaseManager导入成功")
        
        from core.report_generator import ReportGenerator
        print("✅ ReportGenerator导入成功")
        
        from core.chart_generator import ChartGenerator
        print("✅ ChartGenerator导入成功")
        
        from core.pdf_exporter import PDFExporter
        print("✅ PDFExporter导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_database():
    """测试数据库"""
    print("\n🗄️ 测试数据库...")
    
    try:
        from core.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.initialize():
            print("✅ 数据库初始化成功")
            
            # 测试查询患者
            patients = db_manager.get_patients()
            print(f"✅ 查询到 {len(patients)} 个患者")
            
            return db_manager
        else:
            print("❌ 数据库初始化失败")
            return None
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return None

def test_report_generation():
    """测试报告生成"""
    print("\n📊 测试报告生成...")
    
    try:
        from core.database_manager import DatabaseManager
        from core.report_generator import ReportGenerator
        from datetime import datetime, timedelta
        
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        generator = ReportGenerator(db_manager)
        
        # 获取第一个患者
        patients = db_manager.get_patients()
        if patients:
            patient_id = patients[0]['bianhao']
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            end_date = datetime.now().strftime('%Y-%m-%d')
            
            report = generator.generate_personal_report(patient_id, start_date, end_date, "综合报告")
            
            if report and len(report) > 100:
                print("✅ 个人报告生成成功")
                print(f"📄 报告长度: {len(report)} 字符")
                return True
            else:
                print("❌ 个人报告生成失败或内容过短")
                return False
        else:
            print("❌ 没有患者数据")
            return False
            
    except Exception as e:
        print(f"❌ 报告生成测试失败: {e}")
        return False

def test_chart_generation():
    """测试图表生成"""
    print("\n📈 测试图表生成...")
    
    try:
        from core.chart_generator import ChartGenerator
        
        chart_generator = ChartGenerator()
        
        # 创建测试数据
        test_data = [
            {'rq': '2024-12-01', 'defen': 75, 'zlms': '良'},
            {'rq': '2024-12-02', 'defen': 80, 'zlms': '优'},
            {'rq': '2024-12-03', 'defen': 70, 'zlms': '良'},
            {'rq': '2024-12-04', 'defen': 85, 'zlms': '优'},
            {'rq': '2024-12-05', 'defen': 78, 'zlms': '良'},
        ]
        
        # 测试趋势图
        trend_chart = chart_generator.generate_treatment_trend_chart(test_data)
        if trend_chart and trend_chart.startswith('data:image/png;base64,'):
            print("✅ 治疗趋势图生成成功")
        else:
            print("❌ 治疗趋势图生成失败")
            return False
        
        # 测试分布图
        dist_chart = chart_generator.generate_score_distribution_chart(test_data)
        if dist_chart and dist_chart.startswith('data:image/png;base64,'):
            print("✅ 得分分布图生成成功")
        else:
            print("❌ 得分分布图生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成测试失败: {e}")
        return False

def test_pdf_export():
    """测试PDF导出"""
    print("\n📄 测试PDF导出...")
    
    try:
        from core.pdf_exporter import PDFExporter
        
        pdf_exporter = PDFExporter()
        
        if pdf_exporter.is_available():
            print("✅ PDF导出功能可用")
            return True
        else:
            print("⚠️ PDF导出功能不可用（需要安装reportlab库）")
            print("💡 运行: pip install reportlab")
            return False
            
    except Exception as e:
        print(f"❌ PDF导出测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简化测试报告分析功能")
    print("=" * 50)
    
    success_count = 0
    total_tests = 5
    
    # 测试导入
    if test_imports():
        success_count += 1
    
    # 测试数据库
    if test_database():
        success_count += 1
    
    # 测试报告生成
    if test_report_generation():
        success_count += 1
    
    # 测试图表生成
    if test_chart_generation():
        success_count += 1
    
    # 测试PDF导出
    if test_pdf_export():
        success_count += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count >= 4:  # PDF可选
        print("🎉 第一阶段核心功能测试成功!")
        print("\n✅ 已完成功能:")
        print("1. 个人综合报告生成")
        print("2. 基础图表生成（趋势图、分布图）")
        print("3. 数据库集成")
        print("4. 模块化架构")
        
        if success_count == 5:
            print("5. PDF导出功能")
        else:
            print("⚠️ PDF导出需要安装reportlab库")
        
        print("\n🎯 下一步:")
        print("- 在主程序中测试报告分析界面")
        print("- 验证用户界面交互")
        print("- 测试实际患者数据")
        
        return True
    else:
        print("❌ 测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    main()
