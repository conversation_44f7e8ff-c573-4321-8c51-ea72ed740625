2025-05-29 16:52:07,380 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:52:07,382 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 1
2025-05-29 16:52:07,384 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 16:52:07,384 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 16:52:07,384 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-05-29 16:52:07,385 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:52:07,385 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:52:07,389 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:52:07,390 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 16:52:47,092 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:52:47,093 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 1
2025-05-29 16:52:47,095 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 16:52:47,095 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 16:52:47,095 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-05-29 16:52:47,095 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:52:47,095 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=刺激中
2025-05-29 16:52:47,096 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 16:52:47,096 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=暂停
2025-05-29 16:52:47,096 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=刺激中
2025-05-29 16:52:47,096 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=暂停
2025-05-29 16:52:47,096 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:52:47,099 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:52:47,099 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 16:54:25,932 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:54:25,934 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 1
2025-05-29 16:54:25,936 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 16:54:25,936 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 16:54:25,936 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-05-29 16:54:25,938 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 16:54:25,938 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=刺激中
2025-05-29 16:54:25,938 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 16:54:25,939 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=暂停
2025-05-29 16:54:25,939 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=刺激中
2025-05-29 16:54:25,939 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=暂停
2025-05-29 16:54:25,939 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:54:25,942 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 16:54:25,942 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 17:05:13,875 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 17:05:13,876 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 1
2025-05-29 17:05:13,877 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 17:05:13,877 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 17:05:13,878 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-05-29 17:05:13,879 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 17:05:13,879 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 17:05:13,879 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=暂停
2025-05-29 17:05:13,879 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 17:05:13,879 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=停止
2025-05-29 17:05:13,880 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=暂停
2025-05-29 17:05:13,880 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=停止
2025-05-29 17:05:13,880 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 17:05:13,883 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 17:05:13,884 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 17:06:58,830 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 17:06:58,831 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 1
2025-05-29 17:06:58,832 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 17:06:58,832 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 17:06:58,832 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-05-29 17:06:58,833 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 17:06:58,833 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=正常工作
2025-05-29 17:06:58,834 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 17:06:58,834 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=停止
2025-05-29 17:06:58,834 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 17:06:58,834 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=暂停
2025-05-29 17:06:58,834 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchDeviceState
2025-05-29 17:06:58,834 - core.stimulation_device - INFO - 诊断通道1状态: 界面=刺激中, 实际=电流调节
2025-05-29 17:06:58,835 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=正常工作
2025-05-29 17:06:58,835 - core.stimulation_device - INFO - 诊断通道1状态: 界面=已连接, 实际=停止
2025-05-29 17:06:58,835 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 17:06:58,839 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 17:06:58,840 - core.stimulation_device - INFO - 电刺激设备断开成功
