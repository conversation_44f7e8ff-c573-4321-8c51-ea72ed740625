#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电流设置修复效果
验证电流值不再乘以10，直接使用mA值
"""

import sys
import time
import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from core.stimulation_device import StimulationDevice

def test_current_setting_fix():
    """测试电流设置修复效果"""
    print("🧪 测试电流设置修复效果")
    print("=" * 50)
    print("根据协议文档，调节步长为1mA时，电流值应直接使用mA值，不再乘以10")
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    app = QApplication(sys.argv)
    
    try:
        # 创建设备实例
        device = StimulationDevice()
        
        print("📱 步骤1: 连接设备...")
        if device.connect(7):
            print("✅ 设备连接成功")
            
            print("\n📊 步骤2: 测试电流设置...")
            print("   观察日志：应该不再显示'内部值'信息")
            
            # 测试不同电流值
            test_currents = [1.0, 3.0, 5.0, 10.0, 15.0]
            
            def test_current_values():
                try:
                    for i, current in enumerate(test_currents):
                        print(f"\n   测试 {i+1}: 设置A通道电流为 {current}mA")
                        
                        # 设置电流
                        success = device.set_current(1, current)
                        if success:
                            print(f"   ✅ 电流设置成功: {current}mA")
                        else:
                            print(f"   ❌ 电流设置失败: {current}mA")
                        
                        # 等待一段时间
                        if i < len(test_currents) - 1:
                            QTimer.singleShot((i + 1) * 1500, lambda: None)
                    
                    # 结束测试
                    QTimer.singleShot(len(test_currents) * 1500 + 1000, lambda: app.quit())
                    
                except Exception as e:
                    print(f"❌ 测试失败: {e}")
                    app.quit()
            
            QTimer.singleShot(500, test_current_values)
            
            # 运行事件循环
            app.exec()
            
            print("\n📊 步骤3: 断开设备...")
            device.disconnect()
            print("✅ 设备断开成功")
            
        else:
            print("❌ 设备连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎯 测试完成")
    print("检查结果：")
    print("✅ 如果日志显示'通道 1 电流设置成功: XmA'（不再有内部值），说明修复成功")
    print("✅ 电流值应该直接传递给设备，不再乘以10")
    print("✅ 这样设置3mA时，设备接收到的就是3，而不是30")

if __name__ == "__main__":
    test_current_setting_fix()
