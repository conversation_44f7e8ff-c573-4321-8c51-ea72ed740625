#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能优化效果
对比优化前后的响应速度
"""

import sys
import time
import logging
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PySide6.QtCore import QTimer

from core.stimulation_device import StimulationDevice

class PerformanceOptimizationTest(QWidget):
    def __init__(self):
        super().__init__()
        self.device = None
        self.test_results = []
        
        self.init_ui()
        self.init_device()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("预刺激性能优化测试")
        self.setGeometry(100, 100, 600, 500)
        
        layout = QVBoxLayout()
        
        # 状态显示
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_original_button = QPushButton("测试原始方法性能")
        self.test_original_button.clicked.connect(self.test_original_method)
        layout.addWidget(self.test_original_button)
        
        self.test_optimized_button = QPushButton("测试优化方法性能")
        self.test_optimized_button.clicked.connect(self.test_optimized_method)
        layout.addWidget(self.test_optimized_button)
        
        self.test_continuous_button = QPushButton("测试连续调节性能")
        self.test_continuous_button.clicked.connect(self.test_continuous_adjustment)
        layout.addWidget(self.test_continuous_button)
        
        # 结果显示
        self.result_display = QTextEdit()
        layout.addWidget(QLabel("测试结果:"))
        layout.addWidget(self.result_display)
        
        self.setLayout(layout)
        
    def init_device(self):
        """初始化设备"""
        try:
            self.device = StimulationDevice()
            if self.device.connect(7):
                self.status_label.setText("✅ 设备连接成功")
                self.add_result("设备连接成功")
            else:
                self.status_label.setText("❌ 设备连接失败")
                self.add_result("设备连接失败")
        except Exception as e:
            self.status_label.setText(f"❌ 设备初始化失败: {e}")
            self.add_result(f"设备初始化失败: {e}")
    
    def add_result(self, message):
        """添加测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        result_message = f"[{timestamp}] {message}"
        self.test_results.append(result_message)
        self.result_display.append(result_message)
        print(result_message)
    
    def test_original_method(self):
        """测试原始预刺激方法性能"""
        if not self.device or not self.device.is_connected():
            self.add_result("❌ 设备未连接，无法测试")
            return
        
        self.add_result("🧪 开始测试原始预刺激方法性能...")
        self.test_original_button.setEnabled(False)
        
        # 测试10次原始方法调用
        times = []
        for i in range(10):
            start_time = time.time()
            
            # 使用原始的start_pre_stimulation方法
            success = self.device.start_pre_stimulation(1, 10.0 + i, 1.0)  # 1秒预刺激
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000  # 转换为毫秒
            times.append(elapsed)
            
            self.add_result(f"   第{i+1}次: {elapsed:.1f}ms {'✅' if success else '❌'}")
            
            # 等待预刺激结束
            time.sleep(1.2)
        
        # 计算统计数据
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        self.add_result(f"📊 原始方法性能统计:")
        self.add_result(f"   平均耗时: {avg_time:.1f}ms")
        self.add_result(f"   最短耗时: {min_time:.1f}ms")
        self.add_result(f"   最长耗时: {max_time:.1f}ms")
        
        self.test_original_button.setEnabled(True)
    
    def test_optimized_method(self):
        """测试优化预刺激方法性能"""
        if not self.device or not self.device.is_connected():
            self.add_result("❌ 设备未连接，无法测试")
            return
        
        self.add_result("🚀 开始测试优化预刺激方法性能...")
        self.test_optimized_button.setEnabled(False)
        
        # 测试10次优化方法调用
        times = []
        for i in range(10):
            start_time = time.time()
            
            # 使用优化的fast_pre_stimulation方法
            success = self.device.fast_pre_stimulation(1, 10.0 + i, 1.0)  # 1秒预刺激
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000  # 转换为毫秒
            times.append(elapsed)
            
            self.add_result(f"   第{i+1}次: {elapsed:.1f}ms {'✅' if success else '❌'}")
            
            # 等待预刺激结束
            time.sleep(1.2)
        
        # 计算统计数据
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        self.add_result(f"📊 优化方法性能统计:")
        self.add_result(f"   平均耗时: {avg_time:.1f}ms")
        self.add_result(f"   最短耗时: {min_time:.1f}ms")
        self.add_result(f"   最长耗时: {max_time:.1f}ms")
        
        self.test_optimized_button.setEnabled(True)
    
    def test_continuous_adjustment(self):
        """测试连续调节性能（模拟用户快速调节）"""
        if not self.device or not self.device.is_connected():
            self.add_result("❌ 设备未连接，无法测试")
            return
        
        self.add_result("⚡ 开始测试连续调节性能...")
        self.test_continuous_button.setEnabled(False)
        
        # 模拟用户从13mA快速调节到34mA（21次调节）
        start_current = 13
        end_current = 34
        total_start_time = time.time()
        
        times = []
        for current in range(start_current, end_current + 1):
            adjustment_start = time.time()
            
            # 使用优化方法
            success = self.device.fast_pre_stimulation(1, float(current), 0.5)  # 0.5秒预刺激
            
            adjustment_end = time.time()
            elapsed = (adjustment_end - adjustment_start) * 1000
            times.append(elapsed)
            
            self.add_result(f"   调节到{current}mA: {elapsed:.1f}ms {'✅' if success else '❌'}")
            
            # 模拟用户调节间隔（约200ms）
            time.sleep(0.2)
        
        total_end_time = time.time()
        total_elapsed = (total_end_time - total_start_time) * 1000
        
        # 计算统计数据
        avg_time = sum(times) / len(times)
        total_adjustments = len(times)
        
        self.add_result(f"📊 连续调节性能统计:")
        self.add_result(f"   总调节次数: {total_adjustments}")
        self.add_result(f"   总耗时: {total_elapsed:.1f}ms")
        self.add_result(f"   平均每次调节: {avg_time:.1f}ms")
        self.add_result(f"   理论最快完成时间: {total_adjustments * 50:.1f}ms (50ms/次)")
        self.add_result(f"   实际完成时间: {total_elapsed:.1f}ms")
        
        # 计算性能提升
        if avg_time > 0:
            theoretical_best = 50  # 理论最佳50ms每次
            improvement = ((avg_time - theoretical_best) / avg_time) * 100
            if improvement > 0:
                self.add_result(f"   还有{improvement:.1f}%的优化空间")
            else:
                self.add_result(f"   已达到理论最佳性能")
        
        self.test_continuous_button.setEnabled(True)
    
    def compare_methods(self):
        """对比两种方法的性能"""
        self.add_result("📈 性能对比分析:")
        
        # 这里可以添加更详细的对比分析
        # 基于之前的测试结果进行对比
        
        self.add_result("优化建议:")
        self.add_result("1. 使用fast_pre_stimulation方法可以显著提升响应速度")
        self.add_result("2. 异步处理避免UI阻塞，提升用户体验")
        self.add_result("3. 减少不必要的设备状态设置，降低通信开销")
        self.add_result("4. 批量处理连续调节，进一步优化性能")

def main():
    app = QApplication(sys.argv)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_widget = PerformanceOptimizationTest()
    test_widget.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
