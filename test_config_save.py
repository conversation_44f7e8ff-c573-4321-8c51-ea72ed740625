#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置保存功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.app_config import AppConfig

def test_config_save_load():
    """测试配置保存和加载功能"""
    print("=" * 60)
    print("配置保存和加载功能测试")
    print("=" * 60)
    
    try:
        # 显示当前配置
        print(f"当前电刺激端口号: {AppConfig.STIMULATION_CONFIG['port_num']}")
        
        # 修改配置
        original_port = AppConfig.STIMULATION_CONFIG['port_num']
        test_port = 7
        AppConfig.STIMULATION_CONFIG['port_num'] = test_port
        
        print(f"修改端口号为: {test_port}")
        
        # 保存配置
        if AppConfig.save_user_config():
            print("✓ 配置保存成功")
        else:
            print("✗ 配置保存失败")
            return False
        
        # 检查配置文件是否存在
        if AppConfig.CONFIG_FILE.exists():
            print(f"✓ 配置文件已创建: {AppConfig.CONFIG_FILE}")
            
            # 显示配置文件内容
            with open(AppConfig.CONFIG_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"配置文件内容预览:")
                print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print("✗ 配置文件未创建")
            return False
        
        # 重置配置到默认值
        AppConfig.STIMULATION_CONFIG['port_num'] = 1
        print(f"重置端口号为: {AppConfig.STIMULATION_CONFIG['port_num']}")
        
        # 重新加载配置
        if AppConfig.load_user_config():
            print("✓ 配置加载成功")
        else:
            print("✗ 配置加载失败")
            return False
        
        # 检查配置是否正确加载
        loaded_port = AppConfig.STIMULATION_CONFIG['port_num']
        print(f"加载后的端口号: {loaded_port}")
        
        if loaded_port == test_port:
            print("✓ 配置保存和加载功能正常")
            return True
        else:
            print(f"✗ 配置加载错误，期望: {test_port}, 实际: {loaded_port}")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        return False

def test_stimulation_config():
    """测试电刺激配置"""
    print("\n" + "=" * 60)
    print("电刺激配置测试")
    print("=" * 60)
    
    try:
        # 显示所有电刺激配置
        config = AppConfig.STIMULATION_CONFIG
        print("当前电刺激配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # 测试配置修改
        test_configs = {
            'port_num': 7,
            'max_current': 30.0,
            'default_frequency': 25.0,
            'default_pulse_width': 250.0
        }
        
        print(f"\n修改配置:")
        for key, value in test_configs.items():
            old_value = config[key]
            config[key] = value
            print(f"  {key}: {old_value} -> {value}")
        
        # 保存配置
        if AppConfig.save_user_config():
            print("✓ 配置保存成功")
        else:
            print("✗ 配置保存失败")
            return False
        
        # 验证保存的配置
        print(f"\n验证保存的配置:")
        for key, expected_value in test_configs.items():
            actual_value = config[key]
            if actual_value == expected_value:
                print(f"  ✓ {key}: {actual_value}")
            else:
                print(f"  ✗ {key}: 期望 {expected_value}, 实际 {actual_value}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 电刺激配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("配置保存功能测试")
    print("测试时间:", os.popen('date /t').read().strip() if os.name == 'nt' else os.popen('date').read().strip())
    
    # 运行测试
    tests = [
        ("配置保存和加载", test_config_save_load),
        ("电刺激配置", test_stimulation_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总计: {total} 项测试")
    print(f"通过: {passed} 项")
    print(f"失败: {total - passed} 项")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置保存功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
