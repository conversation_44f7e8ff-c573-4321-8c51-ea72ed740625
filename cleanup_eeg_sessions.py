#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理eeg_sessions表的孤立记录
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def cleanup_eeg_sessions():
    """清理eeg_sessions表"""
    print("🧹 清理eeg_sessions表...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已临时禁用外键约束")
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(eeg_sessions)")
            columns = cursor.fetchall()
            print("📋 eeg_sessions表结构:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 查看当前数据
            cursor.execute("SELECT * FROM eeg_sessions")
            sessions = cursor.fetchall()
            print(f"\n📋 eeg_sessions表中有 {len(sessions)} 条记录")
            
            if sessions:
                print("前几条记录:")
                for i, session in enumerate(sessions[:5]):
                    print(f"   记录 {i+1}: {list(session)}")
            
            # 找到患者ID字段
            patient_id_column = None
            for col in columns:
                col_name = col[1].lower()
                if 'patient' in col_name or 'bingren' in col_name:
                    patient_id_column = col[1]
                    break
            
            if not patient_id_column:
                print("❌ 没有找到患者ID字段")
                return False
            
            print(f"📋 使用患者ID字段: {patient_id_column}")
            
            # 查找孤立记录
            cursor.execute(f"""
                SELECT rowid, {patient_id_column}
                FROM eeg_sessions es
                WHERE NOT EXISTS (
                    SELECT 1 FROM bingren b WHERE b.bianhao = es.{patient_id_column}
                )
            """)
            orphaned_sessions = cursor.fetchall()
            
            if orphaned_sessions:
                print(f"📋 发现 {len(orphaned_sessions)} 个孤立的会话记录:")
                for session in orphaned_sessions:
                    print(f"   rowid: {session[0]}, 患者ID: {session[1]}")
                
                # 删除孤立记录
                for session in orphaned_sessions:
                    cursor.execute("DELETE FROM eeg_sessions WHERE rowid = ?", (session[0],))
                    print(f"   ✅ 删除会话记录 rowid: {session[0]} (患者ID: {session[1]})")
                
                print(f"✅ 清理了 {len(orphaned_sessions)} 个孤立会话记录")
            else:
                print("✅ 没有发现孤立的会话记录")
            
            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 提交事务
            conn.commit()
            print("✅ 事务已提交")
            
            return True
        
    except Exception as e:
        print(f"❌ 清理eeg_sessions失败: {e}")
        return False

def verify_cleanup():
    """验证清理结果"""
    print("\n🔍 验证清理结果...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查外键约束
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"❌ 仍有 {len(violations)} 个外键约束违规:")
                for violation in violations:
                    print(f"   {list(violation)}")
                return False
            else:
                print("✅ 外键约束检查通过")
        
        # 检查数据完整性
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()
        print(f"📋 数据完整性检查: {integrity_result[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证清理结果失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 清理eeg_sessions表工具")
    print("=" * 80)
    
    # 1. 清理eeg_sessions表
    if not cleanup_eeg_sessions():
        print("❌ 清理eeg_sessions失败，退出")
        return False
    
    # 2. 验证清理结果
    if not verify_cleanup():
        print("❌ 验证失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 eeg_sessions表清理完成！")
    print("✅ 孤立的会话记录已删除")
    print("✅ 外键约束检查通过")
    print("✅ 数据库完整性恢复")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
