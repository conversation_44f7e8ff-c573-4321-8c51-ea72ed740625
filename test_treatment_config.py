#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试治疗界面配置读取
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.app_config import AppConfig

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("配置加载测试")
    print("=" * 60)
    
    try:
        # 显示当前配置
        print("当前电刺激配置:")
        config = AppConfig.STIMULATION_CONFIG
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # 重点检查端口号
        port_num = config.get('port_num', 1)
        print(f"\n重点检查:")
        print(f"  电刺激端口号: {port_num}")
        
        if port_num == 7:
            print("✓ 端口号配置正确 (7)")
            return True
        else:
            print(f"✗ 端口号配置错误，期望: 7, 实际: {port_num}")
            return False
            
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False

def test_treatment_connection_logic():
    """测试治疗界面连接逻辑"""
    print("\n" + "=" * 60)
    print("治疗界面连接逻辑测试")
    print("=" * 60)
    
    try:
        # 模拟治疗界面的连接逻辑
        config = AppConfig.STIMULATION_CONFIG
        port_num = config.get('port_num', 1)
        
        print(f"模拟治疗界面连接逻辑:")
        print(f"  从配置读取端口号: {port_num}")
        print(f"  将使用端口 {port_num} 连接电刺激设备")
        
        # 检查其他相关配置
        other_configs = {
            'max_current': config.get('max_current', 50.0),
            'default_frequency': config.get('default_frequency', 20.0),
            'default_pulse_width': config.get('default_pulse_width', 200.0),
            'connection_timeout': config.get('connection_timeout', 5.0)
        }
        
        print(f"\n其他相关配置:")
        for key, value in other_configs.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 治疗界面连接逻辑测试失败: {e}")
        return False

def test_config_file_exists():
    """测试配置文件是否存在"""
    print("\n" + "=" * 60)
    print("配置文件检查")
    print("=" * 60)
    
    try:
        config_file = AppConfig.CONFIG_FILE
        print(f"配置文件路径: {config_file}")
        
        if config_file.exists():
            print("✓ 配置文件存在")
            
            # 显示文件大小
            size = config_file.stat().st_size
            print(f"  文件大小: {size} 字节")
            
            # 显示修改时间
            import datetime
            mtime = datetime.datetime.fromtimestamp(config_file.stat().st_mtime)
            print(f"  修改时间: {mtime}")
            
            return True
        else:
            print("✗ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 配置文件检查失败: {e}")
        return False

def main():
    """主函数"""
    print("治疗界面配置读取测试")
    print("测试时间:", os.popen('date /t').read().strip() if os.name == 'nt' else os.popen('date').read().strip())
    
    # 运行测试
    tests = [
        ("配置文件检查", test_config_file_exists),
        ("配置加载测试", test_config_loading),
        ("治疗界面连接逻辑", test_treatment_connection_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总计: {total} 项测试")
    print(f"通过: {passed} 项")
    print(f"失败: {total - passed} 项")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 治疗界面现在应该能正确使用端口7连接电刺激设备")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
