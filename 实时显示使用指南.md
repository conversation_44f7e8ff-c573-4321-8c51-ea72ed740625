# 实时脑电信号显示功能使用指南

## 快速开始

### 1. 安装依赖库
```bash
python install_display_dependencies.py
```

### 2. 测试功能
```bash
python test_realtime_display.py
```

### 3. 在治疗系统中使用
1. 启动治疗系统
2. 连接脑电设备
3. 选择患者
4. 点击"开始治疗"
5. 观察实时显示区域

## 界面说明

### 脑电训练页面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    顶部信息栏                                │
├─────────────┬───────────────────────────────────────────────┤
│             │              脑电信号显示区域                  │
│   控制面板   ├─────────────────┬───────────────────────────┤
│             │  8通道实时曲线   │    实时脑电地形图          │
│             │                │                          │
│             │                │                          │
│             ├─────────────────┴───────────────────────────┤
│             │              治疗反馈与结果区域              │
└─────────────┴───────────────────────────────────────────────┘
```

### 实时曲线显示
- **位置**: 脑电信号显示区域左半部
- **内容**: 8通道脑电信号的ASCII艺术曲线和统计信息
- **更新频率**: 每500ms更新一次
- **显示信息**:
  - ASCII波形图: 使用 _ . - ~ ^ 字符表示波形
  - 各通道均值、标准差、RMS值
  - 数据更新时间和缓冲区状态
- **示例显示**:
```
实时脑电信号曲线 (最近2秒)
============================================================

 PZ: ~~^~-._~~^~-._~~    均值: 12.3 标准差: 45.6 RMS: 67.8
 P3: -._~~^~-._~~^~-.    均值: 23.4 标准差: 56.7 RMS: 78.9
 P4: ^~-._~~^~-._~~^~    均值: 34.5 标准差: 67.8 RMS: 89.0
```

### 脑电地形图显示
- **位置**: 脑电信号显示区域右半部
- **内容**: ASCII艺术头皮电极分布和信号强度
- **更新频率**: 与曲线显示同步
- **强度编码**: · ○ ● ◉ ⬢ (从低到高)
- **显示格式**:
```
脑电地形图 (RMS值)
========================================

        F3      F4
      ● 45.6  ◉ 67.8

    C3      CZ      C4
  ○ 23.4  ● 56.7  ◉ 78.9

      P3   PZ   P4
    ○ 34.5 ● 45.6 ○ 56.7

强度图例: · ○ ● ◉ ⬢ (低→高)
```

## 操作流程

### 开始实时显示
1. **前置条件检查**:
   - ✅ 脑电设备已连接
   - ✅ 电刺激设备已连接
   - ✅ 模型已加载
   - ✅ 患者已选择

2. **启动治疗**:
   - 点击"开始治疗"按钮
   - 系统自动启动实时显示
   - 开始接收和显示脑电数据

3. **观察数据**:
   - 左侧: 查看8通道统计信息
   - 右侧: 查看头皮信号分布
   - 监控数据质量和信号强度

### 停止实时显示
1. **停止治疗**:
   - 点击"停止治疗"按钮
   - 系统自动停止实时显示
   - 清理缓冲区和资源

2. **数据保存**:
   - 治疗数据自动保存
   - 实时显示数据不保存

## 数据解读

### 统计指标说明
- **均值**: 信号的直流偏移，正常范围±50μV
- **标准差**: 信号的变异程度，反映信号活跃度
- **RMS值**: 信号的有效值，反映信号强度

### 信号质量评估
- **良好**: RMS值在20-100μV范围内
- **一般**: RMS值在10-20μV或100-200μV范围内
- **较差**: RMS值小于10μV或大于200μV

### 电极位置说明
- **F3/F4**: 前额叶区域，与注意力相关
- **C3/CZ/C4**: 运动皮层区域，与运动想象相关
- **P3/PZ/P4**: 顶叶区域，与感觉处理相关

## 故障排除

### 常见问题

#### 1. 显示区域空白
**原因**: 依赖库未安装或脑电设备未连接
**解决方案**:
```bash
# 检查依赖
python install_display_dependencies.py

# 检查设备连接
确认脑电设备已正确连接并启动
```

#### 2. 数据不更新
**原因**: 治疗未启动或数据流中断
**解决方案**:
- 确认已点击"开始治疗"
- 检查脑电设备连接状态
- 查看系统日志错误信息

#### 3. 显示异常数值
**原因**: 电极接触不良或信号干扰
**解决方案**:
- 检查电极接触质量
- 减少环境电磁干扰
- 重新连接脑电设备

### 日志查看
系统日志位置: 治疗反馈与结果区域的系统日志栏
关键日志信息:
- "实时显示已启动"
- "更新实时曲线失败"
- "收集显示数据失败"

## 性能优化

### 系统要求
- **CPU**: Intel i5或同等性能
- **内存**: 8GB RAM以上
- **显卡**: 集成显卡即可
- **操作系统**: Windows 10/11

### 优化建议
1. **关闭不必要的程序**: 释放系统资源
2. **使用有线连接**: 减少无线干扰
3. **定期重启**: 清理内存碎片
4. **更新驱动**: 保持设备驱动最新

## 技术支持

### 联系方式
- 技术支持邮箱: <EMAIL>
- 用户手册: 查看完整技术文档
- 在线帮助: 系统内置帮助功能

### 常用命令
```bash
# 测试实时显示
python test_realtime_display.py

# 检查依赖状态
python -c "import pyqtgraph, matplotlib, mne; print('所有依赖已安装')"

# 查看系统信息
python -c "import sys; print(f'Python版本: {sys.version}')"
```

## 更新日志

### v1.0.0 (2024-12-05)
- ✅ 实现8通道实时曲线显示
- ✅ 实现脑电地形图显示
- ✅ 集成到治疗系统界面
- ✅ 添加兼容性处理
- ✅ 完成测试验证

### 计划功能
- 🔄 图形化曲线显示
- 🔄 频谱分析功能
- 🔄 数据录制回放
- 🔄 自定义显示配置
