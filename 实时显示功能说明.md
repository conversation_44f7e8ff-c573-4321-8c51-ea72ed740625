# 实时脑电信号显示功能实现说明

## 概述

本文档说明了在治疗系统中实现的实时脑电信号显示功能，包括8通道实时曲线和脑电地形图显示。

## 功能特性

### 1. 实时曲线显示 (RealTimeEEGCurves)
- **8通道同时显示**: 支持PZ, P3, P4, C3, CZ, C4, F3, F4等8个标准10-20电极位置
- **ASCII艺术曲线**: 使用字符图形显示波形变化趋势
- **实时数据更新**: 500ms更新频率，平衡性能和实时性
- **数据缓冲**: 10秒滚动窗口，1250个样本点@125Hz采样率
- **统计信息**: 显示每个通道的均值、标准差、RMS值等统计信息
- **兼容性设计**: 纯文本显示，无需额外图形库依赖

### 2. 脑电地形图显示 (MNETopographyDisplay)
- **头皮分布**: 按照标准10-20电极位置排列显示
- **ASCII艺术地形图**: 使用特殊字符表示信号强度分布
- **强度编码**: 使用 · ○ ● ◉ ⬢ 字符表示从低到高的信号强度
- **RMS值计算**: 显示每个电极位置的信号强度数值
- **统计摘要**: 显示最大值、最小值、平均值等统计信息
- **实时更新**: 与曲线显示同步更新

## 技术实现

### 1. 数据流架构
```
脑电设备 → EEGDataPacket → _collect_display_data() → 显示缓冲区 → _update_realtime_display() → 显示组件
```

### 2. 线程安全设计
- 使用`threading.Lock`保护显示缓冲区
- 数据收集和显示更新在不同线程中进行
- 避免UI阻塞和数据竞争

### 3. 性能优化
- **限制更新频率**: 显示更新限制在250ms间隔
- **数据预处理**: 复用现有信号处理器进行数据预处理
- **缓冲区管理**: 固定大小的滚动缓冲区，避免内存泄漏

### 4. 兼容性处理
- **依赖检测**: 自动检测PyQtGraph、Matplotlib、MNE等库的可用性
- **优雅降级**: 当图形库不可用时，自动切换到文本显示模式
- **错误处理**: 完善的异常处理和日志记录

## 集成方式

### 1. 在治疗界面中的集成
实时显示组件已集成到治疗系统的脑电训练页面中：

```python
# 在create_display_panel()方法中
self.realtime_curves = RealTimeEEGCurves()
self.realtime_topography = MNETopographyDisplay()
```

### 2. 数据流集成
在`on_eeg_data_received()`方法中添加了数据收集：

```python
# 收集实时显示数据（仅在治疗期间）
if self.is_classifying and hasattr(self, 'display_buffer'):
    self._collect_display_data(data_packet)
```

### 3. 生命周期管理
- **启动**: 在开始治疗时自动启动实时显示
- **停止**: 在停止治疗时自动停止实时显示
- **清理**: 自动清理缓冲区和定时器资源

## 使用方法

### 1. 启动实时显示
实时显示会在开始治疗时自动启动，无需手动操作。

### 2. 观察数据
- **实时曲线**: 左侧显示8通道的实时波形和统计信息
- **地形图**: 右侧显示头皮电极分布和信号强度

### 3. 停止显示
实时显示会在停止治疗时自动停止。

## 测试验证

### 1. 测试脚本
提供了`test_realtime_display.py`测试脚本：
- 模拟8通道脑电数据
- 测试实时曲线和地形图显示
- 验证数据更新和界面响应

### 2. 依赖安装
提供了`install_display_dependencies.py`安装脚本：
- 自动检测和安装所需依赖库
- 支持PyQtGraph、Matplotlib、MNE、SciPy等

### 3. 运行测试
```bash
# 安装依赖
python install_display_dependencies.py

# 运行测试
python test_realtime_display.py
```

## 配置选项

### 1. 更新频率
```python
self.display_update_timer.setInterval(250)  # 250ms更新频率
```

### 2. 缓冲区大小
```python
self.buffer_size = 1250  # 10秒窗口 @ 125Hz
```

### 3. 显示范围
```python
self.y_range = 200  # ±200μV显示范围
```

## 故障排除

### 1. 常见问题
- **依赖库缺失**: 运行安装脚本安装所需依赖
- **显示异常**: 检查日志文件中的错误信息
- **性能问题**: 调整更新频率和缓冲区大小

### 2. 日志信息
系统会记录详细的日志信息：
- 组件初始化状态
- 数据更新错误
- 性能统计信息

### 3. 兼容性
- 支持Windows 10/11
- 要求Python 3.8+
- 推荐使用conda环境

## 未来扩展

### 1. 功能增强
- 支持更多电极位置
- 添加频谱分析显示
- 实现数据录制和回放

### 2. 性能优化
- GPU加速计算
- 更高效的数据结构
- 自适应更新频率

### 3. 用户体验
- 可配置的显示选项
- 交互式缩放和平移
- 自定义颜色主题

## 总结

实时脑电信号显示功能已成功集成到治疗系统中，提供了：
- ✅ 8通道实时曲线显示
- ✅ 脑电地形图显示
- ✅ 统计信息展示
- ✅ 兼容性设计
- ✅ 性能优化
- ✅ 完整测试验证

该功能增强了治疗系统的可视化能力，为医生和患者提供了直观的脑电信号反馈。
