#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速连接速度测试 - 验证优化效果
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.WARNING,  # 减少日志输出
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_quick_connection():
    """快速连接测试"""
    print("⚡ 快速连接速度测试")
    print("=" * 50)
    
    try:
        from core.stimulation_device import StimulationDevice
        
        device = StimulationDevice()
        
        print("\n📍 测试1: 正确端口连接速度")
        start_time = time.time()
        result = device.connect(7)
        connection_time = time.time() - start_time
        
        if result:
            print(f"✅ 连接成功: {connection_time:.3f}秒")
            
            start_time = time.time()
            device.disconnect()
            disconnect_time = time.time() - start_time
            print(f"✅ 断开成功: {disconnect_time:.3f}秒")
        else:
            print(f"❌ 连接失败: {connection_time:.3f}秒")
        
        time.sleep(1)
        
        print("\n📍 测试2: 错误端口处理速度（关键测试）")
        start_time = time.time()
        result = device.connect(26)  # 错误端口
        error_time = time.time() - start_time
        
        print(f"错误端口处理时间: {error_time:.3f}秒")
        
        if error_time <= 8:
            print("🎉 错误处理速度优秀（≤8秒）")
        elif error_time <= 12:
            print("✅ 错误处理速度良好（≤12秒）")
        else:
            print("⚠️ 错误处理速度仍需优化（>12秒）")
        
        time.sleep(1)
        
        print("\n📍 测试3: 错误后恢复连接")
        start_time = time.time()
        result = device.connect(7)  # 正确端口
        recovery_time = time.time() - start_time
        
        if result:
            print(f"✅ 恢复连接成功: {recovery_time:.3f}秒")
            device.disconnect()
        else:
            print(f"❌ 恢复连接失败: {recovery_time:.3f}秒")
        
        print("\n" + "=" * 50)
        print("📊 总结:")
        print(f"正确端口连接: ~{connection_time:.1f}秒")
        print(f"错误端口处理: ~{error_time:.1f}秒")
        print(f"恢复连接: ~{recovery_time:.1f}秒")
        
        if error_time <= 10 and connection_time <= 1.5:
            print("🎉 速度优化成功！")
            return True
        else:
            print("⚠️ 仍有优化空间")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_quick_connection()
    sys.exit(0 if success else 1)
