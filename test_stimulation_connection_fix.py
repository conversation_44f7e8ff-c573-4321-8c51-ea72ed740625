#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备连接问题修复测试脚本

测试修复后的电刺激设备连接逻辑，验证：
1. 连接错误端口后能否正常切换到正确端口
2. 连接超时后能否立即重新连接
3. 资源清理是否彻底
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('stimulation_connection_test.log', encoding='utf-8')
        ]
    )

def test_connection_recovery():
    """测试连接恢复能力"""
    print("🔧 测试电刺激设备连接恢复能力")
    
    device = StimulationDevice()
    test_results = []
    
    # 测试1: 连接错误端口
    print("\n📍 测试1: 连接错误端口")
    wrong_port = 99  # 不存在的端口
    start_time = time.time()
    result1 = device.connect(wrong_port)
    end_time = time.time()
    
    print(f"   连接错误端口 {wrong_port}: {'成功' if result1 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('错误端口连接', not result1))  # 应该失败
    
    # 等待一下
    time.sleep(1)
    
    # 测试2: 连接正确端口（模拟）
    print("\n📍 测试2: 连接正确端口")
    correct_port = 7  # 假设正确端口
    start_time = time.time()
    result2 = device.connect(correct_port)
    end_time = time.time()
    
    print(f"   连接正确端口 {correct_port}: {'成功' if result2 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('正确端口连接', result2))
    
    # 测试3: 断开连接
    print("\n📍 测试3: 断开连接")
    if result2:
        disconnect_result = device.disconnect()
        print(f"   断开连接: {'成功' if disconnect_result else '失败'}")
        test_results.append(('断开连接', disconnect_result))
    
    # 测试4: 重新连接
    print("\n📍 测试4: 重新连接")
    time.sleep(1)
    start_time = time.time()
    result4 = device.connect(correct_port)
    end_time = time.time()
    
    print(f"   重新连接端口 {correct_port}: {'成功' if result4 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('重新连接', result4))
    
    # 最终清理
    if result4:
        device.disconnect()
    
    return test_results

def test_force_release_mechanism():
    """测试强制释放机制"""
    print("\n🔧 测试强制释放机制")
    
    device = StimulationDevice()
    test_results = []
    
    # 加载DLL
    if not device.load_dll():
        print("   ❌ DLL加载失败")
        return [('DLL加载', False)]
    
    print("   ✅ DLL加载成功")
    test_results.append(('DLL加载', True))
    
    # 测试强制释放端口
    test_port = 99
    print(f"\n📍 测试强制释放端口 {test_port}")
    
    try:
        device._force_release_port(test_port)
        print("   ✅ 强制释放端口完成（无异常）")
        test_results.append(('强制释放端口', True))
    except Exception as e:
        print(f"   ❌ 强制释放端口失败: {e}")
        test_results.append(('强制释放端口', False))
    
    # 测试增强DLL重置
    print("\n📍 测试增强DLL重置")
    try:
        device._enhanced_dll_reset()
        print("   ✅ 增强DLL重置完成（无异常）")
        test_results.append(('增强DLL重置', True))
    except Exception as e:
        print(f"   ❌ 增强DLL重置失败: {e}")
        test_results.append(('增强DLL重置', False))
    
    return test_results

def test_multiple_connection_attempts():
    """测试多次连接尝试"""
    print("\n🔧 测试多次连接尝试")
    
    device = StimulationDevice()
    test_results = []
    
    wrong_port = 99
    attempts = 3
    
    for i in range(attempts):
        print(f"\n📍 第 {i+1} 次连接尝试")
        start_time = time.time()
        result = device.connect(wrong_port)
        end_time = time.time()
        
        print(f"   连接结果: {'成功' if result else '失败'}")
        print(f"   耗时: {end_time - start_time:.2f}秒")
        
        test_results.append((f'第{i+1}次连接', not result))  # 应该失败
        
        # 短暂等待
        time.sleep(0.5)
    
    return test_results

def print_test_summary(all_results):
    """打印测试总结"""
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for test_name, results in all_results.items():
        print(f"\n🔍 {test_name}:")
        for test_case, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_case}: {status}")
            total_tests += 1
            if result:
                passed_tests += 1
    
    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 修复效果良好！")
    elif success_rate >= 60:
        print("⚠️  修复有一定效果，但仍需改进")
    else:
        print("❌ 修复效果不佳，需要进一步调查")

def main():
    """主函数"""
    print("🚀 电刺激设备连接问题修复测试")
    print("="*60)
    
    setup_logging()
    
    all_results = {}
    
    try:
        # 测试1: 连接恢复能力
        results1 = test_connection_recovery()
        all_results["连接恢复能力"] = results1
        
        # 测试2: 强制释放机制
        results2 = test_force_release_mechanism()
        all_results["强制释放机制"] = results2
        
        # 测试3: 多次连接尝试
        results3 = test_multiple_connection_attempts()
        all_results["多次连接尝试"] = results3
        
        # 打印总结
        print_test_summary(all_results)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
