#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强脑电信号处理算法
Test Enhanced EEG Signal Processing Algorithms

作者: AI Assistant
版本: 1.0.0
"""

import numpy as np
import logging
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_synthetic_eeg_data(n_trials=20, n_channels=8, n_samples=250, task_type='motor_imagery'):
    """生成合成脑电数据用于测试"""
    np.random.seed(42)  # 确保可重复性
    
    data_list = []
    labels = []
    
    for trial in range(n_trials):
        # 生成基础信号
        t = np.linspace(0, 2, n_samples)  # 2秒数据
        
        # 基础脑电信号 (alpha波段)
        base_signal = np.sin(2 * np.pi * 10 * t) + 0.5 * np.sin(2 * np.pi * 12 * t)
        
        # 添加噪声
        noise = np.random.normal(0, 0.3, n_samples)
        
        trial_data = []
        label = trial % 2  # 二分类：0和1
        
        for ch in range(n_channels):
            if task_type == 'motor_imagery':
                if label == 1:  # 运动想象
                    # 在C3, C4通道添加mu节律抑制 (8-12Hz)
                    if ch in [2, 3]:  # 模拟C3, C4
                        mu_suppression = -0.3 * np.sin(2 * np.pi * 10 * t)
                        beta_enhancement = 0.2 * np.sin(2 * np.pi * 20 * t)
                        channel_signal = base_signal + mu_suppression + beta_enhancement + noise
                    else:
                        channel_signal = base_signal + noise
                else:  # 休息状态
                    channel_signal = base_signal + noise
            else:
                channel_signal = base_signal + noise
            
            # 缩放到合理的ADC范围
            channel_signal = channel_signal * 1000 + np.random.normal(0, 100)
            trial_data.append(channel_signal)
        
        data_list.append(np.array(trial_data))
        labels.append(label)
    
    return data_list, labels

def test_enhanced_feature_extractor():
    """测试增强特征提取器"""
    logger.info("🧠 测试增强特征提取器...")
    
    try:
        from algorithms.motor_imagery_classifier import EnhancedFeatureExtractor, EEGData
        
        # 创建特征提取器
        extractor = EnhancedFeatureExtractor(sampling_rate=125, n_channels=8)
        
        # 生成测试数据
        data_list, labels = generate_synthetic_eeg_data(n_trials=10)
        
        # 测试单个样本特征提取
        test_data = data_list[0]
        eeg_data = EEGData(
            data=test_data,
            sampling_rate=125,
            channel_names=[f'Ch{i+1}' for i in range(8)],
            timestamp=time.time()
        )
        
        # 测试各种特征提取方法
        logger.info("  📊 测试功率谱密度特征...")
        psd_features = extractor.extract_power_spectral_density(test_data)
        logger.info(f"    PSD特征维度: {len(psd_features)}")
        
        logger.info("  🌊 测试小波变换特征...")
        wavelet_features = extractor.extract_wavelet_features(test_data)
        logger.info(f"    小波特征维度: {len(wavelet_features)}")
        
        logger.info("  🎯 测试自适应频带选择...")
        adaptive_bands = extractor.adaptive_band_selection(np.array(data_list), np.array(labels))
        logger.info(f"    自适应频带: {adaptive_bands}")
        
        logger.info("  🔄 测试增强CSP特征...")
        csp_features = extractor.extract_enhanced_csp_features(
            np.array(data_list), np.array(labels), method='rcsp'
        )
        logger.info(f"    rCSP特征维度: {len(csp_features)}")
        
        logger.info("  📈 测试综合特征提取...")
        all_features = extractor.extract_all_features(eeg_data, labels=np.array(labels))
        logger.info(f"    综合特征维度: {len(all_features)}")
        
        logger.info("✅ 增强特征提取器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强特征提取器测试失败: {e}")
        return False

def test_enhanced_classifier():
    """测试增强分类器"""
    logger.info("🤖 测试增强分类器...")
    
    try:
        from algorithms.motor_imagery_classifier import MotorImageryClassifier, MotorImageryTask, EEGData
        
        # 创建分类器
        classifier = MotorImageryClassifier(sampling_rate=125, classifier_type='lda')
        
        # 生成训练数据
        data_list, labels = generate_synthetic_eeg_data(n_trials=20)
        
        # 转换为EEGData格式
        training_data = []
        training_labels = []
        
        for data, label in zip(data_list, labels):
            eeg_data = EEGData(
                data=data,
                sampling_rate=125,
                channel_names=[f'Ch{i+1}' for i in range(8)],
                timestamp=time.time()
            )
            training_data.append(eeg_data)
            
            # 转换标签
            if label == 0:
                training_labels.append(MotorImageryTask.REST)
            else:
                training_labels.append(MotorImageryTask.LEFT_HAND)
        
        # 训练分类器
        logger.info("  🎓 训练增强分类器...")
        success = classifier.train(
            training_data, 
            training_labels, 
            use_adaptive_bands=True,
            feature_types=['psd', 'statistical', 'wavelet']
        )
        
        if success:
            logger.info(f"    训练准确率: {classifier.training_accuracy:.3f}")
            logger.info(f"    交叉验证准确率: {classifier.cross_validation_score:.3f}")
            
            # 测试预测
            logger.info("  🔮 测试预测功能...")
            test_data = training_data[0]
            result = classifier.predict(test_data)
            
            logger.info(f"    预测结果: {result.predicted_class.value}")
            logger.info(f"    置信度: {result.confidence:.3f}")
            logger.info(f"    处理时间: {result.processing_time:.3f}s")
            
            logger.info("✅ 增强分类器测试完成")
            return True
        else:
            logger.error("❌ 分类器训练失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 增强分类器测试失败: {e}")
        return False

def test_enhanced_signal_processor():
    """测试增强信号处理器"""
    logger.info("⚡ 测试增强信号处理器...")
    
    try:
        from core.signal_processor import EEGSignalProcessor
        
        # 创建信号处理器
        processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
        
        # 生成测试数据
        data_list, _ = generate_synthetic_eeg_data(n_trials=1)
        test_data = data_list[0]
        
        # 添加一些伪迹
        artifact_data = test_data.copy()
        artifact_data[0, 50:60] = 50000  # 饱和伪迹
        artifact_data[1, :] = np.random.normal(0, 10000, artifact_data.shape[1])  # 高噪声
        
        logger.info("  🔧 测试改进的信号预处理...")
        processed_data, quality = processor.preprocess_signal(artifact_data)
        
        logger.info(f"    信号质量评分: {quality.overall_quality:.3f}")
        logger.info(f"    噪声水平: {quality.noise_level:.3f}")
        logger.info(f"    伪迹比例: {quality.artifact_ratio:.3f}")
        logger.info(f"    信号可用性: {'是' if quality.is_usable else '否'}")
        
        logger.info("  📊 测试特征提取...")
        features = processor.extract_features(processed_data)
        
        for feature_name, feature_data in features.items():
            logger.info(f"    {feature_name}: {feature_data.shape}")
        
        logger.info("✅ 增强信号处理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强信号处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始测试增强脑电信号处理算法...")
    
    test_results = []
    
    # 测试各个组件
    test_results.append(test_enhanced_signal_processor())
    test_results.append(test_enhanced_feature_extractor())
    test_results.append(test_enhanced_classifier())
    
    # 统计结果
    success_count = sum(test_results)
    total_count = len(test_results)
    
    logger.info(f"\n📊 测试结果: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        logger.info("🎉 所有增强算法测试通过！")
        logger.info("\n✨ 优化效果:")
        logger.info("  • MNE-Python CSP实现提供更稳定的空间滤波")
        logger.info("  • 正则化CSP提高小样本性能")
        logger.info("  • Filter Bank CSP处理多频带信息")
        logger.info("  • 小波变换提供时频域特征")
        logger.info("  • 自适应频带选择个性化优化")
        logger.info("  • 改进伪迹检测提高信号质量")
    else:
        logger.warning("⚠️ 部分测试失败，请检查依赖库安装")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
