#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理界面演示
Data Management UI Demo

演示脑电原始数据存储和管理功能

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from ui.data_management_ui import DataManagementWidget

class DataManagementDemo(QMainWindow):
    """数据管理演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("脑电原始数据管理系统 - 演示")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 初始化数据库
        self.db_manager = DatabaseManager()
        if not self.db_manager.initialize():
            logging.error("数据库初始化失败")
            sys.exit(1)
        
        # 创建数据管理界面
        self.data_management_widget = DataManagementWidget()
        self.data_management_widget.set_database_manager(self.db_manager)
        
        # 设置中央部件
        self.setCentralWidget(self.data_management_widget)
        
        logging.info("数据管理演示界面初始化完成")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("脑电数据管理系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("NK Medical")
    
    # 创建主窗口
    window = DataManagementDemo()
    window.show()
    
    # 运行应用程序
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
