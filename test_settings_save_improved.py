#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的设置保存功能
验证智能检测配置变化和具体反馈功能
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from utils.app_config import AppConfig
from core.database_manager import DatabaseManager
from ui.settings_ui import SettingsWidget

class TestSettingsSave:
    """测试设置保存功能"""
    
    def __init__(self):
        self.app = None
        self.settings_widget = None
        self.db_manager = None
        self.temp_dir = None
        
    def setup(self):
        """设置测试环境"""
        print("=" * 60)
        print("设置保存功能改进测试")
        print("=" * 60)
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="nk_test_")
        print(f"临时目录: {self.temp_dir}")
        
        # 设置临时配置文件路径
        AppConfig.CONFIG_FILE = Path(self.temp_dir) / "test_config.json"
        
        # 创建应用程序
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        if not self.db_manager.initialize():
            print("✗ 数据库初始化失败")
            return False
        
        # 创建设置界面
        self.settings_widget = SettingsWidget()
        self.settings_widget.set_database_manager(self.db_manager)
        
        print("✓ 测试环境设置完成")
        return True
    
    def test_no_changes(self):
        """测试无变化时的保存"""
        print("\n1. 测试无变化时的保存...")
        
        # 不修改任何设置，直接保存
        # 模拟保存操作（不显示对话框）
        original_info = QMessageBox.information
        messages = []
        
        def capture_message(parent, title, text):
            messages.append((title, text))
            return QMessageBox.StandardButton.Ok
        
        QMessageBox.information = capture_message
        
        try:
            self.settings_widget.save_settings()
            
            if messages:
                title, text = messages[-1]
                print(f"对话框标题: {title}")
                print(f"对话框内容: {text}")
                
                if "无变化" in title and "未检测到任何配置变化" in text:
                    print("✓ 无变化检测正确")
                    return True
                else:
                    print("✗ 无变化检测失败")
                    return False
            else:
                print("✗ 未显示任何对话框")
                return False
                
        finally:
            QMessageBox.information = original_info
    
    def test_hospital_info_changes(self):
        """测试医院信息变化检测"""
        print("\n2. 测试医院信息变化检测...")
        
        # 修改医院信息
        self.settings_widget.hospital_name_edit.setText("测试医院")
        self.settings_widget.department_edit.setText("测试科室")
        self.settings_widget.device_id_edit.setText("TEST001")
        
        # 模拟保存操作
        original_info = QMessageBox.information
        messages = []
        
        def capture_message(parent, title, text):
            messages.append((title, text))
            return QMessageBox.StandardButton.Ok
        
        QMessageBox.information = capture_message
        
        try:
            self.settings_widget.save_settings()
            
            if messages:
                title, text = messages[-1]
                print(f"对话框标题: {title}")
                print(f"对话框内容: {text}")
                
                if "保存成功" in title and "医院名称:" in text and "→" in text:
                    print("✓ 医院信息变化检测正确")
                    return True
                else:
                    print("✗ 医院信息变化检测失败")
                    return False
            else:
                print("✗ 未显示任何对话框")
                return False
                
        finally:
            QMessageBox.information = original_info
    
    def test_config_changes(self):
        """测试配置变化检测"""
        print("\n3. 测试配置变化检测...")
        
        # 修改一些配置
        self.settings_widget.font_size_spin.setValue(12)  # 从默认10改为12
        self.settings_widget.log_level_combo.setCurrentText("DEBUG")  # 从INFO改为DEBUG
        
        # 模拟保存操作
        original_info = QMessageBox.information
        messages = []
        
        def capture_message(parent, title, text):
            messages.append((title, text))
            return QMessageBox.StandardButton.Ok
        
        QMessageBox.information = capture_message
        
        try:
            self.settings_widget.save_settings()
            
            if messages:
                title, text = messages[-1]
                print(f"对话框标题: {title}")
                print(f"对话框内容: {text}")
                
                if ("保存成功" in title and 
                    "字体大小:" in text and 
                    "日志级别:" in text and 
                    "→" in text):
                    print("✓ 配置变化检测正确")
                    return True
                else:
                    print("✗ 配置变化检测失败")
                    return False
            else:
                print("✗ 未显示任何对话框")
                return False
                
        finally:
            QMessageBox.information = original_info
    
    def cleanup(self):
        """清理测试环境"""
        try:
            if self.settings_widget:
                self.settings_widget.close()
            
            if self.db_manager:
                self.db_manager.close()
            
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"✓ 清理临时目录: {self.temp_dir}")
                
        except Exception as e:
            print(f"清理时发生错误: {e}")
    
    def run_tests(self):
        """运行所有测试"""
        if not self.setup():
            return False
        
        try:
            results = []
            results.append(self.test_no_changes())
            results.append(self.test_hospital_info_changes())
            results.append(self.test_config_changes())
            
            print("\n" + "=" * 60)
            print("测试结果汇总:")
            print("=" * 60)
            
            test_names = [
                "无变化检测",
                "医院信息变化检测", 
                "配置变化检测"
            ]
            
            for i, (name, result) in enumerate(zip(test_names, results)):
                status = "✓ 通过" if result else "✗ 失败"
                print(f"{i+1}. {name}: {status}")
            
            success_count = sum(results)
            total_count = len(results)
            print(f"\n总计: {success_count}/{total_count} 测试通过")
            
            if success_count == total_count:
                print("🎉 所有测试通过！设置保存功能改进成功！")
                return True
            else:
                print("❌ 部分测试失败，需要进一步检查")
                return False
                
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = TestSettingsSave()
    success = tester.run_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
