#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型参数同步功能
Test script for model parameter synchronization
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_parameter_sync():
    """测试模型加载后的参数同步功能"""
    print("=" * 70)
    print("模型参数同步功能测试")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：检查_update_ui_from_model_info方法
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查模型参数同步方法...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_update_ui_from_model_info方法
        required_elements = [
            'def _update_ui_from_model_info(self):',
            'model_info = self.current_model.get_model_info()',
            'self.temperature_spinbox.setValue(model_info.temperature)',
            'self.activation_threshold_spin.setValue(model_info.decision_threshold)',
            'self.class_weight_spinbox.setValue(model_info.class_weight_ratio)',
            'self.smoothing_slider.setValue(model_info.smoothing_window)',
            'self.adaptive_learning_checkbox.setChecked(model_info.adaptive_learning)',
            'self.transfer_learning_checkbox.setChecked(model_info.transfer_learning)',
            'self.finetune_layers_spinbox.setValue(model_info.finetune_layers)'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ 缺少模型参数同步元素: {missing_elements}")
        else:
            print("✅ 模型参数同步方法完整")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查模型参数同步方法失败: {e}")
    
    # 测试2：检查模型加载时的参数同步调用
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查模型加载时的参数同步调用...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模型加载后的调用
        required_calls = [
            'self._update_ui_from_model_info()',
            'self.start_classification_button.setEnabled(True)',
            '已根据模型信息更新深度学习参数'
        ]
        
        missing_calls = []
        for call in required_calls:
            if call not in content:
                missing_calls.append(call)
        
        if missing_calls:
            print(f"❌ 缺少模型加载调用: {missing_calls}")
        else:
            print("✅ 模型加载时正确调用参数同步")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查模型加载调用失败: {e}")
    
    # 测试3：检查深度学习参数变化回调方法
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查深度学习参数变化回调方法...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查参数变化回调方法
        callback_methods = [
            'def on_temperature_changed(self, value):',
            'def on_activation_threshold_changed(self, value):',
            'def on_class_weight_changed(self, value):',
            'def on_smoothing_changed(self, value):',
            'def on_adaptive_learning_toggled(self, checked):',
            'def on_transfer_learning_toggled(self, checked):',
            'def on_finetune_layers_changed(self, value):',
            'def on_neural_calibrate(self):'
        ]
        
        missing_methods = []
        for method in callback_methods:
            if method not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少参数变化回调方法: {missing_methods}")
        else:
            print("✅ 所有深度学习参数变化回调方法都存在")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查参数变化回调方法失败: {e}")
    
    # 测试4：检查参数更新逻辑
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查参数更新逻辑...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查参数更新逻辑
        update_logic = [
            'model_info.temperature = value',
            'model_info.decision_threshold = value',
            'model_info.class_weight_ratio = value',
            'model_info.smoothing_window = value',
            'model_info.adaptive_learning = checked',
            'model_info.transfer_learning = checked',
            'model_info.finetune_layers = value'
        ]
        
        missing_logic = []
        for logic in update_logic:
            if logic not in content:
                missing_logic.append(logic)
        
        if missing_logic:
            print(f"❌ 缺少参数更新逻辑: {missing_logic}")
        else:
            print("✅ 参数更新逻辑完整")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查参数更新逻辑失败: {e}")
    
    # 测试5：检查在线分类中的参数应用
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查在线分类中的参数应用...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查在线分类中的参数应用
        classification_params = [
            'threshold_value = self.activation_threshold_spin.value()',
            'model_info.decision_threshold = threshold_value',
            'temp_value = self.temperature_spinbox.value()',
            'model_info.temperature = temp_value',
            '强制更新激活阈值为界面设置值',
            '强制更新温度缩放为界面设置值'
        ]
        
        missing_params = []
        for param in classification_params:
            if param not in content:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 缺少在线分类参数应用: {missing_params}")
        else:
            print("✅ 在线分类中正确应用参数")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查在线分类参数应用失败: {e}")
    
    # 测试6：检查神经网络校准功能
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查神经网络校准功能...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查神经网络校准功能
        calibration_features = [
            'def on_neural_calibrate(self):',
            '开始神经网络校准...',
            '神经网络校准完成',
            'QMessageBox.information(self, "成功", "神经网络校准完成")'
        ]
        
        missing_features = []
        for feature in calibration_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ 缺少神经网络校准功能: {missing_features}")
        else:
            print("✅ 神经网络校准功能完整")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查神经网络校准功能失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 70)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有模型参数同步测试通过！")
        print("\n✅ 修复的功能:")
        print("   🔄 模型加载后自动同步UI参数")
        print("   ⚙️ 深度学习参数实时更新到模型")
        print("   🎯 在线分类使用最新的界面参数")
        print("   🔧 神经网络校准功能")
        print("   📊 完整的参数变化回调机制")
        print("\n🚀 现在的工作流程:")
        print("   1. 加载模型 → 自动设置UI参数")
        print("   2. 调整参数 → 实时更新模型配置")
        print("   3. 开始分类 → 使用最新参数进行预测")
        print("   4. 校准功能 → 优化模型性能")
        print("\n💡 解决的问题:")
        print("   ❌ 模型加载后参数不同步 → ✅ 自动同步")
        print("   ❌ 在线分类参数不生效 → ✅ 实时应用")
        print("   ❌ 缺少参数调节反馈 → ✅ 完整回调")
        return 0
    else:
        print("⚠️ 部分模型参数同步测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    sys.exit(test_model_parameter_sync())
