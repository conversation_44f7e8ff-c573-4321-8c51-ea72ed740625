#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的脑电原始数据存储功能测试
Simple Test for EEG Raw Data Storage

测试原始数据存储功能的基本功能

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from core.eeg_raw_data_manager import EEGRawDataManager, TrialType
from core.eeg_data_loader import EEGDataLoader
from utils.app_config import AppConfig


def generate_mock_eeg_data(channels=8, duration_seconds=4, sampling_rate=125):
    """生成模拟脑电数据"""
    n_samples = int(duration_seconds * sampling_rate)
    
    # 生成基础噪声
    data = np.random.randn(channels, n_samples) * 1000
    
    # 添加一些频率成分
    t = np.linspace(0, duration_seconds, n_samples)
    
    # Alpha波 (8-12 Hz)
    alpha_freq = 10
    alpha_component = 500 * np.sin(2 * np.pi * alpha_freq * t)
    
    # Beta波 (13-30 Hz)
    beta_freq = 20
    beta_component = 300 * np.sin(2 * np.pi * beta_freq * t)
    
    # 在某些通道添加特定频率成分
    data[2, :] += alpha_component  # C3
    data[5, :] += beta_component   # C4
    
    return data.astype(np.float32)


def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 检查HDF5支持
        import h5py
        print("✅ HDF5支持可用")
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        print("✅ 数据库初始化成功")
        
        # 初始化原始数据管理器
        raw_data_manager = EEGRawDataManager(db_manager)
        print("✅ 原始数据管理器初始化成功")
        
        # 测试配置
        config = AppConfig.get_config('raw_data')
        print(f"✅ 原始数据存储配置: 启用={config.get('enabled', False)}")
        
        # 测试开始会话
        patient_id = 123
        session_id = raw_data_manager.start_session(patient_id, session_type='test')
        
        if session_id <= 0:
            print("⚠️  会话创建失败，可能是因为原始数据存储被禁用")
            return True  # 这不算错误，只是功能被禁用
        
        print(f"✅ 会话创建成功: {session_id}")
        
        # 测试保存试验数据
        for i in range(3):
            # 生成模拟数据
            eeg_data = generate_mock_eeg_data()
            label = i % 2  # 交替标签
            
            # 保存数据
            success = raw_data_manager.save_trial_data(eeg_data, label)
            if success:
                print(f"✅ 试验{i}数据保存成功 (标签: {label})")
            else:
                print(f"⚠️  试验{i}数据保存失败")
        
        # 测试结束会话
        success = raw_data_manager.end_session()
        if success:
            print("✅ 会话结束成功")
        else:
            print("⚠️  会话结束失败")
        
        # 测试统计信息
        stats = raw_data_manager.get_storage_statistics()
        print(f"✅ 存储统计: {stats}")
        
        return True
        
    except ImportError:
        print("❌ HDF5支持不可用，请安装h5py: pip install h5py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_loader():
    """测试数据加载器"""
    print("\n=== 测试数据加载器 ===")
    
    try:
        # 初始化组件
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        data_loader = EEGDataLoader(db_manager)
        print("✅ 数据加载器初始化成功")
        
        # 测试获取数据统计
        stats = data_loader.get_data_statistics()
        print(f"✅ 数据统计: {stats}")
        
        # 测试搜索试验
        criteria = {
            'limit': 10
        }
        trials = data_loader.search_trials(criteria)
        print(f"✅ 搜索到 {len(trials)} 个试验")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        return False


def test_configuration():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        # 测试各种配置
        configs = [
            ('database', '数据库配置'),
            ('raw_data', '原始数据配置'),
            ('eeg', '脑电设备配置'),
            ('paths', '路径配置')
        ]
        
        for config_name, desc in configs:
            config = AppConfig.get_config(config_name)
            if config:
                print(f"✅ {desc}: {len(config)}个配置项")
            else:
                print(f"⚠️  {desc}: 配置为空")
        
        # 检查关键路径
        paths = AppConfig.get_config('paths')
        if paths:
            data_path = paths.get('data')
            if data_path and os.path.exists(data_path):
                print(f"✅ 数据目录存在: {data_path}")
            else:
                print(f"⚠️  数据目录不存在: {data_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_database_tables():
    """测试数据库表"""
    print("\n=== 测试数据库表 ===")
    
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        # 检查原始数据相关表是否存在
        tables_to_check = [
            'eeg_raw_data',
            'eeg_sessions'
        ]
        
        for table_name in tables_to_check:
            try:
                result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                if result:
                    count = result[0]['count']
                    print(f"✅ 表 {table_name} 存在，包含 {count} 条记录")
                else:
                    print(f"⚠️  表 {table_name} 查询失败")
            except Exception as e:
                print(f"⚠️  表 {table_name} 不存在或查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库表测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始脑电原始数据存储功能简化测试...")
    print("="*60)
    
    # 运行测试
    tests = [
        ("配置测试", test_configuration),
        ("数据库表测试", test_database_tables),
        ("基本功能测试", test_basic_functionality),
        ("数据加载器测试", test_data_loader),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 输出总结
    print(f"\n{'='*60}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！原始数据存储功能基本正常。")
        
        # 输出使用说明
        print("\n📋 使用说明:")
        print("1. 原始数据存储功能已集成到系统中")
        print("2. 数据将保存为HDF5格式，包含完整的标签信息")
        print("3. 可以通过配置文件启用/禁用此功能")
        print("4. 数据存储在 data/raw_eeg_data/ 目录下")
        print("5. 支持数据质量评估和完整性验证")
        
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
