#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能打包脚本 - 只打包必要文件，排除用户数据和大文件
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
from datetime import datetime

class SmartBuilder:
    """智能构建器 - 只打包必要文件"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.build_config = {
            'app_name': 'NK脑机接口康复训练系统',
            'version': '1.0.0',
            'company': '山东海天智能工程有限公司',
        }
        
        # 定义需要打包的文件和目录
        self.include_patterns = {
            # 配置文件（只包含模板）
            'config_files': [
                'data/user_config.json',  # 配置模板
            ],
            
            # 资源文件
            'resource_files': [
                'resources/',  # 所有资源文件
                'libs/',       # 库文件
                'docs/',       # 文档文件
                '密码.txt',    # 厂家密码
            ],
            
            # 空目录（需要创建但不包含内容）
            'empty_dirs': [
                'data/backup',
                'data/models', 
                'data/raw_eeg_data',
                'data/pretrained_models',
                'logs',
                'reports',
            ]
        }
        
        # 定义需要排除的文件和目录
        self.exclude_patterns = [
            # 用户数据
            'data/nk_system.db',           # 用户数据库
            'data/backup/*.db',            # 数据库备份
            'data/models/*',               # 训练的模型
            'data/raw_eeg_data/*',         # 原始脑电数据
            'data/bci_dataset/*',          # 数据集
            'data/datasets/*',             # 其他数据集
            'data/pretrained_models/*',    # 预训练模型
            
            # 临时文件
            '__pycache__/',
            '*.pyc',
            '*.pyo',
            '.pytest_cache/',
            'build/',
            'dist/',
            '*.spec',
            
            # 开发文件
            '.git/',
            '.vscode/',
            '.idea/',
            '*.log',
            'logs/*',
            'test_*.py',
            '*_test.py',
            
            # 大文件
            '*.zip',
            '*.rar',
            '*.7z',
            '*.pkl',
            '*.h5',
            '*.keras',
            '*.model',
        ]
    
    def check_environment(self):
        """检查构建环境"""
        print("🔍 检查构建环境...")
        
        # 检查Python
        try:
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                print("❌ Python版本过低，需要3.8+")
                return False
            print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        except Exception as e:
            print(f"❌ Python检查失败: {e}")
            return False
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("📦 安装PyInstaller...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✅ PyInstaller安装成功")
            except subprocess.CalledProcessError:
                print("❌ PyInstaller安装失败")
                return False
        
        return True
    
    def analyze_data_directory(self):
        """分析data目录，显示哪些文件会被排除"""
        print("\n📊 分析data目录...")
        print("=" * 60)
        
        data_path = self.project_root / 'data'
        if not data_path.exists():
            print("⚠️ data目录不存在")
            return
        
        total_size = 0
        excluded_size = 0
        included_files = []
        excluded_files = []
        
        for root, dirs, files in os.walk(data_path):
            for file in files:
                file_path = Path(root) / file
                file_size = file_path.stat().st_size
                total_size += file_size
                
                relative_path = file_path.relative_to(self.project_root)
                
                # 检查是否应该排除
                should_exclude = False
                for pattern in self.exclude_patterns:
                    if self._match_pattern(str(relative_path), pattern):
                        should_exclude = True
                        break
                
                if should_exclude:
                    excluded_files.append((relative_path, file_size))
                    excluded_size += file_size
                else:
                    # 检查是否在包含列表中
                    should_include = False
                    for file_pattern in self.include_patterns['config_files']:
                        if str(relative_path) == file_pattern:
                            should_include = True
                            break
                    
                    if should_include:
                        included_files.append((relative_path, file_size))
        
        print(f"📁 data目录总大小: {self._format_size(total_size)}")
        print(f"🚫 排除文件大小: {self._format_size(excluded_size)}")
        print(f"📦 包含文件大小: {self._format_size(total_size - excluded_size)}")
        print(f"💾 节省空间: {self._format_size(excluded_size)} ({excluded_size/total_size*100:.1f}%)")
        
        print(f"\n✅ 将包含的文件 ({len(included_files)}个):")
        for file_path, size in included_files:
            print(f"   📄 {file_path} ({self._format_size(size)})")
        
        print(f"\n🚫 将排除的文件 ({len(excluded_files)}个):")
        for file_path, size in excluded_files[:10]:  # 只显示前10个
            print(f"   ❌ {file_path} ({self._format_size(size)})")
        
        if len(excluded_files) > 10:
            print(f"   ... 还有 {len(excluded_files) - 10} 个文件被排除")
    
    def _match_pattern(self, path, pattern):
        """匹配文件模式"""
        import fnmatch
        return fnmatch.fnmatch(path, pattern) or fnmatch.fnmatch(path, pattern.replace('/', os.sep))
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def prepare_clean_config(self):
        """准备干净的配置文件"""
        print("\n🧹 准备干净的配置文件...")
        
        config_file = self.project_root / 'data' / 'user_config.json'
        if not config_file.exists():
            print("⚠️ 配置文件不存在，创建默认配置")
            return self._create_default_config()
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 清理配置中的绝对路径，使用相对路径
            if 'database' in config:
                config['database']['path'] = 'data/nk_system.db'
                config['database']['backup_path'] = 'data/backup'
            
            if 'stimulation' in config:
                config['stimulation']['dll_path'] = 'libs/RecoveryDLL.dll'
            
            # 创建临时的干净配置文件
            clean_config_file = self.project_root / 'data' / 'user_config_clean.json'
            with open(clean_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ 干净配置文件已准备")
            return str(clean_config_file)
            
        except Exception as e:
            print(f"❌ 准备配置文件失败: {e}")
            return None
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "database": {
                "type": "sqlite",
                "path": "data/nk_system.db",
                "backup_path": "data/backup",
                "auto_backup": True,
                "backup_interval": 86400,
                "treatment_duration": 20,
                "min_treatment_duration": 1
            },
            "stimulation": {
                "dll_path": "libs/RecoveryDLL.dll",
                "max_current": 100,
                "min_current": 1,
                "port_num": 7,
                "connection_timeout": 5
            },
            "eeg": {
                "serial_port": "COM8",
                "baud_rate": 115200,
                "channels": 8,
                "sample_rate": 125.0
            }
        }
        
        config_file = self.project_root / 'data' / 'user_config_clean.json'
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print("✅ 默认配置文件已创建")
        return str(config_file)
    
    def build_smart_package(self):
        """智能打包"""
        print("\n🚀 开始智能打包...")
        
        # 准备干净配置
        clean_config = self.prepare_clean_config()
        if not clean_config:
            return False
        
        # 构建PyInstaller命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onedir',
            '--windowed',
            '--clean',
            '--noconfirm',
            f'--name={self.build_config["app_name"]}',
            
            # 只包含必要的资源文件
            '--add-data=resources;resources',
            '--add-data=libs;libs',
            f'--add-data={clean_config};data',
            '--add-data=密码.txt;.',
            
            # 隐藏导入
            '--hidden-import=PySide6.QtCore',
            '--hidden-import=PySide6.QtGui', 
            '--hidden-import=PySide6.QtWidgets',
            '--hidden-import=numpy',
            '--hidden-import=matplotlib',
            '--hidden-import=serial',
            '--hidden-import=scipy',
            '--hidden-import=sklearn',
            '--hidden-import=sqlite3',
            
            # 排除不需要的模块
            '--exclude-module=tkinter',
            '--exclude-module=test',
            '--exclude-module=unittest',
            '--exclude-module=pdb',
            
            'main.py'
        ]
        
        try:
            print("执行打包命令...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 打包成功!")
                return True
            else:
                print("❌ 打包失败:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 打包过程中发生错误: {e}")
            return False
        finally:
            # 清理临时文件
            if clean_config and Path(clean_config).exists():
                Path(clean_config).unlink()
    
    def post_build_setup(self):
        """构建后设置"""
        print("\n🔧 构建后设置...")
        
        dist_path = self.project_root / 'dist' / self.build_config["app_name"]
        
        if not dist_path.exists():
            print("❌ 构建目录不存在")
            return False
        
        try:
            # 创建空的数据目录结构
            for dir_name in self.include_patterns['empty_dirs']:
                (dist_path / dir_name).mkdir(parents=True, exist_ok=True)
                
                # 在空目录中创建说明文件
                readme_file = dist_path / dir_name / 'README.txt'
                with open(readme_file, 'w', encoding='utf-8') as f:
                    f.write(f"此目录用于存储{dir_name}相关文件\n")
                    f.write("系统运行时会自动创建相关文件\n")
                    f.write("请勿删除此目录\n")
            
            # 创建启动脚本和说明文档
            self._create_deployment_files(dist_path)
            
            print("✅ 构建后设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 构建后设置失败: {e}")
            return False
    
    def _create_deployment_files(self, dist_path):
        """创建部署文件"""
        # 启动脚本
        startup_script = f'''@echo off
chcp 65001 >nul 2>&1
title {self.build_config["app_name"]}

echo ========================================
echo    {self.build_config["app_name"]}
echo    {self.build_config["company"]}
echo ========================================
echo.
echo 正在启动系统，请稍候...
echo.

"{self.build_config["app_name"]}.exe"

if errorlevel 1 (
    echo.
    echo 启动失败，请检查：
    echo 1. 以管理员身份运行
    echo 2. 添加到杀毒软件白名单
    echo 3. 检查系统权限
    pause
)
'''
        
        with open(dist_path / '启动系统.bat', 'w', encoding='gbk') as f:
            f.write(startup_script)
        
        # 部署说明
        deployment_guide = f'''# {self.build_config["app_name"]} - 部署说明

## 🎯 这是一个干净的发布版本

此版本已经过智能打包，只包含运行必需的文件：
- ✅ 系统程序和库文件
- ✅ 配置文件模板
- ✅ 资源文件
- ❌ 不包含用户数据
- ❌ 不包含训练模型
- ❌ 不包含数据集

## 📁 目录说明

- `{self.build_config["app_name"]}.exe`: 主程序
- `启动系统.bat`: 启动脚本（推荐使用）
- `data/`: 数据目录（首次运行时会自动创建数据库）
- `libs/`: 系统库文件
- `resources/`: 资源文件
- `logs/`: 日志目录（运行时创建）

## 🚀 首次运行

1. 双击"启动系统.bat"启动系统
2. 使用默认账户登录：admin / admin123
3. 配置您的医院信息
4. 开始使用系统

## 💡 重要提示

- 首次运行会自动创建空数据库
- 系统会自动初始化默认数据
- 厂家配置密码见"密码.txt"文件
- 建议添加到杀毒软件白名单

## 📞 技术支持

{self.build_config["company"]}
版本: {self.build_config["version"]}
'''
        
        with open(dist_path / '部署说明.md', 'w', encoding='utf-8') as f:
            f.write(deployment_guide)

def main():
    """主函数"""
    print("🧠 NK脑机接口系统智能打包工具")
    print("=" * 80)
    print("🎯 特点：只打包必要文件，排除用户数据和大文件")
    print("=" * 80)
    
    builder = SmartBuilder()
    
    # 检查环境
    if not builder.check_environment():
        return False
    
    # 分析数据目录
    builder.analyze_data_directory()
    
    # 确认打包
    print("\n" + "=" * 60)
    print("⚠️ 重要提示：")
    print("- 只会打包必要的配置文件模板")
    print("- 不会打包用户数据、训练模型、数据集")
    print("- 目标机器上会创建全新的空数据库")
    print("- 这是推荐的发布方式")
    print("=" * 60)
    
    confirm = input("\n是否继续智能打包？(y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消打包")
        return False
    
    # 执行打包
    if not builder.build_smart_package():
        return False
    
    # 构建后设置
    if not builder.post_build_setup():
        return False
    
    print("\n" + "=" * 80)
    print("🎉 智能打包完成！")
    print("=" * 80)
    print("📁 构建位置: dist/")
    print("💾 大幅减少了包大小")
    print("🔒 源代码完全保护")
    print("🚀 可安全部署到任何机器")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 智能打包失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中发生异常: {e}")
        sys.exit(1)
