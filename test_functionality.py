#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试治疗界面功能完整性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_treatment_ui_functionality():
    """测试治疗界面功能完整性"""
    try:
        print("🔍 开始测试治疗界面功能...")
        
        # 1. 测试导入
        from PySide6.QtWidgets import QApplication
        from ui.treatment_ui import TreatmentWidget
        print("✅ TreatmentWidget 导入成功")

        # 2. 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✅ QApplication 创建成功")

        # 3. 测试实例化
        widget = TreatmentWidget()
        print("✅ TreatmentWidget 实例化成功")
        
        # 4. 检查关键属性
        required_attributes = [
            # 模型管理相关
            'loaded_model_label', 'load_model_button', 'remove_model_button',
            # EEGNet参数相关
            'temperature_spinbox', 'activation_threshold_spin', 'class_weight_spinbox',
            'smoothing_spinbox', 'adaptive_learning_checkbox', 'transfer_learning_checkbox',
            'finetune_layers_spinbox', 'neural_calibrate_btn',
            # 电刺激设备相关
            'stimulation_connect_button', 'channel_a_current', 'channel_b_current',
            'channel_a_status_label', 'channel_b_status_label',
            # 新界面元素
            'mi_detection_label', 'confidence_label', 'stim_status_label',
            'trigger_count_label', 'accuracy_label', 'system_log'
        ]
        
        missing_attributes = []
        for attr in required_attributes:
            if not hasattr(widget, attr):
                missing_attributes.append(attr)
        
        if missing_attributes:
            print(f"❌ 缺失属性: {missing_attributes}")
            return False
        else:
            print("✅ 所有必需属性都存在")
        
        # 5. 检查关键方法
        required_methods = [
            # 模型管理方法
            'load_model', 'remove_model',
            # EEGNet参数方法
            'on_temperature_changed', 'on_activation_threshold_changed',
            'on_class_weight_changed', 'on_smoothing_changed',
            'on_adaptive_learning_toggled', 'on_transfer_learning_toggled',
            'on_finetune_layers_changed', 'on_neural_calibrate',
            # 电刺激设备方法
            'toggle_stimulation_connection', 'connect_stimulation_device',
            'on_channel_a_current_changed', 'on_channel_b_current_changed',
            # 新界面方法
            '_update_treatment_time', '_start_treatment_timer',
            '_stop_treatment_timer', 'update_patient_info',
            'update_device_status_display'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(widget, method) or not callable(getattr(widget, method)):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺失方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需方法都存在")
        
        # 6. 检查AB通道电流初始值
        a_current = widget.channel_a_current.value()
        b_current = widget.channel_b_current.value()
        
        if a_current == 10 and b_current == 10:
            print(f"✅ AB通道电流初始值正确: A={a_current}mA, B={b_current}mA")
        else:
            print(f"❌ AB通道电流初始值错误: A={a_current}mA, B={b_current}mA (期望: 10mA)")
            return False
        
        # 7. 检查信号连接
        try:
            widget.setup_connections()
            print("✅ 信号连接设置成功")
        except Exception as e:
            print(f"❌ 信号连接设置失败: {e}")
            return False
        
        # 8. 检查新界面布局
        layout_elements = [
            'eeg_curves_display', 'eeg_topo_display',  # 脑电信号显示
            'mi_detection_label', 'confidence_label', 'stim_status_label',  # 分类结果
            'trigger_count_label', 'accuracy_label', 'training_progress',  # 治疗状态
            'system_log'  # 系统日志
        ]
        
        missing_layout = []
        for element in layout_elements:
            if not hasattr(widget, element):
                missing_layout.append(element)
        
        if missing_layout:
            print(f"❌ 缺失界面元素: {missing_layout}")
            return False
        else:
            print("✅ 新界面布局元素完整")
        
        print("\n🎉 所有功能测试通过！")
        print("📋 功能恢复总结:")
        print("   ✅ 模型管理功能 - 加载、移除模型")
        print("   ✅ EEGNet参数设置 - 温度、阈值、权重、平滑、学习模式")
        print("   ✅ 电刺激设备连接 - 连接按钮、AB通道控制")
        print("   ✅ AB通道电流初始值 - 正确设置为10mA")
        print("   ✅ 新界面布局 - 脑电信号显示、3栏反馈、治疗计时")
        print("   ✅ 信号连接 - 所有按钮和控件事件绑定")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_treatment_ui_functionality()
    if success:
        print("\n✅ 治疗界面功能完整性测试通过")
        print("💡 所有原有功能已恢复，新界面布局已实现")
    else:
        print("\n❌ 治疗界面功能测试失败")

    sys.exit(0 if success else 1)
