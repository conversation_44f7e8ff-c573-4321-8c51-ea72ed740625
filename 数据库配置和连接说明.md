# 数据库配置和连接说明

## 数据库位置和配置

### 1. 数据库文件位置

**默认数据库位置**：
```
项目根目录/data/nk_system.db
```

**完整路径示例**：
```
d:\NK_QT\QT6\NK\NK\Python_NK_System\data\nk_system.db
```

### 2. 数据库配置结构

在 `utils/app_config.py` 中定义：

```python
DATABASE_CONFIG = {
    'type': 'sqlite',                                    # 数据库类型
    'path': PROJECT_ROOT / 'data' / 'nk_system.db',    # 数据库文件路径
    'backup_path': PROJECT_ROOT / 'data' / 'backup',    # 备份目录
    'auto_backup': True,                                 # 自动备份开关
    'backup_interval': 24 * 60 * 60,                    # 备份间隔(秒)
    'min_treatment_duration': 5,                        # 治疗数据最小时长(分钟)
}
```

### 3. 重置配置后的影响

**重要说明**：重置数据库配置**不会删除**数据库文件，只会重置配置参数。

#### 重置的配置项：
- ✅ 自动备份：重置为 `True`（开启）
- ✅ 备份间隔：重置为 `24小时`
- ✅ 治疗数据最小时长：重置为 `5分钟`

#### 不受影响的内容：
- ❌ 数据库文件路径：保持不变
- ❌ 数据库文件内容：所有数据保持完整
- ❌ 患者信息、治疗记录等：完全保留

## 数据库连接方式

### 1. 自动连接机制

系统启动时会自动连接数据库：

```python
# 在 main.py 中
db_manager = DatabaseManager()
if not db_manager.initialize():
    # 数据库初始化失败处理
```

### 2. 数据库管理器连接流程

```python
class DatabaseManager:
    def __init__(self):
        # 从配置获取数据库路径
        self.db_config = AppConfig.get_config('database')
        self.db_path = Path(self.db_config['path'])
        
    def initialize(self):
        # 1. 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 2. 创建数据库表（如果不存在）
        self._create_tables()
        
        # 3. 初始化基础数据
        self._initialize_base_data()
```

### 3. 连接池管理

系统使用连接池管理数据库连接：

```python
@contextmanager
def get_connection(self):
    """获取数据库连接（线程安全）"""
    thread_id = threading.get_ident()
    
    # 每个线程维护独立的连接
    if thread_id not in self._connection_pool:
        conn = sqlite3.connect(
            str(self.db_path),
            check_same_thread=False,
            timeout=30.0
        )
        self._connection_pool[thread_id] = conn
```

## 数据库文件管理

### 1. 数据库文件结构

```
data/
├── nk_system.db          # 主数据库文件
├── backup/               # 备份目录
│   ├── backup_20241219_143022.db
│   └── backup_20241218_143022.db
└── user_config.json      # 用户配置文件
```

### 2. 数据库表结构

主要数据表：
- `yiyuan` - 医院信息表
- `bingren` - 患者信息表
- `zhiliao` - 治疗记录表
- `Edata` - 脑电数据表
- `operator` - 操作员表
- `doctor` - 医生表
- `system_logs` - 系统日志表
- `audit_logs` - 操作审计表
- `device_status` - 设备状态表

### 3. 数据库备份机制

**自动备份**：
- 默认每24小时自动备份一次
- 备份文件保存在 `data/backup/` 目录
- 备份文件命名格式：`backup_YYYYMMDD_HHMMSS.db`

**手动备份**：
- 在设置界面点击"备份数据库"按钮
- 可以在数据库管理标签页进行操作

## 如何重新连接数据库

### 1. 正常情况下

系统会自动连接数据库，无需手动操作。

### 2. 连接失败时的处理

如果数据库连接失败，系统会：

1. **自动创建目录**：如果 `data` 目录不存在，会自动创建
2. **自动创建数据库**：如果数据库文件不存在，会自动创建
3. **自动创建表结构**：确保所有必要的表都存在
4. **初始化基础数据**：创建默认管理员账户和医院信息

### 3. 手动重新连接

如果需要手动重新连接：

```python
# 重新初始化数据库管理器
db_manager = DatabaseManager()
success = db_manager.initialize()

if success:
    print("数据库连接成功")
else:
    print("数据库连接失败")
```

## 故障排除

### 1. 数据库文件丢失

如果数据库文件意外丢失：

1. **检查备份目录**：`data/backup/` 中查找最新备份
2. **恢复备份**：将备份文件复制为 `nk_system.db`
3. **重启系统**：系统会自动连接恢复的数据库

### 2. 数据库损坏

如果数据库文件损坏：

1. **使用备份恢复**：从 `data/backup/` 选择最新的备份文件
2. **重新初始化**：删除损坏的数据库文件，系统会重新创建

### 3. 权限问题

如果遇到权限问题：

1. **检查目录权限**：确保 `data` 目录有读写权限
2. **以管理员身份运行**：在Windows上以管理员身份运行程序

## 配置文件位置

### 用户配置文件

**位置**：`data/user_config.json`

**包含的数据库配置**：
```json
{
  "database": {
    "type": "sqlite",
    "path": "d:\\NK_QT\\QT6\\NK\\NK\\Python_NK_System\\data\\nk_system.db",
    "backup_path": "d:\\NK_QT\\QT6\\NK\\NK\\Python_NK_System\\data\\backup",
    "auto_backup": true,
    "backup_interval": 86400,
    "min_treatment_duration": 5
  }
}
```

## 总结

1. **数据库位置固定**：`项目根目录/data/nk_system.db`
2. **重置配置安全**：只重置配置参数，不影响数据
3. **自动连接机制**：系统启动时自动连接
4. **备份保护机制**：自动备份确保数据安全
5. **故障自动恢复**：数据库文件丢失时可自动重建

**重要提醒**：重置数据库配置是安全的操作，不会丢失任何患者数据或治疗记录。
