#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时显示功能测试脚本
测试8通道实时曲线和脑电地形图显示功能
"""

import sys
import os
import logging
import numpy as np
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    from ui.treatment_ui import RealTimeEEGCurves, MNETopographyDisplay
    print("✅ 成功导入实时显示组件")
except ImportError as e:
    print(f"❌ 导入实时显示组件失败: {e}")
    sys.exit(1)


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("实时脑电显示测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始模拟数据")
        self.start_button.clicked.connect(self.start_simulation)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止模拟数据")
        self.stop_button.clicked.connect(self.stop_simulation)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # 创建显示区域
        display_layout = QHBoxLayout()
        
        # 实时曲线显示
        self.curves_display = RealTimeEEGCurves()
        display_layout.addWidget(self.curves_display)
        
        # 脑电地形图显示
        self.topo_display = MNETopographyDisplay()
        display_layout.addWidget(self.topo_display)
        
        layout.addLayout(display_layout)
        
        # 创建数据生成定时器
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.generate_test_data)
        self.data_timer.setInterval(8)  # 125Hz = 8ms间隔
        
        # 地形图更新定时器
        self.topo_timer = QTimer()
        self.topo_timer.timeout.connect(self.update_topography)
        self.topo_timer.setInterval(500)  # 2Hz更新，更流畅
        
        # 数据缓冲区
        self.data_buffer = []
        self.time_counter = 0
        
        print("✅ 测试窗口初始化完成")
        
    def start_simulation(self):
        """开始数据模拟"""
        print("🚀 开始模拟脑电数据...")
        self.data_timer.start()
        self.topo_timer.start()
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
    def stop_simulation(self):
        """停止数据模拟"""
        print("⏹️ 停止模拟脑电数据")
        self.data_timer.stop()
        self.topo_timer.stop()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
    def generate_test_data(self):
        """生成测试数据"""
        try:
            # 生成8通道的模拟脑电数据
            self.time_counter += 1
            t = self.time_counter * 0.008  # 时间（秒）
            
            # 模拟不同频率的脑电信号
            channels_data = []
            
            for ch in range(8):
                # 基础频率：8-12Hz (α节律)
                alpha_freq = 10 + ch * 0.5
                alpha_signal = 50 * np.sin(2 * np.pi * alpha_freq * t)
                
                # β节律：13-30Hz
                beta_freq = 20 + ch * 1.0
                beta_signal = 20 * np.sin(2 * np.pi * beta_freq * t)
                
                # 添加噪声
                noise = np.random.normal(0, 5)
                
                # 模拟运动想象（每10秒一次）
                if int(t) % 10 < 3:  # 前3秒模拟运动想象
                    motor_signal = 30 * np.sin(2 * np.pi * 12 * t)  # μ节律抑制
                else:
                    motor_signal = 0
                
                # 合成信号
                signal = alpha_signal + beta_signal + motor_signal + noise
                channels_data.append(signal)
            
            # 转换为numpy数组
            data = np.array(channels_data)
            
            # 更新实时曲线
            self.curves_display.update_data(data.reshape(8, 1))
            
            # 添加到缓冲区用于地形图
            self.data_buffer.append(data)
            if len(self.data_buffer) > 125:  # 保持1秒数据
                self.data_buffer.pop(0)
                
        except Exception as e:
            print(f"❌ 生成测试数据失败: {e}")
            
    def update_topography(self):
        """更新地形图"""
        try:
            if len(self.data_buffer) > 0:
                # 计算最近1秒数据的RMS值
                recent_data = np.array(self.data_buffer[-125:])  # 最近1秒
                rms_values = np.sqrt(np.mean(recent_data ** 2, axis=0))
                
                # 更新地形图
                self.topo_display.update_topography(rms_values)
                
        except Exception as e:
            print(f"❌ 更新地形图失败: {e}")


def main():
    """主函数"""
    print("🧠 启动实时脑电显示测试...")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("✅ 测试窗口已启动")
    print("📝 使用说明：")
    print("   1. 点击'开始模拟数据'按钮开始测试")
    print("   2. 观察8通道实时曲线和脑电地形图")
    print("   3. 每10秒会模拟一次运动想象信号")
    print("   4. 点击'停止模拟数据'按钮停止测试")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
