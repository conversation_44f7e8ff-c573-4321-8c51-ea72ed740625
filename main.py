#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑机接口康复训练系统 - 主程序入口
Brain-Computer Interface Rehabilitation Training System - Main Entry Point

作者: AI Assistant
版本: 1.0.0
创建日期: 2024年
"""

import sys
import os
import logging
import platform
import time
from pathlib import Path
from datetime import datetime

# 启动优化设置
os.environ['PYTHONOPTIMIZE'] = '1'  # 启用Python优化
os.environ['QT_LOGGING_RULES'] = '*.debug=false'  # 禁用Qt调试日志
os.environ['QT_QUICK_CONTROLS_STYLE'] = 'Basic'  # 使用基础样式

# 设置基本的环境变量（PySide6会自动处理Qt插件加载）
# 只在Windows平台上设置必要的环境变量
if platform.system() == 'Windows':
    # 设置Qt平台（通常PySide6会自动检测，但明确设置更安全）
    os.environ['QT_QPA_PLATFORM'] = 'windows'

# 检查PySide6是否可用
try:
    from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtGui import QPixmap, QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.logger_system import LoggerSystem
from utils.app_config import AppConfig
from utils.single_instance import SingleInstance

# 条件导入主窗口和权限管理器
if PYSIDE6_AVAILABLE:
    from core.main_window import MainWindow
    from core.auth_manager import AuthManager
    from core.performance_optimizer import PerformanceOptimizer


class NKApplication:
    """NK脑机接口应用程序主类"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.splash = None
        self.logger = None
        self.db_manager = None
        self.auth_manager = None
        self.performance_optimizer = None
        self.single_instance = None
        self.startup_time = time.time()  # 记录启动时间

    def initialize_logging(self):
        """初始化日志系统"""
        try:
            self.logger = LoggerSystem()
            self.logger.setup_logging()
            logging.info("=== 脑机接口康复训练系统启动 ===")
            logging.info(f"系统版本: {AppConfig.VERSION}")
            logging.info(f"Python版本: {sys.version}")
        except Exception as e:
            print(f"日志系统初始化失败: {e}")

    def check_single_instance(self):
        """检查是否已有实例运行"""
        try:
            self.single_instance = SingleInstance()
            if not self.single_instance.is_single():
                QMessageBox.warning(
                    None,
                    "警告",
                    "程序已经在运行中，请勿重复启动！",
                    QMessageBox.Ok
                )
                return False
            return True
        except Exception as e:
            logging.error(f"单实例检查失败: {e}")
            return True  # 如果检查失败，允许启动

    def setup_application(self):
        """设置应用程序基本属性"""
        try:
            # 设置高DPI支持
            QApplication.setHighDpiScaleFactorRoundingPolicy(
                Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
            )

            # 创建应用程序实例
            self.app = QApplication(sys.argv)

            # 设置应用程序信息
            self.app.setApplicationName("脑机接口康复训练系统")
            self.app.setApplicationVersion(AppConfig.VERSION)
            self.app.setOrganizationName("山东海天智能")
            self.app.setOrganizationDomain("www.zhhtzn.com")

            # 设置默认字体
            font = QFont("Microsoft YaHei", 10)
            self.app.setFont(font)

            # 设置样式表
            self.load_stylesheet()

            logging.info("应用程序基本设置完成")
            return True

        except Exception as e:
            logging.error(f"应用程序设置失败: {e}")
            return False

    def load_stylesheet(self):
        """加载样式表"""
        try:
            style_file = project_root / "resources" / "styles" / "main_style.qss"
            if style_file.exists():
                with open(style_file, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
                self.app.setStyleSheet(stylesheet)
                logging.info("样式表加载成功")
            else:
                logging.warning("样式表文件不存在，使用默认样式")
        except Exception as e:
            logging.error(f"样式表加载失败: {e}")

    def show_splash_screen(self):
        """显示启动画面"""
        try:
            splash_image = project_root / "resources" / "images" / "splash.png"
            if splash_image.exists():
                pixmap = QPixmap(str(splash_image))
                self.splash = QSplashScreen(pixmap)
                self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
                self.splash.show()
                self.splash.showMessage(
                    "正在初始化系统...",
                    Qt.AlignBottom | Qt.AlignCenter,
                    Qt.white
                )
                self.app.processEvents()
                logging.info("启动画面显示成功")
            else:
                logging.warning("启动画面图片不存在")
        except Exception as e:
            logging.error(f"启动画面显示失败: {e}")

    def optimize_database_performance(self):
        """优化数据库性能"""
        try:
            if self.db_manager:
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()

                    # SQLite性能优化设置
                    optimizations = [
                        "PRAGMA journal_mode = WAL",      # 写前日志模式
                        "PRAGMA synchronous = NORMAL",    # 正常同步模式
                        "PRAGMA cache_size = 10000",      # 增加缓存大小
                        "PRAGMA temp_store = MEMORY",     # 临时存储在内存
                        "PRAGMA mmap_size = 268435456",   # 内存映射大小 (256MB)
                    ]

                    for pragma in optimizations:
                        cursor.execute(pragma)

                    logging.info("数据库性能优化设置已应用")
        except Exception as e:
            logging.warning(f"数据库性能优化失败: {e}")

    def initialize_database(self):
        """初始化数据库"""
        try:
            if self.splash:
                self.splash.showMessage(
                    "正在初始化数据库...",
                    Qt.AlignBottom | Qt.AlignCenter,
                    Qt.white
                )
                self.app.processEvents()

            self.db_manager = DatabaseManager()
            if self.db_manager.initialize():
                # 应用数据库性能优化
                self.optimize_database_performance()
                logging.info("数据库初始化成功")
                return True
            else:
                logging.error("数据库初始化失败")
                return False

        except Exception as e:
            logging.error(f"数据库初始化异常: {e}")
            return False

    def create_main_window(self):
        """创建主窗口"""
        try:
            if self.splash:
                self.splash.showMessage(
                    "正在创建主界面...",
                    Qt.AlignBottom | Qt.AlignCenter,
                    Qt.white
                )
                self.app.processEvents()

            self.main_window = MainWindow()

            # 初始化权限管理器
            self.auth_manager = AuthManager(self.db_manager)

            # 初始化性能优化器
            self.performance_optimizer = PerformanceOptimizer()

            # 设置数据库管理器、权限管理器和性能优化器
            self.main_window.set_database_manager(self.db_manager)
            self.main_window.set_auth_manager(self.auth_manager)
            self.main_window.set_performance_optimizer(self.performance_optimizer)

            logging.info("主窗口创建成功")
            return True

        except Exception as e:
            logging.error(f"主窗口创建失败: {e}")
            return False

    def show_main_window(self):
        """显示主窗口"""
        try:
            if self.splash:
                # 延迟关闭启动画面
                QTimer.singleShot(1000, self.splash.close)

            self.main_window.show()
            self.main_window.showMaximized()  # 最大化显示

            # 计算并记录启动时间
            total_startup_time = time.time() - self.startup_time
            logging.info(f"主窗口显示成功，总启动时间: {total_startup_time:.2f}s")

        except Exception as e:
            logging.error(f"主窗口显示失败: {e}")

    def run(self):
        """运行应用程序"""
        try:
            # 1. 初始化日志系统
            self.initialize_logging()

            # 2. 检查PySide6依赖
            if not PYSIDE6_AVAILABLE:
                print("=" * 60)
                print("错误：缺少必需的依赖包")
                print("=" * 60)
                print("PySide6未安装，无法启动图形界面。")
                print("\n解决方案：")
                print("1. 安装PySide6：")
                print("   pip install PySide6")
                print("\n2. 或安装所有依赖：")
                print("   python install_dependencies.py")
                print("\n3. 或运行核心功能测试：")
                print("   python test_core_functions.py")
                print("=" * 60)
                input("按回车键退出...")
                return 1

            # 3. 设置应用程序
            if not self.setup_application():
                return 1

            # 4. 检查单实例
            if not self.check_single_instance():
                return 1

            # 5. 显示启动画面
            self.show_splash_screen()

            # 6. 初始化数据库
            if not self.initialize_database():
                QMessageBox.critical(
                    None,
                    "错误",
                    "数据库初始化失败，程序无法启动！",
                    QMessageBox.Ok
                )
                return 1

            # 7. 创建主窗口
            if not self.create_main_window():
                QMessageBox.critical(
                    None,
                    "错误",
                    "主窗口创建失败，程序无法启动！",
                    QMessageBox.Ok
                )
                return 1

            # 8. 显示主窗口
            self.show_main_window()

            # 9. 运行应用程序事件循环
            logging.info("应用程序启动完成，进入事件循环")
            return self.app.exec()

        except Exception as e:
            logging.error(f"应用程序运行异常: {e}")
            if hasattr(self, 'app') and self.app:
                QMessageBox.critical(
                    None,
                    "严重错误",
                    f"应用程序运行异常：{str(e)}",
                    QMessageBox.Ok
                )
            return 1

    def cleanup(self):
        """清理资源"""
        try:
            logging.info("开始清理应用程序资源")

            if self.main_window:
                self.main_window.cleanup()

            if self.db_manager:
                self.db_manager.close()

            if self.single_instance:
                self.single_instance.cleanup()

            logging.info("应用程序资源清理完成")
            logging.info("=== 脑机接口康复训练系统退出 ===")

        except Exception as e:
            logging.error(f"资源清理异常: {e}")


def main():
    """主函数"""
    # 检查是否为测试模式
    if "--test-mode" in sys.argv:
        print("启动测试模式...")

        # 模拟启动过程但不显示界面
        class TestNKApplication(NKApplication):
            def show_splash_screen(self):
                pass  # 跳过启动画面

            def show_main_window(self):
                # 记录启动完成时间
                total_startup_time = time.time() - self.startup_time
                print(f"测试模式启动完成: {total_startup_time:.2f}s")

                # 立即退出
                if self.app:
                    self.app.quit()

        nk_app = TestNKApplication()
    else:
        nk_app = NKApplication()

    try:
        exit_code = nk_app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        exit_code = 0
    except Exception as e:
        print(f"程序运行异常: {e}")
        exit_code = 1
    finally:
        nk_app.cleanup()

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
