#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脑电原始数据存储功能
Test EEG Raw Data Storage Functionality

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.training_data_integration import TrainingDataIntegration
from core.eeg_data_loader import EEGDataLoader
from core.eeg_device import EEGDataPacket
from core.motor_imagery_trainer import TrainingState
from utils.app_config import AppConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_data_management.log')
        ]
    )

def create_test_data():
    """创建测试数据"""
    # 模拟8通道脑电数据
    channels = 8
    samples_per_packet = 25  # 每包25个样本
    sampling_rate = 125  # 125Hz采样率
    
    # 生成4秒的数据（500个包）
    packets = []
    for i in range(500):
        # 生成随机脑电数据
        data = np.random.randn(channels, samples_per_packet) * 50  # 50μV幅度
        
        # 添加一些模拟的运动想象信号
        if 100 <= i < 200:  # 运动想象期间
            # 在C3, C4通道添加模拟的ERD/ERS
            data[2, :] += np.sin(2 * np.pi * 10 * np.arange(samples_per_packet) / sampling_rate) * 20
            data[3, :] += np.cos(2 * np.pi * 12 * np.arange(samples_per_packet) / sampling_rate) * 15
        
        # 创建数据包 - 需要4组数据，每组8个通道
        # 将单组数据复制为4组
        channel_data_4groups = []
        for group in range(4):
            group_data = []
            for ch in range(channels):
                # 转换为整数（模拟ADS1299的24位数据）
                value = int(data[ch, group * samples_per_packet // 4] * 1000)  # 转换为微伏
                group_data.append(value)
            channel_data_4groups.append(group_data)

        packet = EEGDataPacket(
            timestamp=datetime.now().timestamp() + i * (samples_per_packet / sampling_rate),
            channel_data=channel_data_4groups,
            packet_number=i,
            is_valid=True
        )
        packets.append(packet)
    
    return packets

def test_training_data_integration():
    """测试训练数据集成功能"""
    print("=" * 60)
    print("测试训练数据集成功能")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 初始化训练数据集成
        integration = TrainingDataIntegration(db_manager)

        # 测试患者ID
        test_patient_id = 9999  # 使用数字ID

        # 先创建测试医院
        print("🏥 创建测试医院...")
        hospital_sql = """
            INSERT OR IGNORE INTO yiyuan (id, hname, keshi, shebeiid)
            VALUES (?, ?, ?, ?)
        """
        hospital_params = (1, "测试医院", "测试科室", "001")
        db_manager.execute_non_query(hospital_sql, hospital_params)
        print("✅ 测试医院创建成功")

        # 先创建测试患者
        print(f"👤 创建测试患者 {test_patient_id}...")
        patient_sql = """
            INSERT OR IGNORE INTO bingren (bianhao, name, xingbie, age, czy, keshi, shebeiid, yiyuanid)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        patient_params = (test_patient_id, "测试患者", "男", 30, "测试操作员", "测试科室", "001", 1)
        db_manager.execute_non_query(patient_sql, patient_params)
        print("✅ 测试患者创建成功")

        print(f"📊 开始测试患者 {test_patient_id} 的数据记录...")
        
        # 开始训练会话
        success = integration.start_training_session(test_patient_id)
        if not success:
            print("❌ 启动训练会话失败")
            return False
        
        print("✅ 训练会话启动成功")
        
        # 创建测试数据
        test_packets = create_test_data()
        print(f"📦 生成了 {len(test_packets)} 个测试数据包")
        
        # 模拟训练过程
        trial_count = 0
        
        # 第一个试验：运动想象
        print("\n🧠 开始运动想象试验...")
        success = integration.start_trial_recording(
            training_state=TrainingState.MOTOR_IMAGERY,
            round_number=1,
            trial_number=trial_count
        )
        
        if success:
            print("✅ 运动想象试验记录开始")
            
            # 处理运动想象期间的数据
            for i, packet in enumerate(test_packets[100:200]):  # 运动想象期间
                integration.process_eeg_data(packet)
                if i % 50 == 0:
                    print(f"  📈 已处理 {i+1}/100 个数据包")
            
            # 结束试验记录
            success = integration.end_trial_recording()
            if success:
                print("✅ 运动想象试验记录完成")
                trial_count += 1
            else:
                print("❌ 运动想象试验记录结束失败")
        else:
            print("❌ 运动想象试验记录开始失败")
        
        # 第二个试验：休息状态
        print("\n😌 开始休息状态试验...")
        success = integration.start_trial_recording(
            training_state=TrainingState.QUIET,
            round_number=1,
            trial_number=trial_count
        )
        
        if success:
            print("✅ 休息状态试验记录开始")
            
            # 处理休息期间的数据
            for i, packet in enumerate(test_packets[300:400]):  # 休息期间
                integration.process_eeg_data(packet)
                if i % 50 == 0:
                    print(f"  📈 已处理 {i+1}/100 个数据包")
            
            # 结束试验记录
            success = integration.end_trial_recording()
            if success:
                print("✅ 休息状态试验记录完成")
                trial_count += 1
            else:
                print("❌ 休息状态试验记录结束失败")
        else:
            print("❌ 休息状态试验记录开始失败")
        
        # 结束训练会话
        success = integration.end_training_session()
        if success:
            print(f"\n✅ 训练会话结束，共记录 {trial_count} 个试验")
        else:
            print("\n❌ 训练会话结束失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loader():
    """测试数据加载功能"""
    print("\n" + "=" * 60)
    print("测试数据加载功能")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 初始化数据加载器
        loader = EEGDataLoader(db_manager)
        
        # 获取数据统计
        print("📊 获取数据统计...")
        stats = loader.get_data_statistics()
        
        print(f"  总患者数: {stats.get('total_patients', 0)}")
        print(f"  总会话数: {stats.get('total_sessions', 0)}")
        print(f"  总试验数: {stats.get('total_trials', 0)}")
        print(f"  数据大小: {(stats.get('total_size', 0) or 0) / 1024 / 1024:.2f} MB")
        avg_quality = stats.get('avg_quality', 0) or 0
        print(f"  平均质量: {avg_quality:.3f}")
        
        # 搜索试验数据
        print("\n🔍 搜索试验数据...")
        criteria = {
            'patient_ids': [9999],
            'limit': 10
        }
        
        trials = loader.search_trials(criteria)
        print(f"  找到 {len(trials)} 个试验")
        
        for i, trial in enumerate(trials):
            print(f"  试验 {i+1}:")
            print(f"    患者ID: {trial.get('patient_id', '')}")
            print(f"    会话ID: {trial.get('session_id', '')}")
            print(f"    标签: {'运动想象' if trial.get('label', 0) == 1 else '休息'}")
            print(f"    质量: {trial.get('data_quality', 0):.3f}")
            print(f"    时长: {trial.get('duration_seconds', 0):.1f} 秒")
            print(f"    文件大小: {(trial.get('file_size_bytes', 0) or 0) / 1024:.2f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧠 脑电原始数据存储功能测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 测试训练数据集成
    success1 = test_training_data_integration()
    
    # 测试数据加载
    success2 = test_data_loader()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success1:
        print("✅ 训练数据集成测试通过")
    else:
        print("❌ 训练数据集成测试失败")
    
    if success2:
        print("✅ 数据加载测试通过")
    else:
        print("❌ 数据加载测试失败")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！脑电原始数据存储功能正常工作")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
