#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终状态修复验证测试
验证通道状态映射和UI显示是否正确
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('final_status_fix_test.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_status_mapping_consistency():
    """测试状态映射一致性"""
    print("🔧 测试状态映射一致性")
    print("=" * 50)
    
    try:
        # 创建设备实例
        device = StimulationDevice()
        
        print("\n📋 验证设备模块状态映射:")
        device_status_map = {
            0: "停止",
            1: "暂停", 
            2: "电流调节",
            3: "正常工作"
        }
        
        for status_code, expected_text in device_status_map.items():
            actual_text = device._get_channel_status_text(status_code)
            if actual_text == expected_text:
                print(f"   ✅ 状态码 {status_code}: {actual_text}")
            else:
                print(f"   ❌ 状态码 {status_code}: 期望 '{expected_text}', 实际 '{actual_text}'")
                return False
        
        print("\n📋 验证UI界面状态映射:")
        # 模拟UI界面的_get_channel_display_info方法
        def get_channel_display_info(status: int) -> tuple:
            """根据通道状态获取显示信息"""
            if status == 0:  # 停止
                return "关闭", ""
            elif status == 1:  # 暂停
                return "暂停", "color: orange; font-weight: bold;"
            elif status == 2:  # 电流调节
                return "电流调节", "color: blue; font-weight: bold;"
            elif status == 3:  # 正常工作
                return "刺激中", "color: green; font-weight: bold;"
            else:
                return f"未知({status})", "color: red; font-weight: bold;"
        
        ui_expected_map = {
            0: "关闭",
            1: "暂停",
            2: "电流调节", 
            3: "刺激中"
        }
        
        for status_code, expected_display in ui_expected_map.items():
            actual_display, _ = get_channel_display_info(status_code)
            if actual_display == expected_display:
                print(f"   ✅ 状态码 {status_code}: {actual_display}")
            else:
                print(f"   ❌ 状态码 {status_code}: 期望 '{expected_display}', 实际 '{actual_display}'")
                return False
        
        print("\n📋 验证状态验证逻辑:")
        # 测试_verify_stimulation_active函数
        test_cases = [
            (0, False, "停止状态不应该被认为是激活的"),
            (1, False, "暂停状态不应该被认为是激活的"),
            (2, False, "电流调节状态不应该被认为是激活的"),
            (3, True, "正常工作状态应该被认为是激活的")
        ]
        
        for status_code, expected_active, description in test_cases:
            device.channel_a_status = status_code
            is_active = device._verify_stimulation_active(1)
            if is_active == expected_active:
                print(f"   ✅ 状态码 {status_code}: {description}")
            else:
                print(f"   ❌ 状态码 {status_code}: {description} - 验证失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_device_consistency():
    """测试UI界面与设备模块的一致性"""
    print("\n🔍 测试UI界面与设备模块的一致性")
    print("-" * 40)
    
    # 设备模块状态映射
    device_map = {
        0: "停止",
        1: "暂停",
        2: "电流调节", 
        3: "正常工作"
    }
    
    # UI界面状态映射
    ui_map = {
        0: "关闭",    # UI中显示为"关闭"而不是"停止"
        1: "暂停",
        2: "电流调节",
        3: "刺激中"   # UI中显示为"刺激中"而不是"正常工作"
    }
    
    print("状态码 | 设备模块    | UI界面     | 说明")
    print("-------|-------------|------------|------------------")
    
    for status_code in range(4):
        device_text = device_map[status_code]
        ui_text = ui_map[status_code]
        
        # 检查语义一致性
        if (status_code == 0 and device_text == "停止" and ui_text == "关闭") or \
           (status_code == 1 and device_text == "暂停" and ui_text == "暂停") or \
           (status_code == 2 and device_text == "电流调节" and ui_text == "电流调节") or \
           (status_code == 3 and device_text == "正常工作" and ui_text == "刺激中"):
            consistency = "✅ 一致"
        else:
            consistency = "❌ 不一致"
        
        print(f"{status_code:6d} | {device_text:11s} | {ui_text:10s} | {consistency}")
    
    print("\n📝 说明:")
    print("   - 状态0: 设备'停止' = UI'关闭' (语义一致)")
    print("   - 状态1: 设备'暂停' = UI'暂停' (完全一致)")
    print("   - 状态2: 设备'电流调节' = UI'电流调节' (完全一致)")
    print("   - 状态3: 设备'正常工作' = UI'刺激中' (语义一致)")
    
    return True

def main():
    """主函数"""
    setup_logging()
    
    print("🔧 NK电刺激设备最终状态修复验证")
    print("验证状态映射和UI显示的正确性")
    print("=" * 60)
    
    success1 = test_status_mapping_consistency()
    success2 = test_ui_device_consistency()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ 最终状态修复验证成功！")
        print("🎯 验证结果：")
        print("   - ✅ 设备模块状态映射正确")
        print("   - ✅ UI界面状态映射正确")
        print("   - ✅ 状态验证逻辑正确")
        print("   - ✅ 设备与UI语义一致")
        print("\n📋 状态对应关系：")
        print("   0: 停止/关闭 -> 设备未工作")
        print("   1: 暂停 -> 设备暂停状态")
        print("   2: 电流调节 -> 设备正在调节电流")
        print("   3: 正常工作/刺激中 -> 设备正在输出刺激")
    else:
        print("❌ 最终状态修复验证失败")
        print("需要进一步检查修复内容")
    
    print("📄 详细日志：final_status_fix_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
