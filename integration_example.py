#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始数据存储集成示例
Integration Example for Raw Data Storage

展示如何将原始数据存储功能集成到现有的治疗界面中

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import numpy as np
from datetime import datetime
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.treatment_data_integration import TreatmentDataIntegration
from core.database_manager import DatabaseManager
from core.eeg_device import EEGDataPacket


class EnhancedTreatmentUI:
    """增强的治疗界面示例（集成原始数据存储）"""
    
    def __init__(self):
        """初始化治疗界面"""
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        
        # 初始化治疗数据集成管理器
        self.data_integration = TreatmentDataIntegration(self.db_manager)
        
        # 治疗状态
        self.current_patient_id: Optional[int] = None
        self.current_treatment_id: Optional[int] = None
        self.is_treatment_active = False
        self.current_trial_state = 'idle'  # idle, motor_imagery, rest
        
        print("增强治疗界面初始化完成（包含原始数据存储）")
    
    def start_treatment(self, patient_id: int, treatment_id: Optional[int] = None):
        """开始治疗"""
        try:
            print(f"\n🏥 开始治疗 - 患者ID: {patient_id}")
            
            # 保存患者信息
            self.current_patient_id = patient_id
            self.current_treatment_id = treatment_id
            
            # 开始治疗数据会话
            success = self.data_integration.start_treatment_session(
                patient_id=patient_id,
                treatment_id=treatment_id
            )
            
            if success:
                self.is_treatment_active = True
                print("✅ 治疗会话开始成功")
                print("📊 原始数据记录已启动")
            else:
                print("⚠️  治疗会话开始失败，但治疗可以继续")
            
            # 显示会话统计
            stats = self.data_integration.get_session_statistics()
            if stats:
                print(f"📈 会话信息: {stats}")
            
        except Exception as e:
            print(f"❌ 开始治疗失败: {e}")
    
    def start_motor_imagery_trial(self):
        """开始运动想象试验"""
        if not self.is_treatment_active:
            print("⚠️  治疗未激活")
            return
        
        print("\n🧠 开始运动想象试验")
        
        # 开始试验数据记录
        success = self.data_integration.start_trial_recording('motor_imagery')
        if success:
            self.current_trial_state = 'motor_imagery'
            print("📊 运动想象数据记录已开始")
        else:
            print("⚠️  数据记录开始失败")
    
    def start_rest_trial(self):
        """开始休息试验"""
        if not self.is_treatment_active:
            print("⚠️  治疗未激活")
            return
        
        print("\n😌 开始休息试验")
        
        # 开始试验数据记录
        success = self.data_integration.start_trial_recording('rest')
        if success:
            self.current_trial_state = 'rest'
            print("📊 休息状态数据记录已开始")
        else:
            print("⚠️  数据记录开始失败")
    
    def process_eeg_data(self, eeg_data_packet: EEGDataPacket):
        """处理脑电数据包"""
        if not self.is_treatment_active:
            return
        
        # 将数据传递给数据集成管理器
        self.data_integration.process_eeg_data(eeg_data_packet)
        
        # 这里可以添加实时处理逻辑
        # 例如：运动想象分类、实时反馈等
    
    def end_current_trial(self):
        """结束当前试验"""
        if self.current_trial_state == 'idle':
            return
        
        print(f"\n✅ 结束{self.current_trial_state}试验")
        
        # 结束试验数据记录
        success = self.data_integration.end_trial_recording()
        if success:
            print("📊 试验数据保存成功")
        else:
            print("⚠️  试验数据保存失败")
        
        self.current_trial_state = 'idle'
        
        # 显示会话统计
        stats = self.data_integration.get_session_statistics()
        if stats:
            print(f"📈 当前统计: 试验{stats['current_trial']}/{stats['total_trials']}")
    
    def end_treatment(self):
        """结束治疗"""
        if not self.is_treatment_active:
            return
        
        print(f"\n🏁 结束治疗 - 患者ID: {self.current_patient_id}")
        
        # 如果有正在进行的试验，先结束它
        if self.current_trial_state != 'idle':
            self.end_current_trial()
        
        # 结束治疗数据会话
        success = self.data_integration.end_treatment_session()
        if success:
            print("✅ 治疗会话结束成功")
            print("📊 原始数据记录已停止")
        else:
            print("⚠️  治疗会话结束失败")
        
        # 重置状态
        self.is_treatment_active = False
        self.current_patient_id = None
        self.current_treatment_id = None
        self.current_trial_state = 'idle'
    
    def get_session_training_data(self):
        """获取当前会话的训练数据"""
        if not self.is_treatment_active:
            return None
        
        session_data = self.data_integration.get_session_data_for_training()
        if session_data:
            data, labels = session_data
            print(f"📊 当前会话训练数据: {len(data)}个试验")
            return data, labels
        
        return None
    
    def get_patient_historical_data(self, max_sessions: int = 5):
        """获取患者历史数据"""
        if not self.current_patient_id:
            return None
        
        historical_data = self.data_integration.get_patient_historical_data(
            patient_id=self.current_patient_id,
            max_sessions=max_sessions
        )
        
        if historical_data:
            data, labels = historical_data
            print(f"📊 患者历史数据: {len(data)}个试验")
            return data, labels
        
        return None
    
    def show_data_statistics(self):
        """显示数据统计信息"""
        if not self.current_patient_id:
            print("⚠️  没有当前患者")
            return
        
        from core.eeg_data_loader import EEGDataLoader
        
        data_loader = EEGDataLoader(self.db_manager)
        
        # 获取患者数据统计
        stats = data_loader.get_data_statistics([self.current_patient_id])
        
        print(f"\n📊 患者{self.current_patient_id}数据统计:")
        print(f"   总试验数: {stats.get('total_trials', 0)}")
        print(f"   总会话数: {stats.get('total_sessions', 0)}")
        avg_quality = stats.get('avg_quality') or 0
        total_size = stats.get('total_size') or 0
        print(f"   平均质量: {avg_quality:.3f}")
        print(f"   数据大小: {total_size / 1024 / 1024:.2f} MB")
        
        # 标签分布
        label_dist = stats.get('label_distribution', {})
        if label_dist:
            print(f"   标签分布:")
            for label, count in label_dist.items():
                label_name = "运动想象" if label == "1" else "休息"
                print(f"     {label_name}: {count}")
        
        # 质量分布
        quality_dist = stats.get('quality_distribution', {})
        if quality_dist:
            print(f"   质量分布:")
            for quality, count in quality_dist.items():
                print(f"     {quality}: {count}")


def simulate_treatment_session():
    """模拟一个完整的治疗会话"""
    print("🎯 模拟治疗会话开始")
    print("="*60)

    # 创建治疗界面
    treatment_ui = EnhancedTreatmentUI()

    # 创建测试患者记录
    patient_id = 123
    patient_data = {
        'bianhao': patient_id,
        'name': '测试患者',
        'age': 45,
        'xingbie': '男',
        'cardid': '123456789012345678',
        'zhenduan': '脑卒中',
        'bingshi': '测试病史',
        'shebeiid': 'NK001',
        'yiyuanid': 1,
        'keshi': '康复科',
        'zhuzhi': '张医生',
        'czy': 'admin',
        'brhc': '测试备注'
    }

    # 尝试添加患者记录（如果不存在）
    try:
        treatment_ui.db_manager.add_patient(patient_data)
        print(f"✅ 创建测试患者记录: {patient_id}")
    except:
        print(f"ℹ️  患者记录已存在: {patient_id}")

    # 开始治疗
    treatment_ui.start_treatment(patient_id)
    
    # 模拟多个试验
    for trial_idx in range(6):
        print(f"\n--- 试验 {trial_idx + 1} ---")
        
        # 交替进行运动想象和休息试验
        if trial_idx % 2 == 0:
            treatment_ui.start_motor_imagery_trial()
        else:
            treatment_ui.start_rest_trial()
        
        # 模拟脑电数据流
        for packet_idx in range(125):  # 模拟1秒的数据（125个数据包）
            # 生成模拟数据包
            channel_data = []
            for group in range(4):  # 每包4组数据
                group_data = [np.random.randint(-100000, 100000) for _ in range(8)]
                channel_data.append(group_data)
            
            eeg_packet = EEGDataPacket(
                timestamp=datetime.now().timestamp(),
                channel_data=channel_data,
                packet_number=packet_idx,
                is_valid=True
            )
            
            treatment_ui.process_eeg_data(eeg_packet)
        
        # 结束试验
        treatment_ui.end_current_trial()
    
    # 获取会话训练数据
    session_data = treatment_ui.get_session_training_data()
    
    # 显示数据统计
    treatment_ui.show_data_statistics()
    
    # 结束治疗
    treatment_ui.end_treatment()
    
    print("\n🎉 模拟治疗会话完成")


def main():
    """主函数"""
    print("脑电原始数据存储集成示例")
    print("="*60)
    
    try:
        # 检查依赖
        import h5py
        print("✅ HDF5支持可用")
        
        # 运行模拟治疗会话
        simulate_treatment_session()
        
        print("\n📋 集成要点:")
        print("1. 在治疗开始时调用 start_treatment_session()")
        print("2. 在每个试验开始时调用 start_trial_recording()")
        print("3. 将脑电数据包传递给 process_eeg_data()")
        print("4. 在试验结束时调用 end_trial_recording()")
        print("5. 在治疗结束时调用 end_treatment_session()")
        print("6. 可以随时获取训练数据和统计信息")
        
        return True
        
    except ImportError:
        print("❌ HDF5支持不可用，请安装h5py: pip install h5py")
        return False
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
