#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备状态上传错误处理
Test Equipment Status Upload Error Handling

验证状态码-2被正确记录为异常，并在日志中记录错误编号

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader
from core.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_equipment_status_error_handling():
    """测试设备状态上传错误处理"""
    print("⚠️ 测试设备状态上传错误处理")
    print("=" * 50)
    
    # 1. 初始化组件
    print("\n1. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 2. 获取医院信息
    print("\n2. 获取医院信息...")
    hospital_info = db_manager.get_hospital_info()
    if not hospital_info:
        print("❌ 无法获取医院信息")
        return
    
    print(f"医院ID: {hospital_info.get('id', 'N/A')}")
    print(f"设备编号: {hospital_info.get('shebeiid', 'N/A')}")
    
    # 3. 测试设备开机状态上传（正确的错误处理）
    print("\n3. 测试设备开机状态上传（正确的错误处理）...")
    print("🔄 模拟用户登录成功...")
    
    start_time = time.time()
    try:
        print("🌐 开始上传设备开机状态...")
        
        # 执行开机状态上传
        upload_result = uploader.update_equipment_status(hospital_info, "1")
        
        end_time = time.time()
        upload_duration = end_time - start_time
        
        print(f"⏱️ 开机状态上传耗时: {upload_duration:.2f} 秒")
        
        if upload_result.success:
            print(f"✅ 开机状态上传成功: {upload_result.message}")
        else:
            print(f"⚠️ 开机状态上传失败: {upload_result.message}")
            if "错误编号: -2" in upload_result.message:
                print("📝 正确记录了错误编号-2，这是异常情况")
        
    except Exception as e:
        end_time = time.time()
        upload_duration = end_time - start_time
        print(f"❌ 开机状态上传测试失败: {e}")
        print(f"⏱️ 失败耗时: {upload_duration:.2f} 秒")
    
    # 4. 等待一段时间
    print("\n4. 等待3秒...")
    time.sleep(3)
    
    # 5. 测试设备关机状态上传（正确的错误处理）
    print("\n5. 测试设备关机状态上传（正确的错误处理）...")
    print("🔄 模拟用户登出或系统退出...")
    
    start_time = time.time()
    try:
        print("🌐 开始上传设备关机状态...")
        
        # 执行关机状态上传
        upload_result = uploader.update_equipment_status(hospital_info, "0")
        
        end_time = time.time()
        upload_duration = end_time - start_time
        
        print(f"⏱️ 关机状态上传耗时: {upload_duration:.2f} 秒")
        
        if upload_result.success:
            print(f"✅ 关机状态上传成功: {upload_result.message}")
        else:
            print(f"⚠️ 关机状态上传失败: {upload_result.message}")
            if "错误编号: -2" in upload_result.message:
                print("📝 正确记录了错误编号-2，这是异常情况")
        
    except Exception as e:
        end_time = time.time()
        upload_duration = end_time - start_time
        print(f"❌ 关机状态上传测试失败: {e}")
        print(f"⏱️ 失败耗时: {upload_duration:.2f} 秒")
    
    # 6. 测试患者数据上传（对比错误处理）
    print("\n6. 测试患者数据上传（对比错误处理）...")
    
    # 创建测试患者数据
    test_patient = {
        'bianhao': int(time.time()) % 1000000,
        'name': '错误处理测试患者',
        'age': 30,
        'xingbie': '男',
        'cardid': '110101199001011234',
        'zhenduan': '测试诊断',
        'bingshi': '测试既往史',
        'brhc': '左侧',
        'zhuzhi': '测试医生',
        'czy': '测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1),
        'status': 'pending'
    }
    
    try:
        print("🌐 开始上传患者数据...")
        
        # 执行患者数据上传
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        if upload_result.success:
            print(f"✅ 患者数据上传成功: {upload_result.message}")
        else:
            print(f"⚠️ 患者数据上传失败: {upload_result.message}")
            if "错误编号: -2" in upload_result.message:
                print("📝 正确记录了患者数据的错误编号-2")
        
    except Exception as e:
        print(f"❌ 患者数据上传测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 设备状态上传错误处理测试完成！")
    
    # 7. 总结正确的错误处理方式
    print("\n📋 正确的错误处理方式:")
    print("✅ 状态码-2被正确识别为异常情况")
    print("✅ 在日志中明确记录错误编号")
    print("✅ 区分设备状态和患者数据的错误信息")
    print("✅ 所有非0状态码都视为错误")
    
    print("\n🎯 错误信息格式:")
    print("- 设备状态-2: '设备状态上传异常，错误编号: -2'")
    print("- 患者数据-2: '患者数据重复或已存在，错误编号: -2'")
    print("- 其他错误: '服务器返回业务错误，错误编号: X'")
    
    print("\n⚠️ 错误处理原则:")
    print("1. 只有状态码0表示成功")
    print("2. 所有非0状态码都是异常情况")
    print("3. 在日志中明确记录错误编号")
    print("4. 根据上传类型提供准确的错误描述")
    
    print("\n📊 日志记录规范:")
    print("- 成功: INFO级别，记录操作成功")
    print("- 异常: WARNING级别，记录错误编号和描述")
    print("- 错误: ERROR级别，记录系统级错误")
    
    print("\n🔍 状态码-2的正确理解:")
    print("- 这是服务端返回的异常状态码")
    print("- 需要在日志中记录此错误编号")
    print("- 不应该将其视为正常情况")
    print("- 可能需要进一步调查服务端问题")
    
    print("\n💡 后续处理建议:")
    print("1. 监控状态码-2的出现频率")
    print("2. 分析服务端返回-2的具体原因")
    print("3. 根据业务需求决定是否需要重试机制")
    print("4. 与服务端开发团队确认-2状态码的确切含义")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_equipment_status_error_handling()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
