#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
B通道修复验证测试
验证修复后B通道能否正常工作
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('b_channel_fix_verification.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_b_channel_fix():
    """测试B通道修复效果"""
    print("=" * 60)
    print("🔧 B通道修复验证测试")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        # 步骤1：连接设备
        print("\n📡 步骤1: 连接设备")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")
        
        # 步骤2：模拟治疗界面的修复后逻辑
        print("\n🔧 步骤2: 模拟修复后的治疗界面逻辑")
        
        # 模拟界面参数设置
        base_params = {
            'frequency': 25.0,
            'pulse_width': 250.0,
            'relax_time': 5.0,
            'climb_time': 2.0,
            'work_time': 10.0,
            'fall_time': 2.0,
            'wave_type': 0
        }
        
        success = False
        
        # 模拟A通道启动（如果选中）
        channel_a_enabled = True
        channel_a_current = 2.0
        
        if channel_a_enabled and channel_a_current > 0:
            print(f"\n🔋 启动A通道，电流: {channel_a_current}mA")
            
            # 为A通道设置专用参数
            params_a = StimulationParameters(
                channel_num=1,  # 明确设置为A通道
                frequency=base_params['frequency'],
                pulse_width=base_params['pulse_width'],
                relax_time=base_params['relax_time'],
                climb_time=base_params['climb_time'],
                work_time=base_params['work_time'],
                fall_time=base_params['fall_time'],
                wave_type=base_params['wave_type']
            )
            
            if device.set_stimulation_parameters(params_a):
                print("   ✅ A通道参数设置成功")
                if device.set_current(1, channel_a_current):
                    print("   ✅ A通道电流设置成功")
                    if device.start_stimulation(1):
                        print("   ✅ A通道刺激启动成功")
                        success = True
                    else:
                        print("   ❌ A通道启动失败")
                else:
                    print("   ❌ A通道电流设置失败")
            else:
                print("   ❌ A通道参数设置失败")
        
        # 模拟B通道启动（如果选中）
        channel_b_enabled = True
        channel_b_current = 1.0
        
        if channel_b_enabled and channel_b_current > 0:
            print(f"\n🔋 启动B通道，电流: {channel_b_current}mA")
            
            # 为B通道设置专用参数（关键修复）
            params_b = StimulationParameters(
                channel_num=2,  # 明确设置为B通道
                frequency=base_params['frequency'],
                pulse_width=base_params['pulse_width'],
                relax_time=base_params['relax_time'],
                climb_time=base_params['climb_time'],
                work_time=base_params['work_time'],
                fall_time=base_params['fall_time'],
                wave_type=base_params['wave_type']
            )
            
            if device.set_stimulation_parameters(params_b):
                print("   ✅ B通道参数设置成功")
                if device.set_current(2, channel_b_current):
                    print("   ✅ B通道电流设置成功")
                    if device.start_stimulation(2):
                        print("   ✅ B通道刺激启动成功")
                        success = True
                    else:
                        print("   ❌ B通道启动失败")
                else:
                    print("   ❌ B通道电流设置失败")
            else:
                print("   ❌ B通道参数设置失败")
        
        if success:
            print("\n✅ 电刺激治疗开始成功")
            
            # 步骤3：监控双通道状态
            print("\n📊 步骤3: 监控双通道状态 (10秒)")
            for i in range(10):
                time.sleep(1)
                status_info = device.get_status_info()
                a_status = status_info['channel_a_status']
                b_status = status_info['channel_b_status']
                
                # 状态解释
                status_names = {0: "停止", 1: "暂停", 2: "电流调节", 3: "正常工作"}
                a_name = status_names.get(a_status, f"未知({a_status})")
                b_name = status_names.get(b_status, f"未知({b_status})")
                
                print(f"   [{i+1:2d}s] A通道: {a_status}({a_name}), B通道: {b_status}({b_name})")
                
                # 分析状态
                if a_status == 3 and b_status == 3:
                    print(f"      ✅ 双通道都在正常工作！修复成功！")
                elif a_status == 3 and b_status != 3:
                    print(f"      ⚠️ A通道正常，B通道异常({b_name})")
                elif a_status != 3 and b_status == 3:
                    print(f"      ⚠️ B通道正常，A通道异常({a_name})")
                else:
                    print(f"      ⚠️ 双通道都不在工作状态")
            
            # 步骤4：停止刺激
            print("\n🛑 步骤4: 停止所有刺激")
            if device.stop_all_stimulation():
                print("✅ 所有刺激已停止")
            else:
                print("❌ 停止刺激失败")
        else:
            print("\n❌ 电刺激启动失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        try:
            device.disconnect()
            print("\n🔌 设备连接已断开")
        except:
            pass

def test_parameter_isolation():
    """测试参数隔离性"""
    print("\n" + "=" * 60)
    print("🔍 参数隔离性测试")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
            
        print("✅ 设备连接成功")
        
        # 测试：为不同通道设置不同参数
        print("\n🔍 测试1: 为A通道设置25Hz参数")
        params_a = StimulationParameters(
            channel_num=1,
            frequency=25.0,
            pulse_width=250.0
        )
        
        if device.set_stimulation_parameters(params_a):
            print("   ✅ A通道参数设置成功 (25Hz)")
        else:
            print("   ❌ A通道参数设置失败")
            
        print("\n🔍 测试2: 为B通道设置20Hz参数")
        params_b = StimulationParameters(
            channel_num=2,
            frequency=20.0,
            pulse_width=200.0
        )
        
        if device.set_stimulation_parameters(params_b):
            print("   ✅ B通道参数设置成功 (20Hz)")
        else:
            print("   ❌ B通道参数设置失败")
            
        print("\n🔍 测试3: 验证参数独立性")
        print("   如果参数设置正确，每个通道应该使用自己的参数")
        print("   A通道: 25Hz, 250μs")
        print("   B通道: 20Hz, 200μs")
        
        # 启动测试
        device.set_current(1, 1.0)
        device.set_current(2, 1.0)
        
        if device.start_stimulation(1):
            print("   ✅ A通道启动成功")
        else:
            print("   ❌ A通道启动失败")
            
        if device.start_stimulation(2):
            print("   ✅ B通道启动成功")
        else:
            print("   ❌ B通道启动失败")
            
        # 观察状态
        time.sleep(3)
        status_info = device.get_status_info()
        print(f"\n   最终状态:")
        print(f"   A通道: {status_info['channel_a_status']}")
        print(f"   B通道: {status_info['channel_b_status']}")
        
        device.stop_all_stimulation()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        try:
            device.disconnect()
        except:
            pass

if __name__ == "__main__":
    setup_logging()
    
    print("🔧 开始B通道修复验证...")
    
    # 修复效果验证
    success1 = test_b_channel_fix()
    
    # 参数隔离性测试
    success2 = test_parameter_isolation()
    
    print("\n" + "=" * 60)
    print("📋 验证结果总结")
    print("=" * 60)
    print(f"修复效果验证: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"参数隔离测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 B通道修复验证成功！")
        print("   ✅ B通道现在可以正常工作")
        print("   ✅ 双通道参数设置独立")
        print("   ✅ 治疗界面逻辑修复完成")
    else:
        print("\n⚠️ 修复验证失败，需要进一步检查")
        print("   日志文件: b_channel_fix_verification.log")
