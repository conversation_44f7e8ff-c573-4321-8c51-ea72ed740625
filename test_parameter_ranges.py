#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电刺激参数范围调整
Test Stimulation Parameter Range Adjustments

作者: AI Assistant
版本: 1.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_parameter_ranges():
    """测试参数范围设置"""
    print("=" * 60)
    print("电刺激参数范围测试")
    print("=" * 60)
    
    try:
        from utils.app_config import AppConfig
        
        # 测试配置值
        config = AppConfig.STIMULATION_CONFIG
        
        print("当前配置值:")
        print(f"  最大电流: {config['max_current']} mA (要求: 1-100)")
        print(f"  最小电流: {config['min_current']} mA (要求: 1-10)")
        print(f"  电流步长: {config['current_step']} mA (要求: 固定为1)")
        print(f"  默认频率: {config['default_frequency']} Hz (要求: 2-160)")
        print(f"  默认脉宽: {config['default_pulse_width']} μs (要求: 10-500)")
        print(f"  默认休息时间: {config['default_relax_time']} s (要求: 0-16)")
        print(f"  默认上升时间: {config['default_climb_time']} s (要求: 0-5)")
        print(f"  默认工作时间: {config['default_work_time']} s (要求: 0-30)")
        print(f"  默认下降时间: {config['default_fall_time']} s (要求: 0-5)")
        print(f"  连接超时: {config['connection_timeout']} s (要求: 1-30)")
        
        # 验证数据类型
        print("\n数据类型验证:")
        type_checks = [
            ('max_current', config['max_current'], int),
            ('min_current', config['min_current'], int),
            ('current_step', config['current_step'], int),
            ('default_frequency', config['default_frequency'], int),
            ('default_pulse_width', config['default_pulse_width'], int),
            ('default_relax_time', config['default_relax_time'], int),
            ('default_climb_time', config['default_climb_time'], int),
            ('default_work_time', config['default_work_time'], int),
            ('default_fall_time', config['default_fall_time'], int),
            ('connection_timeout', config['connection_timeout'], int),
        ]
        
        all_types_correct = True
        for name, value, expected_type in type_checks:
            is_correct = isinstance(value, expected_type)
            all_types_correct &= is_correct
            status = "✅" if is_correct else "❌"
            print(f"  {status} {name}: {type(value).__name__} (期望: {expected_type.__name__})")
        
        # 验证取值范围
        print("\n取值范围验证:")
        range_checks = [
            ('max_current', config['max_current'], 1, 100),
            ('min_current', config['min_current'], 1, 10),
            ('current_step', config['current_step'], 1, 1),  # 固定为1
            ('default_frequency', config['default_frequency'], 2, 160),
            ('default_pulse_width', config['default_pulse_width'], 10, 500),
            ('default_relax_time', config['default_relax_time'], 0, 16),
            ('default_climb_time', config['default_climb_time'], 0, 5),
            ('default_work_time', config['default_work_time'], 0, 30),
            ('default_fall_time', config['default_fall_time'], 0, 5),
            ('connection_timeout', config['connection_timeout'], 1, 30),
        ]
        
        all_ranges_correct = True
        for name, value, min_val, max_val in range_checks:
            is_in_range = min_val <= value <= max_val
            all_ranges_correct &= is_in_range
            status = "✅" if is_in_range else "❌"
            print(f"  {status} {name}: {value} (范围: {min_val}-{max_val})")
        
        return all_types_correct and all_ranges_correct
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_component_simulation():
    """模拟UI组件测试"""
    print("\n" + "=" * 60)
    print("UI组件模拟测试")
    print("=" * 60)
    
    # 模拟QSpinBox的行为
    class MockSpinBox:
        def __init__(self, min_val, max_val, value):
            self.min_val = min_val
            self.max_val = max_val
            self._value = value
            self.setRange(min_val, max_val)
            self.setValue(value)
        
        def setRange(self, min_val, max_val):
            self.min_val = min_val
            self.max_val = max_val
            # 确保当前值在范围内
            if hasattr(self, '_value'):
                self._value = max(min_val, min(max_val, self._value))
        
        def setValue(self, value):
            self._value = max(self.min_val, min(self.max_val, value))
        
        def value(self):
            return self._value
        
        def setEnabled(self, enabled):
            self.enabled = enabled
    
    # 测试各个参数的UI组件
    components = {
        'max_current': MockSpinBox(1, 100, 50),
        'min_current': MockSpinBox(1, 10, 1),
        'current_step': MockSpinBox(1, 1, 1),  # 固定为1
        'frequency': MockSpinBox(2, 160, 20),
        'pulse_width': MockSpinBox(10, 500, 200),
        'relax_time': MockSpinBox(0, 16, 5),
        'climb_time': MockSpinBox(0, 5, 2),
        'work_time': MockSpinBox(0, 30, 10),
        'fall_time': MockSpinBox(0, 5, 2),
        'timeout': MockSpinBox(1, 30, 5),
    }
    
    print("UI组件范围测试:")
    all_ui_correct = True
    for name, component in components.items():
        # 测试边界值
        test_values = [component.min_val, component.max_val, 
                      component.min_val - 1, component.max_val + 1]
        
        for test_val in test_values:
            component.setValue(test_val)
            actual_val = component.value()
            expected_val = max(component.min_val, min(component.max_val, test_val))
            is_correct = actual_val == expected_val
            all_ui_correct &= is_correct
            
            if test_val == component.min_val or test_val == component.max_val:
                status = "✅" if is_correct else "❌"
                print(f"  {status} {name}: 设置{test_val} -> 得到{actual_val} (范围: {component.min_val}-{component.max_val})")
    
    # 测试电流步长的特殊处理
    current_step = components['current_step']
    current_step.setEnabled(False)  # 应该被禁用
    print(f"  ✅ 电流步长组件已禁用: {not getattr(current_step, 'enabled', True)}")
    
    return all_ui_correct

def test_boundary_values():
    """测试边界值"""
    print("\n" + "=" * 60)
    print("边界值测试")
    print("=" * 60)
    
    # 定义参数边界
    boundaries = {
        'frequency': (2, 160),      # 频率
        'pulse_width': (10, 500),   # 脉宽
        'relax_time': (0, 16),      # 休息时间
        'climb_time': (0, 5),       # 上升时间
        'work_time': (0, 30),       # 工作时间
        'fall_time': (0, 5),        # 下降时间
        'max_current': (1, 100),    # 最大电流
        'min_current': (1, 10),     # 最小电流
        'current_step': (1, 1),     # 电流步长（固定）
        'timeout': (1, 30),         # 连接超时
    }
    
    print("边界值验证:")
    for param, (min_val, max_val) in boundaries.items():
        print(f"  {param}: {min_val} - {max_val}")
        
        # 验证边界值是否合理
        if min_val > max_val:
            print(f"    ❌ 错误: 最小值({min_val}) > 最大值({max_val})")
        elif min_val < 0 and param not in ['relax_time', 'climb_time', 'work_time', 'fall_time']:
            print(f"    ⚠️  警告: 最小值为负数({min_val})")
        else:
            print(f"    ✅ 边界值合理")
    
    return True

def main():
    """主测试函数"""
    print("开始电刺激参数范围调整测试...")
    
    success = True
    success &= test_parameter_ranges()
    success &= test_ui_component_simulation()
    success &= test_boundary_values()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试通过！")
        print("参数范围调整完成，符合要求：")
        print("  - 频率: 2~160Hz")
        print("  - 脉宽: 10~500μs")
        print("  - 休息时间: 0~16S")
        print("  - 上升时间: 0~5S")
        print("  - 工作时间: 0~30S")
        print("  - 下降时间: 0~5S")
        print("  - 最大电流: 1-100mA")
        print("  - 最小电流: 1-10mA")
        print("  - 电流步长: 固定为1")
        print("  - 所有数值只接受整数")
    else:
        print("❌ 部分测试失败！")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
