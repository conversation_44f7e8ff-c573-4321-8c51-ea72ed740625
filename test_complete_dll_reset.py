#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整DLL重置测试脚本

测试基于厂家DLL规范的完整重置机制，验证：
1. 连接错误端口后的完整清理
2. 连接正确端口的恢复能力
3. 多次连接失败的处理
4. DLL状态的彻底重置
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('complete_dll_reset_test.log', encoding='utf-8')
        ]
    )

def test_dll_status_check():
    """测试DLL状态检查功能"""
    print("🔧 测试DLL状态检查功能")
    
    device = StimulationDevice()
    test_results = []
    
    # 加载DLL
    if not device.load_dll():
        print("   ❌ DLL加载失败")
        return [('DLL加载', False)]
    
    print("   ✅ DLL加载成功")
    test_results.append(('DLL加载', True))
    
    # 测试IsRecOpen功能
    try:
        is_open = device.dll.IsRecOpen()
        print(f"   📍 初始端口状态: {'打开' if is_open else '关闭'}")
        test_results.append(('端口状态检查', True))
    except Exception as e:
        print(f"   ❌ 端口状态检查失败: {e}")
        test_results.append(('端口状态检查', False))
    
    return test_results

def test_complete_dll_reset():
    """测试完整DLL重置机制"""
    print("\n🔧 测试完整DLL重置机制")
    
    device = StimulationDevice()
    test_results = []
    
    # 加载DLL
    if not device.load_dll():
        print("   ❌ DLL加载失败")
        return [('DLL加载', False)]
    
    print("   ✅ DLL加载成功")
    test_results.append(('DLL加载', True))
    
    # 测试完整DLL重置
    print("\n📍 执行完整DLL重置")
    try:
        reset_result = device._complete_dll_reset()
        print(f"   完整DLL重置: {'成功' if reset_result else '失败'}")
        test_results.append(('完整DLL重置', reset_result))
    except Exception as e:
        print(f"   ❌ 完整DLL重置异常: {e}")
        test_results.append(('完整DLL重置', False))
    
    return test_results

def test_connection_with_complete_reset():
    """测试带完整重置的连接恢复"""
    print("\n🔧 测试带完整重置的连接恢复")
    
    device = StimulationDevice()
    test_results = []
    
    # 测试1: 连接错误端口
    print("\n📍 测试1: 连接错误端口 (应该失败)")
    wrong_port = 99
    start_time = time.time()
    result1 = device.connect(wrong_port)
    end_time = time.time()
    
    print(f"   连接错误端口 {wrong_port}: {'成功' if result1 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('错误端口连接', not result1))  # 应该失败
    
    # 等待一下
    time.sleep(2)
    
    # 测试2: 连接正确端口
    print("\n📍 测试2: 连接正确端口")
    correct_port = 7  # 假设正确端口
    start_time = time.time()
    result2 = device.connect(correct_port)
    end_time = time.time()
    
    print(f"   连接正确端口 {correct_port}: {'成功' if result2 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('正确端口连接', result2))
    
    # 测试3: 断开连接
    if result2:
        print("\n📍 测试3: 断开连接")
        disconnect_result = device.disconnect()
        print(f"   断开连接: {'成功' if disconnect_result else '失败'}")
        test_results.append(('断开连接', disconnect_result))
    
    # 测试4: 再次连接错误端口
    print("\n📍 测试4: 再次连接错误端口")
    time.sleep(1)
    start_time = time.time()
    result4 = device.connect(wrong_port)
    end_time = time.time()
    
    print(f"   再次连接错误端口 {wrong_port}: {'成功' if result4 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('再次错误端口连接', not result4))  # 应该失败
    
    # 测试5: 再次连接正确端口
    print("\n📍 测试5: 再次连接正确端口")
    time.sleep(2)
    start_time = time.time()
    result5 = device.connect(correct_port)
    end_time = time.time()
    
    print(f"   再次连接正确端口 {correct_port}: {'成功' if result5 else '失败'}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    test_results.append(('再次正确端口连接', result5))
    
    # 最终清理
    if result5:
        device.disconnect()
    
    return test_results

def test_rapid_connection_attempts():
    """测试快速连接尝试"""
    print("\n🔧 测试快速连接尝试")
    
    device = StimulationDevice()
    test_results = []
    
    wrong_port = 99
    correct_port = 7
    attempts = 5
    
    for i in range(attempts):
        print(f"\n📍 第 {i+1} 次快速连接测试")
        
        # 连接错误端口
        start_time = time.time()
        result_wrong = device.connect(wrong_port)
        end_time = time.time()
        
        print(f"   错误端口连接: {'成功' if result_wrong else '失败'} (耗时: {end_time - start_time:.2f}秒)")
        test_results.append((f'第{i+1}次错误端口', not result_wrong))
        
        # 短暂等待
        time.sleep(0.5)
        
        # 连接正确端口
        start_time = time.time()
        result_correct = device.connect(correct_port)
        end_time = time.time()
        
        print(f"   正确端口连接: {'成功' if result_correct else '失败'} (耗时: {end_time - start_time:.2f}秒)")
        test_results.append((f'第{i+1}次正确端口', result_correct))
        
        # 断开连接
        if result_correct:
            device.disconnect()
        
        # 短暂等待
        time.sleep(0.5)
    
    return test_results

def print_test_summary(all_results):
    """打印测试总结"""
    print("\n" + "="*60)
    print("📊 完整DLL重置测试总结")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for test_name, results in all_results.items():
        print(f"\n🔍 {test_name}:")
        for test_case, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_case}: {status}")
            total_tests += 1
            if result:
                passed_tests += 1
    
    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 完整DLL重置机制工作优秀！")
    elif success_rate >= 80:
        print("✅ 完整DLL重置机制工作良好！")
    elif success_rate >= 60:
        print("⚠️  完整DLL重置机制有一定效果，但仍需改进")
    else:
        print("❌ 完整DLL重置机制效果不佳，需要进一步调查")

def main():
    """主函数"""
    print("🚀 完整DLL重置机制测试")
    print("="*60)
    
    setup_logging()
    
    all_results = {}
    
    try:
        # 测试1: DLL状态检查
        results1 = test_dll_status_check()
        all_results["DLL状态检查"] = results1
        
        # 测试2: 完整DLL重置
        results2 = test_complete_dll_reset()
        all_results["完整DLL重置"] = results2
        
        # 测试3: 带完整重置的连接恢复
        results3 = test_connection_with_complete_reset()
        all_results["连接恢复能力"] = results3
        
        # 测试4: 快速连接尝试
        results4 = test_rapid_connection_attempts()
        all_results["快速连接尝试"] = results4
        
        # 打印总结
        print_test_summary(all_results)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 完整DLL重置测试完成")

if __name__ == "__main__":
    main()
