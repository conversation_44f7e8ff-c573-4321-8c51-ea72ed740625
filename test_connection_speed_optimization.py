#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备连接速度优化测试
测试连接和断开的响应时间
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('connection_speed_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_connection_speed():
    """测试连接和断开速度"""
    print("🚀 电刺激设备连接速度优化测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.stimulation_device import StimulationDevice
        from utils.app_config import AppConfig
        
        # 创建设备实例
        device = StimulationDevice()
        
        # 测试结果记录
        test_results = []
        
        print("\n📍 测试1: 连接速度测试（正确端口）")
        correct_port = 7
        
        # 进行5次连接测试
        connection_times = []
        for i in range(5):
            print(f"\n   第{i+1}次连接测试:")
            
            start_time = time.time()
            result = device.connect(correct_port)
            connection_time = time.time() - start_time
            
            if result:
                connection_times.append(connection_time)
                print(f"     ✅ 连接成功，耗时: {connection_time:.3f}秒")
                
                # 立即断开
                start_time = time.time()
                device.disconnect()
                disconnect_time = time.time() - start_time
                print(f"     ✅ 断开成功，耗时: {disconnect_time:.3f}秒")
                
                # 短暂等待
                time.sleep(0.5)
            else:
                print(f"     ❌ 连接失败，耗时: {connection_time:.3f}秒")
        
        if connection_times:
            avg_connection_time = sum(connection_times) / len(connection_times)
            min_connection_time = min(connection_times)
            max_connection_time = max(connection_times)
            
            print(f"\n📊 连接速度统计:")
            print(f"   平均连接时间: {avg_connection_time:.3f}秒")
            print(f"   最快连接时间: {min_connection_time:.3f}秒")
            print(f"   最慢连接时间: {max_connection_time:.3f}秒")
            
            # 评估连接速度
            if avg_connection_time <= 1.0:
                print("   🎉 连接速度优秀（≤1秒）")
                test_results.append(('连接速度', True))
            elif avg_connection_time <= 1.5:
                print("   ✅ 连接速度良好（≤1.5秒）")
                test_results.append(('连接速度', True))
            else:
                print("   ⚠️ 连接速度需要进一步优化（>1.5秒）")
                test_results.append(('连接速度', False))
        else:
            print("   ❌ 所有连接测试失败")
            test_results.append(('连接速度', False))
        
        print("\n📍 测试2: 错误端口处理速度测试")
        wrong_port = 26
        
        # 测试错误端口处理速度
        error_times = []
        for i in range(3):
            print(f"\n   第{i+1}次错误端口测试:")
            
            start_time = time.time()
            result = device.connect(wrong_port)
            error_time = time.time() - start_time
            
            if not result:
                error_times.append(error_time)
                print(f"     ✅ 错误端口正确失败，耗时: {error_time:.3f}秒")
                
                # 立即尝试连接正确端口
                start_time = time.time()
                recovery_result = device.connect(correct_port)
                recovery_time = time.time() - start_time
                
                if recovery_result:
                    print(f"     ✅ 恢复连接成功，耗时: {recovery_time:.3f}秒")
                    device.disconnect()
                else:
                    print(f"     ❌ 恢复连接失败，耗时: {recovery_time:.3f}秒")
                
                time.sleep(0.5)
            else:
                print(f"     ❌ 错误端口异常成功，耗时: {error_time:.3f}秒")
        
        if error_times:
            avg_error_time = sum(error_times) / len(error_times)
            print(f"\n📊 错误处理速度统计:")
            print(f"   平均错误处理时间: {avg_error_time:.3f}秒")
            
            # 评估错误处理速度
            if avg_error_time <= 3.0:
                print("   ✅ 错误处理速度良好（≤3秒）")
                test_results.append(('错误处理速度', True))
            else:
                print("   ⚠️ 错误处理速度需要优化（>3秒）")
                test_results.append(('错误处理速度', False))
        
        print("\n📍 测试3: 快速连接/断开循环测试")
        
        # 快速连接断开测试
        rapid_test_times = []
        for i in range(3):
            print(f"\n   第{i+1}次快速循环测试:")
            
            start_time = time.time()
            
            # 连接
            connect_result = device.connect(correct_port)
            if connect_result:
                # 立即断开
                device.disconnect()
                cycle_time = time.time() - start_time
                rapid_test_times.append(cycle_time)
                print(f"     ✅ 连接-断开循环完成，耗时: {cycle_time:.3f}秒")
            else:
                cycle_time = time.time() - start_time
                print(f"     ❌ 连接失败，耗时: {cycle_time:.3f}秒")
            
            time.sleep(0.3)
        
        if rapid_test_times:
            avg_cycle_time = sum(rapid_test_times) / len(rapid_test_times)
            print(f"\n📊 快速循环测试统计:")
            print(f"   平均循环时间: {avg_cycle_time:.3f}秒")
            
            # 评估快速循环性能
            if avg_cycle_time <= 2.0:
                print("   🎉 快速循环性能优秀（≤2秒）")
                test_results.append(('快速循环性能', True))
            elif avg_cycle_time <= 3.0:
                print("   ✅ 快速循环性能良好（≤3秒）")
                test_results.append(('快速循环性能', True))
            else:
                print("   ⚠️ 快速循环性能需要优化（>3秒）")
                test_results.append(('快速循环性能', False))
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("📊 速度优化测试结果总结:")
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, passed in test_results:
            status = "✅ 通过" if passed else "❌ 需要优化"
            print(f"   {test_name}: {status}")
            if passed:
                passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 速度优化效果显著！")
            print("✅ 用户体验得到明显改善")
            return True
        else:
            print("⚠️ 速度优化效果有限，需要进一步改进")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_connection_speed()
    sys.exit(0 if success else 1)
