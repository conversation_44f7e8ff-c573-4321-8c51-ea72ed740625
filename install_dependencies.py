#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
Dependencies Installation Script

作者: AI Assistant
版本: 1.0.0
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python版本过低！需要Python 3.9或更高版本。")
        return False
    
    print("✅ Python版本符合要求")
    return True


def check_pip():
    """检查pip是否可用"""
    print("检查pip...")
    try:
        import pip
        print("✅ pip可用")
        return True
    except ImportError:
        print("❌ pip不可用，请先安装pip")
        return False


def install_package(package_name, upgrade=False):
    """安装单个包"""
    try:
        cmd = [sys.executable, "-m", "pip", "install"]
        if upgrade:
            cmd.append("--upgrade")
        cmd.append(package_name)
        
        print(f"正在安装 {package_name}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装 {package_name} 时发生异常: {e}")
        return False


def install_core_dependencies():
    """安装核心依赖"""
    print("\n" + "="*50)
    print("开始安装核心依赖包...")
    print("="*50)
    
    # 核心依赖包列表（按安装顺序）
    core_packages = [
        "pip",  # 先升级pip
        "setuptools",
        "wheel",
        "PySide6>=6.4.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "pyserial>=3.5",
        "tqdm>=4.62.0",
    ]
    
    failed_packages = []
    
    for i, package in enumerate(core_packages, 1):
        print(f"\n[{i}/{len(core_packages)}] 安装 {package}")
        
        # pip需要升级安装
        upgrade = package == "pip"
        
        if not install_package(package, upgrade=upgrade):
            failed_packages.append(package)
    
    return failed_packages


def install_optional_dependencies():
    """安装可选依赖"""
    print("\n" + "="*50)
    print("开始安装可选依赖包...")
    print("="*50)
    
    optional_packages = [
        "scipy>=1.7.0",
        "pandas>=1.3.0", 
        "scikit-learn>=1.0.0",
        "joblib>=1.1.0",
        "pyttsx3>=2.90",
        "pytest>=6.2.5",
    ]
    
    failed_packages = []
    
    for i, package in enumerate(optional_packages, 1):
        print(f"\n[{i}/{len(optional_packages)}] 安装 {package}")
        
        if not install_package(package):
            failed_packages.append(package)
            print(f"⚠️  {package} 安装失败，但不影响核心功能")
    
    return failed_packages


def test_imports():
    """测试导入"""
    print("\n" + "="*50)
    print("测试包导入...")
    print("="*50)
    
    test_packages = [
        ("PySide6", "PySide6界面框架"),
        ("numpy", "数值计算"),
        ("matplotlib", "图形绘制"),
        ("serial", "串口通信"),
        ("tqdm", "进度条"),
    ]
    
    failed_imports = []
    
    for package, description in test_packages:
        try:
            __import__(package)
            print(f"✅ {package} ({description}) - 导入成功")
        except ImportError as e:
            print(f"❌ {package} ({description}) - 导入失败: {e}")
            failed_imports.append(package)
    
    return failed_imports


def create_test_gui():
    """创建测试GUI"""
    print("\n测试GUI创建...")
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PySide6.QtCore import Qt
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("NK系统 - 依赖测试")
        window.resize(300, 200)
        
        layout = QVBoxLayout()
        label = QLabel("✅ GUI测试成功！\n所有依赖包安装正常。")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        window.setLayout(layout)
        
        print("✅ GUI测试成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False


def main():
    """主函数"""
    print("NK脑机接口系统 - 依赖安装脚本")
    print("="*50)
    
    # 1. 检查Python版本
    if not check_python_version():
        return 1
    
    # 2. 检查pip
    if not check_pip():
        return 1
    
    # 3. 安装核心依赖
    core_failed = install_core_dependencies()
    
    # 4. 安装可选依赖
    optional_failed = install_optional_dependencies()
    
    # 5. 测试导入
    import_failed = test_imports()
    
    # 6. 测试GUI
    gui_success = create_test_gui()
    
    # 7. 输出结果
    print("\n" + "="*50)
    print("安装结果总结")
    print("="*50)
    
    if core_failed:
        print(f"❌ 核心依赖安装失败: {', '.join(core_failed)}")
        print("请手动安装这些包或检查网络连接")
        return 1
    else:
        print("✅ 核心依赖安装成功")
    
    if optional_failed:
        print(f"⚠️  可选依赖安装失败: {', '.join(optional_failed)}")
        print("这些包不影响核心功能，可以稍后手动安装")
    else:
        print("✅ 可选依赖安装成功")
    
    if import_failed:
        print(f"❌ 包导入失败: {', '.join(import_failed)}")
        print("请检查安装是否成功")
        return 1
    else:
        print("✅ 所有包导入成功")
    
    if gui_success:
        print("✅ GUI测试成功")
    else:
        print("❌ GUI测试失败")
        return 1
    
    print("\n🎉 依赖安装完成！")
    print("现在可以运行以下命令启动系统:")
    print("  python main.py")
    print("\n或者先运行系统测试:")
    print("  python test_system.py")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n安装过程中发生异常: {e}")
        input("按回车键退出...")
        sys.exit(1)
