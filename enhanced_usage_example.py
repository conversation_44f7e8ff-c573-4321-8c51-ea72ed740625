#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强脑电信号处理系统使用示例
Enhanced EEG Signal Processing System Usage Example

作者: AI Assistant
版本: 1.0.0
"""

import numpy as np
import logging
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_enhanced_training():
    """示例：使用增强特征进行训练"""
    logger.info("🎓 增强训练示例")
    
    try:
        from core.ml_model import MotorImageryModel
        
        # 创建模型
        model = MotorImageryModel("enhanced_example_model")
        
        # 模拟训练数据 (8通道, 250采样点, 20试验)
        for i in range(20):
            # 生成模拟脑电数据
            eeg_data = np.random.randn(8, 250) * 1000
            
            # 添加一些运动想象相关的模式
            if i % 2 == 1:  # 运动想象试验
                # 在C3, C4通道添加mu节律抑制
                eeg_data[2, :] += -200 * np.sin(2 * np.pi * 10 * np.linspace(0, 2, 250))
                eeg_data[3, :] += -200 * np.sin(2 * np.pi * 10 * np.linspace(0, 2, 250))
            
            # 添加训练数据
            label = 1 if i % 2 == 1 else 0
            model.add_training_data(eeg_data, label)
        
        # 使用增强特征训练
        logger.info("开始增强特征训练...")
        success = model.train_model(algorithm="lda", use_enhanced_features=True)
        
        if success:
            logger.info(f"✅ 训练成功！")
            logger.info(f"   训练轮次: {model.model_info.training_rounds}")
            logger.info(f"   总样本数: {model.model_info.total_samples}")
            if model.model_info.performance:
                logger.info(f"   准确率: {model.model_info.performance.accuracy:.3f}")
            
            # 测试预测
            test_data = np.random.randn(8, 250) * 1000
            prediction, confidence = model.predict(test_data)
            logger.info(f"   预测结果: {prediction}, 置信度: {confidence:.3f}")
            
        return success
        
    except Exception as e:
        logger.error(f"❌ 增强训练示例失败: {e}")
        return False

def example_enhanced_classifier():
    """示例：使用增强分类器"""
    logger.info("🤖 增强分类器示例")
    
    try:
        from algorithms.motor_imagery_classifier import (
            MotorImageryClassifier, MotorImageryTask, EEGData
        )
        
        # 创建分类器
        classifier = MotorImageryClassifier(sampling_rate=125, classifier_type='lda')
        
        # 准备训练数据
        training_data = []
        training_labels = []
        
        for i in range(20):
            # 生成模拟数据
            data = np.random.randn(8, 125) * 1000  # 1秒数据
            
            # 添加运动想象模式
            if i % 2 == 1:
                data[2, :] += -300 * np.sin(2 * np.pi * 10 * np.linspace(0, 1, 125))
                data[3, :] += -300 * np.sin(2 * np.pi * 10 * np.linspace(0, 1, 125))
            
            eeg_data = EEGData(
                data=data,
                sampling_rate=125,
                channel_names=[f'Ch{j+1}' for j in range(8)],
                timestamp=time.time()
            )
            
            training_data.append(eeg_data)
            training_labels.append(
                MotorImageryTask.LEFT_HAND if i % 2 == 1 else MotorImageryTask.REST
            )
        
        # 训练分类器（使用所有增强功能）
        logger.info("开始增强分类器训练...")
        success = classifier.train(
            training_data, 
            training_labels,
            use_adaptive_bands=True,
            feature_types=['psd', 'statistical', 'wavelet']  # 不使用CSP进行预测
        )
        
        if success:
            logger.info(f"✅ 分类器训练成功！")
            logger.info(f"   训练准确率: {classifier.training_accuracy:.3f}")
            logger.info(f"   交叉验证准确率: {classifier.cross_validation_score:.3f}")
            
            # 测试预测
            test_data = EEGData(
                data=np.random.randn(8, 125) * 1000,
                sampling_rate=125,
                channel_names=[f'Ch{j+1}' for j in range(8)],
                timestamp=time.time()
            )
            
            result = classifier.predict(test_data)
            logger.info(f"   预测结果: {result.predicted_class.value}")
            logger.info(f"   置信度: {result.confidence:.3f}")
            logger.info(f"   处理时间: {result.processing_time:.3f}s")
            
        return success
        
    except Exception as e:
        logger.error(f"❌ 增强分类器示例失败: {e}")
        return False

def example_enhanced_signal_processing():
    """示例：使用增强信号处理"""
    logger.info("⚡ 增强信号处理示例")
    
    try:
        from core.signal_processor import EEGSignalProcessor
        
        # 创建信号处理器
        processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
        
        # 生成带伪迹的测试数据
        raw_data = np.random.randn(8, 250) * 1000
        
        # 添加各种伪迹
        raw_data[0, 50:60] = 40000  # 饱和伪迹
        raw_data[1, :] = np.random.normal(0, 8000, 250)  # 高噪声
        raw_data[2, 100:110] = raw_data[2, 99]  # 平坦段
        
        logger.info("原始数据统计:")
        logger.info(f"   数据形状: {raw_data.shape}")
        logger.info(f"   数据范围: [{np.min(raw_data):.1f}, {np.max(raw_data):.1f}]")
        
        # 使用增强预处理
        processed_data, quality = processor.preprocess_signal(raw_data)
        
        logger.info("增强预处理结果:")
        logger.info(f"   信号质量评分: {quality.overall_quality:.3f}")
        logger.info(f"   噪声水平: {quality.noise_level:.3f}")
        logger.info(f"   伪迹比例: {quality.artifact_ratio:.3f}")
        logger.info(f"   信号可用性: {'是' if quality.is_usable else '否'}")
        logger.info(f"   处理后范围: [{np.min(processed_data):.1f}, {np.max(processed_data):.1f}]")
        
        # 提取特征
        features = processor.extract_features(processed_data)
        logger.info("特征提取结果:")
        for feature_name, feature_data in features.items():
            logger.info(f"   {feature_name}: {feature_data.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强信号处理示例失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 增强脑电信号处理系统使用示例")
    logger.info("=" * 60)
    
    examples = [
        ("增强信号处理", example_enhanced_signal_processing),
        ("增强分类器", example_enhanced_classifier),
        ("增强训练", example_enhanced_training),
    ]
    
    results = []
    
    for name, example_func in examples:
        logger.info(f"\n{'='*20} {name} {'='*20}")
        try:
            result = example_func()
            results.append(result)
            if result:
                logger.info(f"✅ {name}示例成功")
            else:
                logger.warning(f"⚠️ {name}示例失败")
        except Exception as e:
            logger.error(f"❌ {name}示例异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 示例结果: {success_count}/{total_count} 个示例成功")
    
    if success_count == total_count:
        logger.info("🎉 所有示例都成功运行！")
        logger.info("\n💡 使用建议:")
        logger.info("1. 在实际应用中，建议使用增强特征训练以获得更好性能")
        logger.info("2. 自适应频带选择可以为不同用户提供个性化优化")
        logger.info("3. 改进的伪迹检测可以提高信号质量和系统稳定性")
        logger.info("4. 小波变换特征可以捕捉传统方法遗漏的时频信息")
    else:
        logger.warning("⚠️ 部分示例失败，请检查系统配置")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
