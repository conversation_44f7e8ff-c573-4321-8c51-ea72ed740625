#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带通滤波修复
验证修复后的信号处理器能否正确处理短数据
"""

import numpy as np
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.signal_processor import EEGSignalProcessor

def test_short_data_filtering():
    """测试短数据滤波处理"""
    print("🧪 测试短数据滤波处理")
    print("=" * 60)
    
    # 创建信号处理器
    processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
    
    # 测试不同长度的数据
    test_cases = [
        ("极短数据", 16),   # 16个样本，约128ms
        ("短数据", 32),     # 32个样本，约256ms  
        ("中等数据", 64),   # 64个样本，约512ms
        ("长数据", 125),    # 125个样本，约1秒
    ]
    
    for case_name, n_samples in test_cases:
        print(f"\n📊 测试 {case_name} ({n_samples} 样本)")
        
        # 生成测试数据
        test_data = np.random.randn(8, n_samples) * 1000  # 8通道，随机数据
        
        try:
            # 尝试预处理
            processed_data, quality = processor.preprocess_signal(test_data)
            
            print(f"  ✅ 处理成功")
            print(f"     输入形状: {test_data.shape}")
            print(f"     输出形状: {processed_data.shape}")
            print(f"     信号质量: {quality.overall_quality:.3f}")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            return False
    
    return True

def test_real_time_scenario():
    """测试实时场景下的数据处理"""
    print("\n🔄 测试实时场景数据处理")
    print("=" * 60)
    
    processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
    
    # 模拟实时数据流
    print("模拟实时数据流处理...")
    
    for i in range(10):
        # 模拟每次收到32个样本（约250ms）
        real_time_data = np.random.randn(8, 32) * 500
        
        try:
            processed_data, quality = processor.preprocess_signal(real_time_data)
            print(f"  数据包 {i+1:2d}: 质量={quality.overall_quality:.3f}, 形状={processed_data.shape}")
            
        except Exception as e:
            print(f"  ❌ 数据包 {i+1} 处理失败: {e}")
            return False
    
    print("  ✅ 实时数据流处理成功")
    return True

def test_filter_bypass():
    """测试滤波器绕过逻辑"""
    print("\n🚫 测试滤波器绕过逻辑")
    print("=" * 60)
    
    processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
    
    # 测试极短数据（应该绕过滤波）
    very_short_data = np.random.randn(8, 10) * 1000  # 只有10个样本
    
    try:
        processed_data, quality = processor.preprocess_signal(very_short_data)
        
        # 检查数据是否基本保持不变（除了基线校正等简单处理）
        print(f"  输入数据范围: [{very_short_data.min():.1f}, {very_short_data.max():.1f}]")
        print(f"  输出数据范围: [{processed_data.min():.1f}, {processed_data.max():.1f}]")
        print(f"  数据形状保持: {very_short_data.shape == processed_data.shape}")
        print(f"  ✅ 极短数据处理成功（绕过滤波）")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 极短数据处理失败: {e}")
        return False

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n🛡️ 测试错误恢复机制")
    print("=" * 60)
    
    processor = EEGSignalProcessor(sample_rate=125.0, channels=8)
    
    # 测试各种可能导致错误的数据
    error_test_cases = [
        ("全零数据", np.zeros((8, 32))),
        ("极大值数据", np.ones((8, 32)) * 1e6),
        ("NaN数据", np.full((8, 32), np.nan)),
        ("Inf数据", np.full((8, 32), np.inf)),
    ]
    
    for case_name, test_data in error_test_cases:
        print(f"\n  测试 {case_name}")
        
        try:
            processed_data, quality = processor.preprocess_signal(test_data)
            print(f"    ✅ 处理成功，质量: {quality.overall_quality:.3f}")
            
        except Exception as e:
            print(f"    ⚠️ 处理异常但已恢复: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🔧 带通滤波修复验证测试")
    print("=" * 80)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    # 运行所有测试
    tests = [
        ("短数据滤波处理", test_short_data_filtering),
        ("实时场景处理", test_real_time_scenario),
        ("滤波器绕过逻辑", test_filter_bypass),
        ("错误恢复机制", test_error_recovery),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*80}")
    print("📋 测试结果总结")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！带通滤波修复成功。")
        print("\n📝 修复说明:")
        print("  1. 增加了数据长度检查，避免filtfilt的padlen错误")
        print("  2. 对于短数据自动绕过滤波处理")
        print("  3. 增加了单通道错误恢复机制")
        print("  4. 实时显示使用更长的数据缓冲区进行滤波")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
