#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电刺激设备端口下拉框功能
Test Stimulation Device Port Dropdown Functionality

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from ui.settings_ui import SettingsWidget
from utils.app_config import AppConfig

def test_port_dropdown():
    """测试端口下拉框功能"""
    print("=" * 60)
    print("电刺激设备端口下拉框功能测试")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("端口下拉框测试")
        main_window.setGeometry(100, 100, 800, 600)
        
        # 创建设置界面
        settings_widget = SettingsWidget()
        main_window.setCentralWidget(settings_widget)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 设置界面创建成功")
        print("✅ 端口下拉框已初始化")
        
        # 检查端口下拉框
        port_combo = settings_widget.port_num_combo
        print(f"✅ 端口下拉框项目数量: {port_combo.count()}")
        
        # 显示所有端口选项
        print("\n可用端口选项:")
        for i in range(port_combo.count()):
            text = port_combo.itemText(i)
            data = port_combo.itemData(i)
            print(f"  {i+1}. {text} (数据: {data})")
        
        # 测试当前选择
        current_text = port_combo.currentText()
        current_data = port_combo.currentData()
        print(f"\n当前选择: {current_text} (数据: {current_data})")
        
        # 测试端口号提取逻辑
        def extract_port_num(port_text):
            """提取端口号的测试函数"""
            if port_text.startswith('COM'):
                try:
                    return int(port_text[3:])
                except ValueError:
                    return 1
            return 1
        
        test_port = current_data or current_text
        extracted_port = extract_port_num(test_port)
        print(f"提取的端口号: {extracted_port}")
        
        # 测试设置不同端口
        test_ports = ["COM1", "COM7", "COM8", "COM10"]
        print(f"\n测试设置端口:")
        for test_port in test_ports:
            index = port_combo.findText(test_port)
            if index >= 0:
                port_combo.setCurrentIndex(index)
                current = port_combo.currentText()
                data = port_combo.currentData()
                extracted = extract_port_num(data or current)
                print(f"  设置 {test_port}: 索引={index}, 当前={current}, 提取端口号={extracted}")
            else:
                print(f"  ❌ 未找到端口 {test_port}")
        
        print("\n" + "=" * 60)
        print("测试完成！请在界面中验证端口下拉框功能。")
        print("可以尝试:")
        print("1. 选择不同的端口")
        print("2. 点击'测试连接'按钮")
        print("3. 点击'保存设置'按钮")
        print("=" * 60)
        
        # 运行应用
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_port_dropdown())
