#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三个问题的修复
Test script for three fixes
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_three_fixes():
    """测试三个问题的修复"""
    print("=" * 70)
    print("三个问题修复测试")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：检查连接超时后的资源释放修复
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查连接超时后的资源释放修复...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查超时处理中的资源释放
        timeout_fixes = [
            'self.stimulation_device.disconnect()',
            '超时后强制断开设备连接，释放资源',
            'self.stimulation_device = None',
            'self.stimulation_connected = False',
            '已释放设备资源，请检查设备状态后重试'
        ]
        
        missing_fixes = []
        for fix in timeout_fixes:
            if fix not in content:
                missing_fixes.append(fix)
        
        if missing_fixes:
            print(f"❌ 缺少超时资源释放修复: {missing_fixes}")
        else:
            print("✅ 连接超时后的资源释放修复完成")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查超时资源释放修复失败: {e}")
    
    # 测试2：检查端口号显示是否已完全移除
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查端口号显示是否已完全移除...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了端口显示相关元素
        removed_elements = [
            'self.stimulation_port_label',
            'settings_hint = QLabel("(在系统设置中配置)")',
            'port_info_layout = QHBoxLayout()',
            'def _update_port_display(self):',
            'self._update_port_display()'
        ]
        
        found_elements = []
        for element in removed_elements:
            if element in content:
                found_elements.append(element)
        
        if found_elements:
            print(f"❌ 仍存在应移除的端口显示元素: {found_elements}")
        else:
            print("✅ 端口号显示已完全移除")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查端口显示移除失败: {e}")
    
    # 测试3：检查刺激参数组是否已移除
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查刺激参数组是否已移除...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了刺激参数组
        removed_param_elements = [
            'param_info_group = QGroupBox("刺激参数")',
            '刺激参数已移至【系统设置 → 设备配置】中进行配置',
            'param_info_label = QLabel(',
            'param_info_layout.addWidget(param_info_label)',
            'layout.addWidget(param_info_group)'
        ]
        
        found_param_elements = []
        for element in removed_param_elements:
            if element in content:
                found_param_elements.append(element)
        
        if found_param_elements:
            print(f"❌ 仍存在应移除的刺激参数元素: {found_param_elements}")
        else:
            print("✅ 刺激参数组已完全移除")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查刺激参数组移除失败: {e}")
    
    # 测试4：检查电刺激设备组的简化
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查电刺激设备组的简化...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查简化后的电刺激设备组
        simplified_elements = [
            'stimulation_device_group = QGroupBox("电刺激设备")',
            'self.stimulation_connect_button = QPushButton("连接电刺激设备")',
            'self.stimulation_status_label = QLabel("状态: 未连接")'
        ]
        
        missing_simplified = []
        for element in simplified_elements:
            if element not in content:
                missing_simplified.append(element)
        
        if missing_simplified:
            print(f"❌ 缺少简化后的电刺激设备组元素: {missing_simplified}")
        else:
            print("✅ 电刺激设备组已正确简化")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查电刺激设备组简化失败: {e}")
    
    # 测试5：检查_get_selected_port方法是否正确从系统配置获取
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查_get_selected_port方法...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_get_selected_port方法的实现
        port_method_elements = [
            'def _get_selected_port(self) -> int:',
            '"""从系统配置获取端口号"""',
            'from utils.app_config import AppConfig',
            'port_num = AppConfig.STIMULATION_CONFIG.get(\'port_num\', 1)',
            'return port_num'
        ]
        
        missing_port_method = []
        for element in port_method_elements:
            if element not in content:
                missing_port_method.append(element)
        
        if missing_port_method:
            print(f"❌ _get_selected_port方法实现不完整: {missing_port_method}")
        else:
            print("✅ _get_selected_port方法正确从系统配置获取端口")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查_get_selected_port方法失败: {e}")
    
    # 测试6：检查连接逻辑是否保持完整
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查连接逻辑是否保持完整...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查连接逻辑的关键部分
        connection_logic = [
            'def connect_stimulation_device(self):',
            'port_num = self._get_selected_port()',
            'self.add_stimulation_log(f"尝试连接端口: COM{port_num}")',
            'def toggle_stimulation_connection(self):'
        ]
        
        missing_connection = []
        for logic in connection_logic:
            if logic not in content:
                missing_connection.append(logic)
        
        if missing_connection:
            print(f"❌ 连接逻辑不完整: {missing_connection}")
        else:
            print("✅ 连接逻辑保持完整")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查连接逻辑失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 70)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有三个问题修复测试通过！")
        print("\n✅ 修复的问题:")
        print("   1️⃣ 串口连接超时后资源释放问题")
        print("      - 超时后强制断开设备连接")
        print("      - 重置设备实例为None")
        print("      - 更新连接状态为False")
        print("      - 提示用户已释放资源")
        print()
        print("   2️⃣ 完全隐藏端口号显示")
        print("      - 移除端口信息标签")
        print("      - 移除设置提示文字")
        print("      - 删除端口显示更新方法")
        print("      - 简化电刺激设备组布局")
        print()
        print("   3️⃣ 移除刺激参数组")
        print("      - 删除刺激参数提示组")
        print("      - 移除参数配置说明文字")
        print("      - 清理相关布局代码")
        print("      - 保持功能逻辑不变")
        print()
        print("🚀 现在的用户体验:")
        print("   ⚡ 连接更稳定：超时后正确释放资源")
        print("   🎯 界面更简洁：无多余的端口和参数显示")
        print("   🔧 操作更直接：直接连接，无需选择")
        print("   📱 布局更清爽：移除不必要的提示信息")
        print()
        print("💡 解决的核心问题:")
        print("   ❌ 串口超时后无法重连 → ✅ 正确释放资源")
        print("   ❌ 界面显示冗余信息 → ✅ 简洁直观")
        print("   ❌ 用户需要手动选择 → ✅ 自动使用配置")
        return 0
    else:
        print("⚠️ 部分问题修复测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    sys.exit(test_three_fixes())
