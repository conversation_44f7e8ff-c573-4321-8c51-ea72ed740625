#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试迁移学习设置修复
Test Transfer Learning Settings Fix

验证UI中的迁移学习设置能正确传递到模型训练

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


def test_transfer_learning_settings():
    """测试迁移学习设置传递"""
    print("🧪 测试迁移学习设置修复")
    print("=" * 60)
    
    try:
        # 测试1: 模拟UI设置迁移学习
        print("\n📋 测试1: 模拟UI设置迁移学习")
        print("-" * 40)
        
        # 创建模型
        model = MotorImageryModel("Transfer_Settings_Test")
        print("✅ 创建模型成功")
        
        # 模拟UI设置（这是修复前的问题所在）
        model_info = model.get_model_info()
        model_info.transfer_learning = True
        model_info.finetune_layers = 3
        print("✅ 设置迁移学习参数")
        print(f"   - 迁移学习: {model_info.transfer_learning}")
        print(f"   - 微调层数: {model_info.finetune_layers}")
        
        # 添加训练数据
        print("\n🔄 添加训练数据...")
        n_samples = 20
        for i in range(n_samples):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model.add_training_data(data, label)
        
        print(f"✅ 添加训练数据: {n_samples} 个样本")
        
        # 验证设置是否保持
        print("\n🔍 验证设置保持:")
        model_info = model.get_model_info()
        print(f"   - 迁移学习: {getattr(model_info, 'transfer_learning', False)}")
        print(f"   - 微调层数: {getattr(model_info, 'finetune_layers', 0)}")
        
        # 开始训练
        print("\n🚀 开始训练...")
        
        def progress_callback(message, progress):
            if progress % 25 == 0:  # 只显示关键进度
                print(f"  [{progress:3d}%] {message}")
        
        config = TrainingConfig(epochs=3, batch_size=8, learning_rate=0.001)
        
        start_time = time.time()
        success = model.train_model(config=config, progress_callback=progress_callback)
        training_time = time.time() - start_time
        
        if success:
            print(f"✅ 训练完成，耗时: {training_time:.1f}秒")
            
            # 检查是否真的使用了迁移学习
            model_info = model.get_model_info()
            if hasattr(model_info, 'used_transfer_learning'):
                if model_info.used_transfer_learning:
                    print("🎯 确认使用了迁移学习！")
                    print(f"   📁 预训练模型: {getattr(model_info, 'pretrained_model_path', '未知')}")
                    print(f"   🔧 微调层数: {getattr(model_info, 'finetune_layers', '未知')}")
                    return True
                else:
                    print("❌ 设置了迁移学习但实际使用了从头训练")
                    return False
            else:
                print("⚠️ 无法确定是否使用了迁移学习（缺少标记）")
                return False
        else:
            print("❌ 训练失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_settings_application():
    """测试UI设置应用方法"""
    print("\n📋 测试2: UI设置应用方法")
    print("-" * 40)
    
    try:
        # 模拟UI设置类
        class MockUI:
            def __init__(self):
                self.transfer_learning_checkbox = MockCheckBox(True)
                self.finetune_layers_spinbox = MockSpinBox(3)
                self.temperature_spinbox = MockSpinBox(1.0)
                self.activation_threshold_spin = MockSpinBox(0.5)
                self.class_weight_spinbox = MockSpinBox(1.0)
                self.smoothing_spinbox = MockSpinBox(3)
                self.adaptive_learning_checkbox = MockCheckBox(False)
                self.current_model = None
                
            def add_training_log(self, message):
                print(f"   📝 {message}")
                
            def _apply_ui_settings_to_model(self):
                """将UI中的设置应用到当前模型"""
                try:
                    if not self.current_model:
                        return
                    
                    model_info = self.current_model.get_model_info()
                    
                    # 应用迁移学习设置
                    transfer_learning_enabled = self.transfer_learning_checkbox.isChecked()
                    model_info.transfer_learning = transfer_learning_enabled
                    print(f"   ✅ 应用迁移学习设置: {transfer_learning_enabled}")
                    
                    # 应用微调层数设置
                    finetune_layers = self.finetune_layers_spinbox.value()
                    model_info.finetune_layers = finetune_layers
                    print(f"   ✅ 应用微调层数设置: {finetune_layers}")
                    
                    # 应用其他设置
                    model_info.temperature = self.temperature_spinbox.value()
                    model_info.decision_threshold = self.activation_threshold_spin.value()
                    model_info.class_weight_ratio = self.class_weight_spinbox.value()
                    model_info.smoothing_window = self.smoothing_spinbox.value()
                    model_info.adaptive_learning = self.adaptive_learning_checkbox.isChecked()
                    
                    print("   ✅ 所有UI设置已应用到模型")
                    
                except Exception as e:
                    print(f"   ❌ 应用UI设置失败: {e}")
        
        class MockCheckBox:
            def __init__(self, checked):
                self._checked = checked
            def isChecked(self):
                return self._checked
        
        class MockSpinBox:
            def __init__(self, value):
                self._value = value
            def value(self):
                return self._value
        
        # 创建模拟UI
        ui = MockUI()
        print("✅ 创建模拟UI")
        
        # 创建模型
        model = MotorImageryModel("UI_Settings_Test")
        ui.current_model = model
        print("✅ 创建模型并关联到UI")
        
        # 应用UI设置
        print("\n🔧 应用UI设置:")
        ui._apply_ui_settings_to_model()
        
        # 验证设置
        print("\n🔍 验证应用结果:")
        model_info = model.get_model_info()
        print(f"   - 迁移学习: {getattr(model_info, 'transfer_learning', False)}")
        print(f"   - 微调层数: {getattr(model_info, 'finetune_layers', 0)}")
        print(f"   - 温度缩放: {getattr(model_info, 'temperature', 1.0)}")
        print(f"   - 激活阈值: {getattr(model_info, 'decision_threshold', 0.5)}")
        print(f"   - 自适应学习: {getattr(model_info, 'adaptive_learning', False)}")
        
        # 检查设置是否正确
        if (getattr(model_info, 'transfer_learning', False) and 
            getattr(model_info, 'finetune_layers', 0) == 3):
            print("🎉 UI设置应用成功！")
            return True
        else:
            print("❌ UI设置应用失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 迁移学习设置修复测试")
    print("=" * 70)
    print("测试目标:")
    print("1. 验证迁移学习设置能正确传递到模型")
    print("2. 验证UI设置应用方法的正确性")
    print("3. 确保修复后迁移学习能正常工作")
    print()
    
    # 运行测试
    test1_success = test_transfer_learning_settings()
    test2_success = test_ui_settings_application()
    
    print(f"\n📊 测试结果总结:")
    print(f"   测试1 (迁移学习设置): {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   测试2 (UI设置应用): {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎊 所有测试通过！")
        print("迁移学习设置修复成功！")
        print("\n📋 修复内容:")
        print("✅ 新模型创建后立即应用UI设置")
        print("✅ 迁移学习设置正确传递到训练过程")
        print("✅ 用户勾选迁移学习后会真正使用迁移学习")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
