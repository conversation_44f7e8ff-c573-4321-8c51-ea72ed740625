#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EEGNet深度学习系统优化验证脚本
验证系统是否成功转换为纯EEGNet深度学习架构
"""

import sys
import os

def test_core_modules():
    """测试核心模块"""
    print("🔍 测试核心模块...")
    
    try:
        from core.ml_model import MotorImageryModel, ModelManager
        from core.eegnet_model import EEGNetModel
        print("✅ 核心模块导入成功")
        
        # 测试模型创建
        model = MotorImageryModel('test_model')
        print("✅ EEGNet模型创建成功")
        
        # 测试模型管理器
        manager = ModelManager()
        print("✅ EEGNet模型管理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块测试失败: {e}")
        return False

def test_configuration():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    try:
        from utils.app_config import AppConfig
        config = AppConfig()
        
        # 检查是否有深度学习配置
        if hasattr(config, 'deep_learning') or 'deep_learning' in config.config:
            print("✅ 深度学习配置存在")
        else:
            print("⚠️  深度学习配置不存在，检查配置文件")
            
        # 检查是否移除了传统ML配置
        if not (hasattr(config, 'classification') or 'classification' in config.config):
            print("✅ 传统ML配置已移除")
        else:
            print("⚠️  传统ML配置仍然存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_interface_components():
    """测试界面组件"""
    print("\n🔍 测试界面组件...")
    
    try:
        # 检查是否可以导入界面组件
        from ui.model_adjustment_widget import ModelAdjustmentWidget
        print("✅ 界面组件导入成功")
        
        # 检查类注释是否更新
        if "EEGNet" in ModelAdjustmentWidget.__doc__:
            print("✅ 界面组件文档已更新为EEGNet")
        else:
            print("⚠️  界面组件文档未更新")
            
        return True
        
    except Exception as e:
        print(f"❌ 界面组件测试失败: {e}")
        return False

def check_code_cleanup():
    """检查代码清理情况"""
    print("\n🔍 检查代码清理...")
    
    # 检查关键文件中是否还有传统ML引用
    files_to_check = [
        'core/ml_model.py',
        'core/eegnet_model.py', 
        'ui/model_adjustment_widget.py',
        'async_training_wrapper.py'
    ]
    
    traditional_ml_terms = ['lda', 'svm', 'random_forest', 'rf', 'ensemble']
    issues_found = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    
                for term in traditional_ml_terms:
                    if term in content and 'eegnet' not in content[content.find(term):content.find(term)+50]:
                        issues_found.append(f"{file_path}: 发现传统ML术语 '{term}'")
                        
            except Exception as e:
                print(f"⚠️  无法检查文件 {file_path}: {e}")
    
    if not issues_found:
        print("✅ 代码清理完成，未发现传统ML残留")
        return True
    else:
        print("⚠️  发现以下问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False

def test_parameter_names():
    """测试参数名称更新"""
    print("\n🔍 测试参数名称...")
    
    try:
        # 检查异步训练包装器的参数
        import inspect
        from async_training_wrapper import AsyncTrainingWrapper
        
        wrapper = AsyncTrainingWrapper()
        sig = inspect.signature(wrapper.train_model_async)
        
        if 'neural_network' in sig.parameters:
            print("✅ 异步训练参数已更新为neural_network")
        else:
            print("⚠️  异步训练参数未更新")
            
        if 'use_deep_learning' in sig.parameters:
            print("✅ 深度学习标志参数存在")
        else:
            print("⚠️  深度学习标志参数不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 参数名称测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 EEGNet深度学习系统优化验证")
    print("=" * 50)
    
    tests = [
        test_core_modules,
        test_configuration, 
        test_interface_components,
        check_code_cleanup,
        test_parameter_names
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 EEGNet深度学习系统优化验证完全通过！")
        print("✨ 系统已成功转换为纯EEGNet深度学习架构")
    else:
        print("⚠️  部分测试未通过，请检查上述问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
