#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的状态修复验证测试
验证回调函数只返回两种状态：0=暂停，1=刺激中
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('correct_status_fix_test.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_correct_status_mapping():
    """测试正确的状态映射"""
    print("🔧 测试正确的状态映射")
    print("=" * 50)
    
    try:
        # 创建设备实例
        device = StimulationDevice()
        
        print("\n📋 验证设备模块状态映射:")
        print("根据用户说明：回调函数只返回两种状态")
        print("- 0: 暂停")
        print("- 1: 刺激中")
        
        correct_status_map = {
            0: "暂停",
            1: "刺激中"
        }
        
        for status_code, expected_text in correct_status_map.items():
            actual_text = device._get_channel_status_text(status_code)
            if actual_text == expected_text:
                print(f"   ✅ 状态码 {status_code}: {actual_text}")
            else:
                print(f"   ❌ 状态码 {status_code}: 期望 '{expected_text}', 实际 '{actual_text}'")
                return False
        
        # 测试未知状态
        unknown_status = device._get_channel_status_text(99)
        if unknown_status == "未知(99)":
            print(f"   ✅ 未知状态处理正确: {unknown_status}")
        else:
            print(f"   ❌ 未知状态处理错误: {unknown_status}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_status_mapping():
    """测试UI界面状态映射"""
    print("\n🔍 测试UI界面状态映射")
    print("-" * 40)
    
    # 模拟UI界面的_get_channel_display_info方法
    def get_channel_display_info(status: int) -> tuple:
        """根据通道状态获取显示信息"""
        if status == 0:  # 暂停
            return "暂停", "color: orange; font-weight: bold;"
        elif status == 1:  # 刺激中
            return "刺激中", "color: green; font-weight: bold;"
        else:
            return f"未知({status})", "color: red; font-weight: bold;"
    
    print("状态码 | UI显示   | 样式")
    print("-------|----------|----------")
    
    ui_expected_map = {
        0: "暂停",
        1: "刺激中"
    }
    
    all_correct = True
    for status_code, expected_display in ui_expected_map.items():
        actual_display, style = get_channel_display_info(status_code)
        if actual_display == expected_display:
            print(f"{status_code:6d} | {actual_display:8s} | ✅ 正确")
        else:
            print(f"{status_code:6d} | {actual_display:8s} | ❌ 错误")
            all_correct = False
    
    # 测试未知状态
    unknown_display, _ = get_channel_display_info(99)
    if unknown_display == "未知(99)":
        print(f"{99:6d} | {unknown_display:8s} | ✅ 正确")
    else:
        print(f"{99:6d} | {unknown_display:8s} | ❌ 错误")
        all_correct = False
    
    return all_correct

def test_status_verification_logic():
    """测试状态验证逻辑"""
    print("\n📋 测试状态验证逻辑")
    print("-" * 30)
    
    try:
        device = StimulationDevice()
        
        print("验证_verify_stimulation_active函数:")
        test_cases = [
            (0, False, "暂停状态不应该被认为是激活的"),
            (1, True, "刺激中状态应该被认为是激活的")
        ]
        
        all_correct = True
        for status_code, expected_active, description in test_cases:
            device.channel_a_status = status_code
            is_active = device._verify_stimulation_active(1)
            if is_active == expected_active:
                print(f"   ✅ 状态码 {status_code}: {description}")
            else:
                print(f"   ❌ 状态码 {status_code}: {description} - 验证失败")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_consistency_check():
    """测试状态一致性检查"""
    print("\n🔍 测试状态一致性检查")
    print("-" * 30)
    
    try:
        from core.stimulation_device import StimulationDeviceStatus
        device = StimulationDevice()
        
        # 测试场景
        test_scenarios = [
            {
                'device_status': 'STIMULATING',
                'channel_status': 1,
                'expected_consistent': True,
                'description': '界面刺激中，通道刺激中'
            },
            {
                'device_status': 'STIMULATING', 
                'channel_status': 0,
                'expected_consistent': False,
                'description': '界面刺激中，通道暂停'
            },
            {
                'device_status': 'CONNECTED',
                'channel_status': 1,
                'expected_consistent': False,
                'description': '界面已连接，通道刺激中'
            },
            {
                'device_status': 'CONNECTED',
                'channel_status': 0,
                'expected_consistent': True,
                'description': '界面已连接，通道暂停'
            }
        ]
        
        all_correct = True
        for scenario in test_scenarios:
            # 设置测试状态
            if scenario['device_status'] == 'STIMULATING':
                device.status = StimulationDeviceStatus.STIMULATING
            else:
                device.status = StimulationDeviceStatus.CONNECTED
            
            device.channel_a_status = scenario['channel_status']
            
            # 执行诊断
            diagnosis = device.diagnose_stimulation_status(1)
            actual_consistent = diagnosis["is_consistent"]
            expected_consistent = scenario['expected_consistent']
            
            if actual_consistent == expected_consistent:
                print(f"   ✅ {scenario['description']}: 一致性检查正确")
            else:
                print(f"   ❌ {scenario['description']}: 期望 {expected_consistent}, 实际 {actual_consistent}")
                print(f"      错误原因: {diagnosis['error_cause']}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🔧 NK电刺激设备正确状态修复验证")
    print("验证回调函数只返回两种状态：0=暂停，1=刺激中")
    print("=" * 60)
    
    success1 = test_correct_status_mapping()
    success2 = test_ui_status_mapping()
    success3 = test_status_verification_logic()
    success4 = test_consistency_check()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3 and success4:
        print("✅ 正确状态修复验证成功！")
        print("🎯 验证结果：")
        print("   - ✅ 设备模块状态映射正确 (0=暂停, 1=刺激中)")
        print("   - ✅ UI界面状态映射正确")
        print("   - ✅ 状态验证逻辑正确")
        print("   - ✅ 状态一致性检查正确")
        print("\n📋 最终状态对应关系：")
        print("   0: 暂停 -> 设备暂停状态")
        print("   1: 刺激中 -> 设备正在输出刺激")
        print("   其他: 未知状态")
    else:
        print("❌ 正确状态修复验证失败")
        print("需要进一步检查修复内容")
    
    print("📄 详细日志：correct_status_fix_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
