#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通道状态修复
验证状态映射是否正确
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('status_fix_test.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_status_mapping():
    """测试状态映射是否正确"""
    print("🔧 测试通道状态映射修复")
    print("=" * 50)

    try:
        # 创建设备实例
        device = StimulationDevice()

        # 测试状态映射函数
        print("\n📋 测试状态映射函数:")
        test_cases = [
            (0, "停止"),
            (1, "暂停"),
            (2, "电流调节"),
            (3, "正常工作"),
            (99, "未知(99)")  # 应该显示未知
        ]

        for status_code, expected_text in test_cases:
            actual_text = device._get_channel_status_text(status_code)
            if actual_text == expected_text:
                print(f"   ✅ 状态码 {status_code}: {actual_text}")
            else:
                print(f"   ❌ 状态码 {status_code}: 期望 '{expected_text}', 实际 '{actual_text}'")

        # 测试连接和状态监控
        print("\n🔌 测试设备连接和状态监控:")
        connection_success = device.connect()
        if connection_success:
            print("   ✅ 设备连接成功")

            # 监控初始状态
            print("\n📊 监控通道状态:")
            for i in range(3):  # 减少监控时间
                a_status = device.get_channel_status(1)
                b_status = device.get_channel_status(2)
                a_text = device._get_channel_status_text(a_status)
                b_text = device._get_channel_status_text(b_status)

                print(f"   [{i+1}] A通道: {a_status} ({a_text}), B通道: {b_status} ({b_text})")
                time.sleep(0.5)  # 减少等待时间

            device.disconnect()
            print("   ✅ 设备断开成功")

        else:
            print("   ⚠️ 设备连接失败（无硬件设备，这是正常的）")

        # 测试状态验证函数（不需要硬件连接）
        print("\n🔍 测试状态验证函数:")
        from core.stimulation_device import StimulationDeviceStatus
        device.status = StimulationDeviceStatus.STIMULATING

        # 模拟不同的通道状态
        test_statuses = [0, 1, 2, 3]
        for test_status in test_statuses:
            device.channel_a_status = test_status
            is_active = device._verify_stimulation_active(1)
            expected = (test_status == 3)  # 只有状态3才是正常工作

            if is_active == expected:
                print(f"   ✅ 通道状态 {test_status}: 验证结果 {is_active} (正确)")
            else:
                print(f"   ❌ 通道状态 {test_status}: 验证结果 {is_active}, 期望 {expected}")

        print("\n🎉 状态映射修复测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_consistency():
    """测试状态一致性检查"""
    print("\n🔍 测试状态一致性检查")
    print("-" * 30)

    try:
        device = StimulationDevice()
        from core.stimulation_device import StimulationDeviceStatus

        # 测试诊断函数
        print("📋 测试诊断函数:")

        # 模拟不同的状态组合
        test_scenarios = [
            {
                'device_status': 'STIMULATING',
                'channel_status': 3,
                'expected_consistent': True,
                'description': '界面刺激中，通道正常工作'
            },
            {
                'device_status': 'STIMULATING',
                'channel_status': 0,
                'expected_consistent': False,
                'description': '界面刺激中，通道停止'
            },
            {
                'device_status': 'STIMULATING',
                'channel_status': 1,
                'expected_consistent': False,
                'description': '界面刺激中，通道暂停'
            },
            {
                'device_status': 'STIMULATING',
                'channel_status': 2,
                'expected_consistent': False,
                'description': '界面刺激中，通道电流调节'
            },
            {
                'device_status': 'CONNECTED',
                'channel_status': 3,
                'expected_consistent': False,
                'description': '界面已连接，通道正常工作'
            },
            {
                'device_status': 'CONNECTED',
                'channel_status': 0,
                'expected_consistent': True,
                'description': '界面已连接，通道停止'
            }
        ]

        for scenario in test_scenarios:
            # 设置测试状态
            if scenario['device_status'] == 'STIMULATING':
                device.status = StimulationDeviceStatus.STIMULATING
            else:
                device.status = StimulationDeviceStatus.CONNECTED

            device.channel_a_status = scenario['channel_status']

            # 执行诊断
            diagnosis = device.diagnose_stimulation_status(1)

            # 检查结果
            is_consistent = diagnosis['is_consistent']
            expected = scenario['expected_consistent']

            if is_consistent == expected:
                print(f"   ✅ {scenario['description']}: 一致性检查正确")
            else:
                print(f"   ❌ {scenario['description']}: 期望 {expected}, 实际 {is_consistent}")
                print(f"      错误原因: {diagnosis['error_cause']}")

        print("✅ 状态一致性检查测试完成")
        return True

    except Exception as e:
        print(f"❌ 状态一致性测试失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()

    print("🔧 NK电刺激设备状态修复验证")
    print("验证通道状态映射是否正确修复")
    print("=" * 60)

    success1 = test_status_mapping()
    success2 = test_status_consistency()

    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ 状态修复验证成功！")
        print("🎯 修复内容：")
        print("   - 通道状态映射: 0=停止, 1=暂停, 2=电流调节, 3=正常工作")
        print("   - 状态验证逻辑: 正常工作状态为3")
        print("   - 诊断函数: 使用正确的状态检查逻辑")
        print("   - 测试文件: 使用正确的状态期望值")
    else:
        print("❌ 状态修复验证失败")
        print("需要进一步检查修复内容")

    print("📄 详细日志：status_fix_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
