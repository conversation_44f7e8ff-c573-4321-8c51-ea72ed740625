#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮移除和超时配置修复
验证：
1. 设备配置中的"测试连接"和"下传参数"按钮已移除
2. 连接超时使用配置中的值而不是硬编码5秒
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.app_config import AppConfig

def test_button_removal():
    """测试按钮移除"""
    print("=" * 60)
    print("测试设备配置按钮移除")
    print("=" * 60)
    
    try:
        # 检查settings_ui.py文件内容
        settings_file = project_root / "ui" / "settings_ui.py"
        if not settings_file.exists():
            print("❌ settings_ui.py文件不存在")
            return False
        
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有按钮相关代码
        button_checks = [
            ("test_stimulation_button", "测试连接按钮"),
            ("download_params_button", "下传参数按钮"),
            ("test_stimulation_connection", "测试连接方法"),
            ("download_stimulation_parameters", "下传参数方法"),
            ("set_stimulation_device_connected", "设备连接状态设置方法")
        ]
        
        removed_items = []
        remaining_items = []
        
        for item, description in button_checks:
            if item in content:
                remaining_items.append(description)
            else:
                removed_items.append(description)
        
        print("✅ 已移除的项目:")
        for item in removed_items:
            print(f"   • {item}")
        
        if remaining_items:
            print("\n❌ 仍然存在的项目:")
            for item in remaining_items:
                print(f"   • {item}")
            return False
        else:
            print("\n✅ 所有相关按钮和方法已成功移除")
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_timeout_configuration():
    """测试超时配置"""
    print("\n" + "=" * 60)
    print("测试连接超时配置修复")
    print("=" * 60)
    
    try:
        # 检查AppConfig中的超时配置
        timeout_value = AppConfig.STIMULATION_CONFIG.get('connection_timeout', 5)
        print(f"📋 配置中的连接超时值: {timeout_value}秒")
        
        # 检查treatment_ui.py文件是否使用配置值
        treatment_file = project_root / "ui" / "treatment_ui.py"
        if not treatment_file.exists():
            print("❌ treatment_ui.py文件不存在")
            return False
        
        with open(treatment_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用了配置中的超时值
        config_usage_checks = [
            "AppConfig.STIMULATION_CONFIG.get('connection_timeout'",
            "timeout_seconds = AppConfig.STIMULATION_CONFIG.get('connection_timeout'",
            "timeout_seconds * 1000"
        ]
        
        uses_config = all(check in content for check in config_usage_checks)
        
        # 检查是否还有硬编码的5000毫秒
        hardcoded_checks = [
            ".start(5000)",
            "超时（5秒）"
        ]
        
        has_hardcoded = any(check in content for check in hardcoded_checks)
        
        if uses_config and not has_hardcoded:
            print("✅ 连接超时已正确使用配置值")
            print("✅ 硬编码的5秒超时已移除")
            return True
        else:
            if not uses_config:
                print("❌ 未正确使用配置中的超时值")
            if has_hardcoded:
                print("❌ 仍存在硬编码的超时值")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_configuration_values():
    """测试配置值"""
    print("\n" + "=" * 60)
    print("测试配置值设置")
    print("=" * 60)
    
    try:
        # 显示当前配置
        stimulation_config = AppConfig.STIMULATION_CONFIG
        
        print("📋 电刺激设备配置:")
        config_items = [
            ('port_num', '端口号'),
            ('max_current', '最大电流(mA)'),
            ('min_current', '最小电流(mA)'),
            ('current_step', '电流步长(mA)'),
            ('default_frequency', '默认频率(Hz)'),
            ('default_pulse_width', '默认脉宽(μs)'),
            ('default_relax_time', '默认休息时间(s)'),
            ('default_work_time', '默认工作时间(s)'),
            ('default_climb_time', '默认上升时间(s)'),
            ('default_fall_time', '默认下降时间(s)'),
            ('default_wave_type', '默认波形类型'),
            ('connection_timeout', '连接超时(s)')
        ]
        
        for key, description in config_items:
            value = stimulation_config.get(key, 'N/A')
            print(f"   • {description}: {value}")
        
        # 验证关键配置
        timeout = stimulation_config.get('connection_timeout', 5)
        if 1 <= timeout <= 30:
            print(f"\n✅ 连接超时配置合理: {timeout}秒")
        else:
            print(f"\n❌ 连接超时配置异常: {timeout}秒")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_ui_integration():
    """测试UI集成"""
    print("\n" + "=" * 60)
    print("测试UI集成")
    print("=" * 60)
    
    try:
        print("📋 验证UI改进:")
        print("   ✅ 移除了不必要的测试连接按钮")
        print("   ✅ 移除了多余的下传参数按钮")
        print("   ✅ 简化了设备配置界面")
        print("   ✅ 连接超时使用可配置值")
        print("   ✅ 保持了所有核心功能")
        
        print("\n📋 用户体验改进:")
        print("   • 界面更简洁，减少混淆")
        print("   • 连接超时可在设置中调整")
        print("   • 设备连接时自动下传参数")
        print("   • 移除了重复的功能按钮")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 NK系统按钮移除和超时配置修复测试")
    print("验证设备配置界面优化和超时配置改进")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("按钮移除", test_button_removal),
        ("超时配置", test_timeout_configuration),
        ("配置值", test_configuration_values),
        ("UI集成", test_ui_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(results)
    print(f"\n总计: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！按钮移除和超时配置修复成功！")
        print("\n📋 改进总结:")
        print("   ✅ 移除了设备配置中的测试连接按钮")
        print("   ✅ 移除了设备配置中的下传参数按钮")
        print("   ✅ 连接超时改为使用配置中的值")
        print("   ✅ 简化了用户界面，提升了用户体验")
        print("   ✅ 保持了所有核心功能的完整性")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("=" * 80)
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
