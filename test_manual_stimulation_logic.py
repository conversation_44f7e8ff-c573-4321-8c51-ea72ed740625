#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动刺激逻辑
Test Manual Stimulation Logic

验证按照手动点击"开始刺激"按钮的逻辑实现：
1. 检查通道勾选状态
2. 检查电流设置
3. 启动对应的通道

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_checkbox_logic():
    """测试通道勾选逻辑"""
    print("=== 测试通道勾选逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面
        class MockTreatmentUI:
            def __init__(self, a_checked=True, b_checked=False, a_current=2, b_current=0):
                # 模拟复选框
                class MockCheckBox:
                    def __init__(self, checked):
                        self._checked = checked
                    def isChecked(self):
                        return self._checked
                
                # 模拟电流控件
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_checkbox = MockCheckBox(a_checked)
                self.channel_b_checkbox = MockCheckBox(b_checked)
                self.channel_a_current = MockSpinBox(a_current)
                self.channel_b_current = MockSpinBox(b_current)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.start_calls = []
                self.set_current_calls = []
                self.dll_calls = []
                
            def is_connected(self):
                return self.connected
                
            def set_current(self, channel_num, current_value):
                self.set_current_calls.append((channel_num, current_value))
                return True
                
            def _safe_dll_call(self, func_name, *args):
                self.dll_calls.append((func_name, args))
                return 0  # 成功
        
        # 测试场景1：只勾选A通道，A通道有电流
        print("\n场景1：只勾选A通道，A通道2mA")
        mock_ui = MockTreatmentUI(a_checked=True, b_checked=False, a_current=2, b_current=0)
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        success = controller._start_stimulation()
        
        print(f"A通道勾选: {mock_ui.channel_a_checkbox.isChecked()}")
        print(f"B通道勾选: {mock_ui.channel_b_checkbox.isChecked()}")
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"B通道电流: {mock_ui.channel_b_current.value()}mA")
        print(f"启动结果: {success}")
        print(f"电流设置调用: {mock_device.set_current_calls}")
        print(f"DLL调用: {mock_device.dll_calls}")
        
        # 验证结果
        if (success and 
            len(mock_device.set_current_calls) == 1 and 
            mock_device.set_current_calls[0] == (1, 2) and
            len(mock_device.dll_calls) == 1 and
            mock_device.dll_calls[0] == ('SwitchChannelState', (1, 3))):
            print("✓ 场景1测试通过：只启动A通道")
            return True
        else:
            print("✗ 场景1测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_dual_channel_logic():
    """测试双通道逻辑"""
    print("\n=== 测试双通道逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A和B都勾选，都有电流）
        class MockTreatmentUI:
            def __init__(self):
                class MockCheckBox:
                    def __init__(self, checked):
                        self._checked = checked
                    def isChecked(self):
                        return self._checked
                
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_checkbox = MockCheckBox(True)
                self.channel_b_checkbox = MockCheckBox(True)
                self.channel_a_current = MockSpinBox(5)
                self.channel_b_current = MockSpinBox(3)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.fast_dual_calls = []
                
            def is_connected(self):
                return self.connected
                
            def fast_dual_channel_start(self, channel_a_current, channel_b_current):
                self.fast_dual_calls.append((channel_a_current, channel_b_current))
                return True
        
        # 测试双通道快速启动
        print("场景：A和B都勾选，A=5mA，B=3mA")
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        success = controller._start_stimulation()
        
        print(f"A通道勾选: {mock_ui.channel_a_checkbox.isChecked()}")
        print(f"B通道勾选: {mock_ui.channel_b_checkbox.isChecked()}")
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"B通道电流: {mock_ui.channel_b_current.value()}mA")
        print(f"启动结果: {success}")
        print(f"快速双通道调用: {mock_device.fast_dual_calls}")
        
        # 验证结果
        if (success and 
            len(mock_device.fast_dual_calls) == 1 and 
            mock_device.fast_dual_calls[0] == (5, 3)):
            print("✓ 双通道测试通过：使用快速双通道启动")
            return True
        else:
            print("✗ 双通道测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_zero_current_logic():
    """测试零电流逻辑"""
    print("\n=== 测试零电流逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A勾选但电流为0）
        class MockTreatmentUI:
            def __init__(self):
                class MockCheckBox:
                    def __init__(self, checked):
                        self._checked = checked
                    def isChecked(self):
                        return self._checked
                
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_checkbox = MockCheckBox(True)
                self.channel_b_checkbox = MockCheckBox(False)
                self.channel_a_current = MockSpinBox(0)  # 电流为0
                self.channel_b_current = MockSpinBox(0)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.calls = []
                
            def is_connected(self):
                return self.connected
        
        # 测试零电流场景
        print("场景：A通道勾选但电流为0mA")
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        success = controller._start_stimulation()
        
        print(f"A通道勾选: {mock_ui.channel_a_checkbox.isChecked()}")
        print(f"A通道电流: {mock_ui.channel_a_current.value()}mA")
        print(f"启动结果: {success}")
        
        # 验证结果
        if not success:
            print("✓ 零电流测试通过：正确拒绝启动")
            return True
        else:
            print("✗ 零电流测试失败：不应该启动")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_no_checkbox_logic():
    """测试未勾选通道逻辑"""
    print("\n=== 测试未勾选通道逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A和B都未勾选）
        class MockTreatmentUI:
            def __init__(self):
                class MockCheckBox:
                    def __init__(self, checked):
                        self._checked = checked
                    def isChecked(self):
                        return self._checked
                
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_checkbox = MockCheckBox(False)
                self.channel_b_checkbox = MockCheckBox(False)
                self.channel_a_current = MockSpinBox(5)
                self.channel_b_current = MockSpinBox(3)
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                
            def is_connected(self):
                return self.connected
        
        # 测试未勾选场景
        print("场景：A和B都未勾选（虽然有电流）")
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        success = controller._start_stimulation()
        
        print(f"A通道勾选: {mock_ui.channel_a_checkbox.isChecked()}")
        print(f"B通道勾选: {mock_ui.channel_b_checkbox.isChecked()}")
        print(f"启动结果: {success}")
        
        # 验证结果
        if not success:
            print("✓ 未勾选测试通过：正确拒绝启动")
            return True
        else:
            print("✗ 未勾选测试失败：不应该启动")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试手动刺激逻辑...")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("通道勾选逻辑", test_checkbox_logic),
        ("双通道逻辑", test_dual_channel_logic),
        ("零电流逻辑", test_zero_current_logic),
        ("未勾选通道逻辑", test_no_checkbox_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n修复总结:")
        print("1. ✅ 检查通道勾选状态")
        print("2. ✅ 检查电流设置")
        print("3. ✅ 只启动勾选且有电流的通道")
        print("4. ✅ 双通道使用快速启动")
        print("5. ✅ 单通道使用标准启动")
        print("6. ✅ 正确处理异常情况")
        return True
    else:
        print("❌ 部分测试失败，请检查代码修改。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
