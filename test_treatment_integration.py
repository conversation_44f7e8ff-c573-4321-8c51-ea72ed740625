#!/usr/bin/env python3
"""
测试治疗系统界面的EEGNet集成
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_treatment_integration():
    """测试治疗系统的EEGNet集成"""
    print("=" * 60)
    print("测试治疗系统的EEGNet集成")
    print("=" * 60)
    
    try:
        # 测试导入
        print("1. 测试导入...")
        from core.ml_model import MotorImageryModel, ModelManager
        from core.eegnet_model import TrainingConfig
        print("   ✅ 成功导入ML模型和EEGNet配置")
        
        # 测试模型管理器
        print("\n2. 测试模型管理器...")
        manager = ModelManager()
        print(f"   ✅ 模型管理器创建成功，使用EEGNet: {manager.use_eegnet}")
        
        # 测试创建模型
        print("\n3. 测试创建模型...")
        model = manager.create_model("Treatment_Test_Model")
        print(f"   ✅ 模型创建成功: {model.model_name}")
        print(f"   - 使用EEGNet: {model.use_eegnet}")
        print(f"   - 已训练: {model.is_trained}")
        
        # 测试添加训练数据
        print("\n4. 测试添加训练数据...")
        for i in range(20):
            # 模拟8通道EEG数据，250个采样点（2秒@125Hz）
            data = np.random.randn(8, 250) * 100  # 添加一些幅值
            label = i % 2  # 0: 休息状态, 1: 运动想象
            success = model.add_training_data(data, label)
            if not success:
                print(f"   ❌ 添加第{i+1}个样本失败")
                break
        
        print(f"   ✅ 成功添加 {len(model.training_data)} 个训练样本")
        print(f"   - 标签分布: {np.bincount(model.training_labels)}")
        
        # 测试模型信息
        print("\n5. 测试模型信息...")
        info = model.get_model_info()
        print(f"   ✅ 模型信息获取成功")
        print(f"   - 模型名称: {info.name}")
        print(f"   - 总样本数: {info.total_samples}")
        print(f"   - 训练轮次: {info.training_rounds}")
        
        # 测试训练配置
        print("\n6. 测试训练配置...")
        config = TrainingConfig(
            epochs=2,  # 少量epoch用于测试
            batch_size=4,
            learning_rate=0.001,
            validation_split=0.2
        )
        print(f"   ✅ 训练配置创建成功")
        print(f"   - Epochs: {config.epochs}")
        print(f"   - Batch Size: {config.batch_size}")
        print(f"   - Learning Rate: {config.learning_rate}")
        
        # 测试模型训练（如果TensorFlow可用）
        print("\n7. 测试模型训练...")
        try:
            import tensorflow as tf
            print(f"   TensorFlow版本: {tf.__version__}")
            
            # 尝试训练
            print("   开始训练...")
            success = model.train_model("eegnet", config)
            
            if success:
                print("   ✅ 模型训练成功")
                print(f"   - 模型已训练: {model.is_trained}")
                
                # 测试预测
                print("\n8. 测试预测...")
                test_data = np.random.randn(8, 250) * 100
                prediction, confidence = model.predict(test_data)
                print(f"   ✅ 预测成功: 类别={prediction}, 置信度={confidence:.3f}")
                
                # 测试带调整的预测
                adj_pred, adj_conf, status = model.predict_with_adjustment(test_data)
                print(f"   ✅ 调整预测: 类别={adj_pred}, 置信度={adj_conf:.3f}, 状态={status}")
                
            else:
                print("   ⚠️  模型训练失败（可能是依赖问题）")
                
        except ImportError:
            print("   ⚠️  TensorFlow未安装，跳过训练测试")
        except Exception as e:
            print(f"   ⚠️  训练测试失败: {e}")
        
        # 测试模型保存
        print("\n9. 测试模型保存...")
        save_success = manager.save_model(model, "test_treatment_model")
        print(f"   模型保存: {'成功' if save_success else '失败'}")
        
        # 测试模型列表
        print("\n10. 测试模型列表...")
        models = manager.list_models()
        print(f"   ✅ 可用模型数量: {len(models)}")
        for model_info in models:
            print(f"   - {model_info.get('name', 'Unknown')}")
        
        print("\n" + "=" * 60)
        print("🎉 治疗系统EEGNet集成测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_treatment_integration()
