#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度学习参数控件
Test script for deep learning parameter controls
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_deep_learning_controls():
    """测试深度学习参数控件"""
    print("=" * 60)
    print("深度学习参数控件测试")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：导入TreatmentWidget
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 导入 TreatmentWidget...")
    try:
        from ui.treatment_ui import TreatmentWidget
        print("✅ TreatmentWidget 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return 1
    
    # 测试2：检查深度学习参数控件
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查深度学习参数控件...")
    try:
        # 检查文件中是否包含深度学习控件定义
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查深度学习控件
        dl_controls = [
            'temperature_spinbox',
            'activation_threshold_spin', 
            'class_weight_spinbox',
            'smoothing_slider',
            'smoothing_label',
            'adaptive_learning_checkbox',
            'transfer_learning_checkbox',
            'finetune_layers_spinbox',
            'neural_calibrate_btn'
        ]
        
        missing_controls = []
        for control in dl_controls:
            if f'self.{control}' not in content:
                missing_controls.append(control)
        
        if missing_controls:
            print(f"❌ 缺少深度学习控件: {missing_controls}")
        else:
            print("✅ 所有深度学习控件都存在")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查深度学习控件失败: {e}")
    
    # 测试3：检查深度学习参数组
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查深度学习参数组...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查参数组定义
        if 'EEGNet深度学习参数组' in content:
            print("✅ EEGNet深度学习参数组存在")
            tests_passed += 1
        else:
            print("❌ EEGNet深度学习参数组不存在")
            
    except Exception as e:
        print(f"❌ 检查深度学习参数组失败: {e}")
    
    # 测试4：检查信号连接
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查深度学习信号连接...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查信号连接
        signal_connections = [
            'temperature_spinbox.valueChanged.connect',
            'activation_threshold_spin.valueChanged.connect',
            'class_weight_spinbox.valueChanged.connect',
            'smoothing_slider.valueChanged.connect',
            'adaptive_learning_checkbox.toggled.connect',
            'transfer_learning_checkbox.toggled.connect',
            'finetune_layers_spinbox.valueChanged.connect',
            'neural_calibrate_btn.clicked.connect'
        ]
        
        missing_connections = []
        for connection in signal_connections:
            if connection not in content:
                missing_connections.append(connection)
        
        if missing_connections:
            print(f"❌ 缺少信号连接: {missing_connections}")
        else:
            print("✅ 所有深度学习信号连接都存在")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查信号连接失败: {e}")
    
    # 测试5：检查控件参数设置
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查控件参数设置...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键参数设置
        parameter_settings = [
            'setRange(0.1, 5.0)',  # 温度缩放范围
            'setRange(0.3, 0.8)',  # 激活阈值范围
            'setRange(0.1, 10.0)', # 类别权重范围
            'setRange(1, 10)',     # 平滑滑块范围
            'setRange(1, 8)',      # 微调层数范围
            'setToolTip('          # 工具提示
        ]
        
        missing_settings = []
        for setting in parameter_settings:
            if setting not in content:
                missing_settings.append(setting)
        
        if missing_settings:
            print(f"❌ 缺少参数设置: {missing_settings}")
        else:
            print("✅ 所有控件参数设置都正确")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查控件参数设置失败: {e}")
    
    # 测试6：检查布局结构
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 检查布局结构...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局结构
        layout_elements = [
            'dl_params_group = QGroupBox("EEGNet深度学习参数")',
            'dl_params_layout = QVBoxLayout(dl_params_group)',
            'temperature_layout = QHBoxLayout()',
            'activation_layout = QHBoxLayout()',
            'class_weight_layout = QHBoxLayout()',
            'smoothing_layout = QHBoxLayout()',
            'adaptive_layout = QHBoxLayout()',
            'transfer_layout = QHBoxLayout()',
            'finetune_layout = QHBoxLayout()'
        ]
        
        missing_layouts = []
        for layout in layout_elements:
            if layout not in content:
                missing_layouts.append(layout)
        
        if missing_layouts:
            print(f"❌ 缺少布局元素: {missing_layouts}")
        else:
            print("✅ 所有布局结构都正确")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查布局结构失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 60)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！深度学习参数控件添加成功！")
        print("\n✅ 已添加的深度学习控件:")
        print("   - 温度缩放参数 (0.1-5.0)")
        print("   - 激活阈值 (0.3-0.8)")
        print("   - 类别权重比 (0.1-10.0)")
        print("   - 预测平滑滑块 (1-10)")
        print("   - 自适应学习复选框")
        print("   - 迁移学习复选框")
        print("   - 微调层数设置 (1-8)")
        print("   - 神经网络校准按钮")
        print("   - 所有控件都有完整的信号连接")
        print("   - 所有控件都有工具提示说明")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查深度学习控件添加结果")
        return 1

if __name__ == "__main__":
    sys.exit(test_deep_learning_controls())
