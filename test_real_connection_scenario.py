#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备连接真实场景测试
模拟用户实际遇到的连接问题：连接错误端口后切换到正确端口
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('real_connection_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_real_connection_scenario():
    """测试真实连接场景"""
    print("🔧 电刺激设备真实连接场景测试")
    print("模拟用户日志中的问题：连接COM26超时后，切换到COM7仍然超时")
    print("=" * 70)
    
    try:
        # 导入必要的模块
        from core.stimulation_device import StimulationDevice
        from utils.app_config import AppConfig
        
        # 创建设备实例
        device = StimulationDevice()
        
        # 测试结果记录
        test_results = []
        
        print("\n📍 步骤1: 连接COM26（错误端口，模拟用户设置错误）")
        wrong_port = 26
        print(f"   尝试连接端口: COM{wrong_port}")
        
        start_time = time.time()
        result1 = device.connect(wrong_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result1 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if not result1:
            print("   ✅ 错误端口连接正确失败")
            test_results.append(('COM26连接失败', True))
        else:
            print("   ❌ 错误端口连接异常成功")
            test_results.append(('COM26连接失败', False))
        
        print("\n⏳ 等待资源清理完成（模拟用户操作间隔）...")
        time.sleep(1)
        
        print("\n📍 步骤2: 立即切换到COM7（正确端口）")
        correct_port = 7
        print(f"   尝试连接端口: COM{correct_port}")
        print("   这是关键测试：之前会因为资源未清理而失败")
        
        start_time = time.time()
        result2 = device.connect(correct_port)
        elapsed_time = time.time() - start_time
        
        print(f"   连接结果: {'成功' if result2 else '失败'}")
        print(f"   耗时: {elapsed_time:.2f}秒")
        
        if result2:
            print("   🎉 正确端口连接成功！问题已修复")
            test_results.append(('COM7连接成功', True))
            
            # 断开连接
            print("   断开连接...")
            device.disconnect()
        else:
            print("   ❌ 正确端口连接失败，问题仍然存在")
            test_results.append(('COM7连接成功', False))
        
        print("\n📍 步骤3: 重复测试（验证修复的稳定性）")
        for i in range(3):
            print(f"\n   第{i+1}轮测试:")
            
            # 连接错误端口
            print(f"     连接COM{wrong_port}...")
            device.connect(wrong_port)
            time.sleep(0.5)
            
            # 立即连接正确端口
            print(f"     立即连接COM{correct_port}...")
            result = device.connect(correct_port)
            
            if result:
                print(f"     ✅ 第{i+1}轮成功")
                device.disconnect()
                test_results.append((f'第{i+1}轮测试', True))
            else:
                print(f"     ❌ 第{i+1}轮失败")
                test_results.append((f'第{i+1}轮测试', False))
            
            time.sleep(0.5)
        
        # 输出测试总结
        print("\n" + "=" * 70)
        print("📊 测试结果总结:")
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, passed in test_results:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"   {test_name}: {status}")
            if passed:
                passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 80:  # 允许一些容错
            print("🎉 测试通过！连接超时问题已修复")
            print("✅ 用户现在可以在连接错误端口后立即切换到正确端口")
            return True
        else:
            print("❌ 测试失败，连接超时问题未完全解决")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_real_connection_scenario()
    sys.exit(0 if success else 1)
