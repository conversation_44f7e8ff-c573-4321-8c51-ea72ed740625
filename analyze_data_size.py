#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据目录分析工具
分析data目录中的文件大小，帮助决定打包策略
"""

import os
from pathlib import Path
import json

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def analyze_directory(path):
    """分析目录"""
    analysis = {
        'total_size': 0,
        'file_count': 0,
        'subdirs': {},
        'large_files': [],
        'files_by_type': {}
    }

    try:
        path = Path(path)

        # 遍历所有文件
        for item in path.rglob('*'):
            if item.is_file():
                try:
                    size = item.stat().st_size
                    analysis['total_size'] += size
                    analysis['file_count'] += 1

                    # 按文件扩展名分类
                    ext = item.suffix.lower()
                    if ext not in analysis['files_by_type']:
                        analysis['files_by_type'][ext] = {'size': 0, 'count': 0, 'files': []}

                    analysis['files_by_type'][ext]['size'] += size
                    analysis['files_by_type'][ext]['count'] += 1

                    # 记录大文件（>1MB）
                    if size > 1 * 1024 * 1024:
                        try:
                            rel_path = str(item.relative_to(Path.cwd()))
                        except ValueError:
                            rel_path = str(item)

                        file_info = {
                            'name': item.name,
                            'size': size,
                            'path': rel_path,
                            'ext': ext
                        }
                        analysis['large_files'].append(file_info)
                        analysis['files_by_type'][ext]['files'].append(file_info)

                except (OSError, PermissionError):
                    continue

    except Exception as e:
        print(f"⚠️ 分析目录失败: {e}")

    return analysis

def print_analysis(analysis):
    """打印分析结果"""
    print(f"📁 data目录总览:")
    print(f"   总大小: {format_size(analysis['total_size'])}")
    print(f"   文件数: {analysis['file_count']} 个")

    # 按文件类型统计
    print(f"\n📊 按文件类型统计:")
    sorted_types = sorted(analysis['files_by_type'].items(),
                         key=lambda x: x[1]['size'], reverse=True)

    for ext, info in sorted_types:
        if info['size'] > 1024:  # 只显示>1KB的类型
            ext_name = ext if ext else '无扩展名'
            print(f"   📄 {ext_name}: {format_size(info['size'])} ({info['count']} 个文件)")

    # 显示最大的文件
    print(f"\n🔍 最大的文件 (前10个):")
    sorted_files = sorted(analysis['large_files'], key=lambda x: x['size'], reverse=True)
    for i, file_info in enumerate(sorted_files[:10], 1):
        print(f"   {i:2d}. {file_info['name']}: {format_size(file_info['size'])}")
        print(f"       路径: {file_info['path']}")

def main():
    """主函数"""
    print("📊 NK系统数据目录分析工具")
    print("=" * 60)
    
    data_path = Path('data')
    if not data_path.exists():
        print("❌ data目录不存在")
        return
    
    print("🔍 分析data目录...")
    analysis = analyze_directory(data_path)
    
    print("\n📋 分析结果:")
    print("=" * 60)
    print_analysis(analysis)
    
    # 分类统计
    print(f"\n📊 按用途分类:")
    print("=" * 60)

    categories = {
        '数据库文件': ['.db'],
        '模型文件': ['.pkl', '.h5', '.keras', '.model'],
        '数据集文件': ['.zip', '.rar', '.7z'],
        '配置文件': ['.json', '.txt', '.ini'],
        '日志文件': ['.log']
    }

    category_stats = {cat: {'size': 0, 'count': 0} for cat in categories}
    other_size = 0
    other_count = 0

    for ext, info in analysis['files_by_type'].items():
        categorized = False
        for category, extensions in categories.items():
            if ext in extensions:
                category_stats[category]['size'] += info['size']
                category_stats[category]['count'] += info['count']
                categorized = True
                break

        if not categorized:
            other_size += info['size']
            other_count += info['count']

    for category, stats in category_stats.items():
        if stats['count'] > 0:
            print(f"📂 {category}: {format_size(stats['size'])} ({stats['count']} 个文件)")

    if other_count > 0:
        print(f"📂 其他文件: {format_size(other_size)} ({other_count} 个文件)")
    
    # 打包建议
    print(f"\n💡 打包建议:")
    print("=" * 60)

    total_size = analysis['total_size']
    models_size = category_stats['模型文件']['size']
    datasets_size = category_stats['数据集文件']['size']
    db_size = category_stats['数据库文件']['size']

    exclude_size = models_size + datasets_size + db_size
    include_size = total_size - exclude_size

    print(f"📁 data目录总大小: {format_size(total_size)}")
    print(f"🚫 建议排除文件: {format_size(exclude_size)} ({exclude_size/total_size*100:.1f}%)")
    print(f"📦 建议包含文件: {format_size(include_size)} ({include_size/total_size*100:.1f}%)")

    print(f"\n🎯 推荐策略:")
    if exclude_size > total_size * 0.5:
        print("✅ 强烈推荐使用智能打包 - 可节省大量空间")
        print("💾 预计可减少包大小超过50%")
    elif exclude_size > total_size * 0.2:
        print("✅ 推荐使用智能打包 - 可节省一定空间")
        print("💾 预计可减少包大小20%以上")
    else:
        print("ℹ️ 可以使用普通打包，数据量不大")

    print(f"\n🚫 智能打包会排除:")
    print("- 训练模型文件 (*.pkl, *.h5, *.keras) - 用户训练的个人模型")
    print("- 数据集文件 (*.zip, *.rar) - 大型训练数据集")
    print("- 用户数据库 (*.db) - 包含患者隐私数据")
    print("- 备份文件 - 历史备份数据")
    print("- 原始脑电数据 - 患者个人数据")

    print(f"\n✅ 智能打包会包含:")
    print("- 配置文件模板 (user_config.json) - 系统配置模板")
    print("- 系统库文件 (libs/) - 运行必需的库")
    print("- 资源文件 (resources/) - 界面资源")
    print("- 文档文件 - 用户手册和说明")

    print(f"\n🎉 智能打包的优势:")
    print("- 🔒 保护用户隐私 - 不包含患者数据")
    print("- 💾 大幅减少包大小 - 便于分发")
    print("- 🚀 加快传输速度 - 网络友好")
    print("- 🧹 干净的发布版本 - 专业部署")

if __name__ == "__main__":
    main()
