#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QT风格电刺激设备测试
模拟QT程序的电流调节和刺激逻辑
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.stimulation_device import StimulationDevice, StimulationParameters

def setup_logging():
    """设置详细日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('qt_style_test.log', encoding='utf-8')
        ]
    )

def test_qt_style_stimulation():
    """测试QT风格的电刺激功能"""
    print("=" * 60)
    print("🔧 QT风格电刺激设备测试")
    print("模拟QT程序的电流调节和刺激逻辑")
    print("=" * 60)
    
    # 创建设备实例
    device = StimulationDevice()
    
    try:
        # 步骤1：连接设备（模拟Button_dianchiji_open）
        print("\n📡 步骤1: 连接设备（模拟Button_dianchiji_open）...")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功，设备已切换到循环刺激状态")
        
        # 步骤2：设置刺激参数
        print("\n⚙️ 步骤2: 设置刺激参数...")
        params = StimulationParameters(
            channel_num=1,      # A通道
            frequency=25.0,     # 25Hz
            pulse_width=250.0,  # 250μs
            relax_time=5.0,     # 5s
            climb_time=2.0,     # 2s
            work_time=10.0,     # 10s
            fall_time=2.0,      # 2s
            wave_type=0         # 单相波
        )
        
        if not device.set_stimulation_parameters(params):
            print("❌ 刺激参数设置失败")
            return False
        print("✅ 刺激参数设置成功")
        
        # 步骤3：模拟spinBox_dianchiji_A调节（自动触发3秒刺激）
        print("\n🎛️ 步骤3: 模拟spinBox_dianchiji_A调节电流...")
        print("   QT程序中调节spinBox会自动触发3秒刺激给患者反馈")
        
        current_values = [3.0, 5.0, 4.0]  # 模拟用户调节电流
        
        for i, current_ma in enumerate(current_values, 1):
            print(f"\n   调节{i}: 设置A通道电流为{current_ma}mA")
            
            # 使用新的自动刺激功能
            if device.set_current_with_auto_stimulation(1, current_ma):
                print(f"   ✅ 电流设置成功，自动刺激3秒")
                
                # 等待观察状态变化
                for j in range(5):
                    time.sleep(1)
                    a_status = device.get_channel_status(1)
                    status_text = device._get_channel_status_text(a_status)
                    print(f"   [{j+1}s] A通道状态: {status_text}")
                    
                    if a_status == 3:  # 正常工作状态
                        print("   ✅ 检测到正在刺激")
                    elif a_status == 0:  # 停止状态
                        print("   ✅ 刺激已自动停止")
                        break
            else:
                print(f"   ❌ 电流设置失败")
            
            print("   等待2秒后进行下次调节...")
            time.sleep(2)
        
        # 步骤4：模拟Button_dianchiji_shart按钮（持续刺激）
        print("\n🚀 步骤4: 模拟Button_dianchiji_shart按钮...")
        print("   按照spinBox_dianchiji_A中的电流值进行持续刺激")
        
        final_current = 6.0
        print(f"   设置A通道电流为{final_current}mA")
        if device.set_current(1, final_current):
            print("   ✅ 电流设置成功")
            
            print("   启动持续刺激...")
            if device.start_stimulation(1):
                print("   ✅ 持续刺激启动成功")
                
                # 监控刺激状态10秒
                print("   监控刺激状态10秒...")
                stimulation_detected = False
                for i in range(10):
                    time.sleep(1)
                    a_status = device.get_channel_status(1)
                    status_text = device._get_channel_status_text(a_status)
                    print(f"   [{i+1:2d}s] A通道状态: {status_text}")
                    
                    if a_status == 3:  # 正常工作状态
                        stimulation_detected = True
                
                # 手动停止刺激
                print("   手动停止刺激...")
                if device.stop_stimulation(1):
                    print("   ✅ 刺激停止成功")
                else:
                    print("   ❌ 刺激停止失败")
                
                # 验证结果
                if stimulation_detected:
                    print("\n🎉 测试成功！")
                    print("   ✅ 回调函数正常工作")
                    print("   ✅ 状态监控正常")
                    print("   ✅ 电流设置和刺激控制正常")
                    return True
                else:
                    print("\n⚠️ 测试部分成功")
                    print("   ✅ 命令执行成功")
                    print("   ❌ 未检测到正常工作状态")
                    return False
            else:
                print("   ❌ 持续刺激启动失败")
                return False
        else:
            print("   ❌ 最终电流设置失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        device.disconnect()
        print("✅ 资源清理完成")

def main():
    """主函数"""
    setup_logging()
    
    print("🔧 NK电刺激设备QT风格测试")
    print("解决'界面显示刺激中但实际无电流输出'问题")
    
    # 运行测试
    success = test_qt_style_stimulation()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ QT风格测试成功！")
        print("🔍 主要改进：")
        print("   - 修复了0字节数据包的警告问题")
        print("   - 简化了电流设置流程（参考QT程序）")
        print("   - 添加了自动3秒刺激功能")
        print("   - 实现了QT程序的电流调节逻辑")
        print("📄 详细日志：qt_style_test.log")
    else:
        print("❌ QT风格测试失败！")
        print("🔍 可能的问题：")
        print("   - 设备连接问题")
        print("   - DLL函数调用问题")
        print("   - 通道状态切换问题")
        print("📄 错误日志：qt_style_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
