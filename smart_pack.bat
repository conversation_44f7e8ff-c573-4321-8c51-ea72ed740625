@echo off
cd /d "%~dp0"
title NK System Smart Build

echo ========================================
echo    NK Brain-Computer Interface System
echo    Smart Build Tool
echo ========================================
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ and add to PATH
    pause
    exit /b 1
)

echo Python environment OK
echo.

echo Starting smart build...
echo This will only pack necessary files and exclude user data
echo.

python smart_build.py

if errorlevel 1 (
    echo.
    echo Build failed!
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Check the dist/ folder for the built application
echo.

set /p open_folder=Open build folder? (y/n): 
if /i "%open_folder%"=="y" (
    if exist "dist" (
        explorer dist
    ) else (
        echo Build folder not found
    )
)

pause
