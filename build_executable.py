#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NK脑机接口系统打包脚本
使用PyInstaller将Python项目打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目根目录
project_root = Path.cwd()

# 数据文件列表
datas = [
    # 资源文件
    ('resources', 'resources'),
    # 库文件
    ('libs', 'libs'),
    # 配置文件模板
    ('data/user_config.json', 'data'),
    # 文档文件
    ('docs', 'docs'),
    # 密码文件
    ('密码.txt', '.'),
]

# 隐藏导入模块
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtCharts',
    'PySide6.QtOpenGL',
    'numpy',
    'matplotlib',
    'matplotlib.backends.backend_qt5agg',
    'serial',
    'scipy',
    'scipy.signal',
    'scipy.stats',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.svm',
    'sklearn.metrics',
    'joblib',
    'pandas',
    'sqlite3',
    'ctypes',
    'threading',
    'multiprocessing',
    'queue',
    'logging',
    'json',
    'pathlib',
    'datetime',
    'hashlib',
    'tempfile',
    'shutil',
    'subprocess',
    'psutil',
    'tqdm',
    'pyttsx3',
]

# 排除的模块
excludes = [
    'tkinter',
    'test',
    'unittest',
    'pdb',
    'doctest',
    'difflib',
    'inspect',
    'pydoc',
]

# 二进制文件
binaries = []

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 去除重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='NK脑机接口康复训练系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/images/app_icon.ico' if Path('resources/images/app_icon.ico').exists() else None,
)

# 收集所有文件
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='NK脑机接口康复训练系统',
)
'''
    
    with open('NK_System.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller配置文件已创建: NK_System.spec")

def prepare_build_environment():
    """准备构建环境"""
    print("🔧 准备构建环境...")
    
    # 创建必要的目录
    dirs_to_create = [
        'resources/images',
        'resources/styles', 
        'libs',
        'data',
        'docs'
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # 创建应用图标（如果不存在）
    icon_path = Path('resources/images/app_icon.ico')
    if not icon_path.exists():
        print("⚠️ 应用图标不存在，将使用默认图标")
    
    # 检查关键文件
    critical_files = [
        'main.py',
        'data/user_config.json',
        'libs/RecoveryDLL.dll'
    ]
    
    missing_files = []
    for file_path in critical_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️ 以下关键文件缺失:")
        for file in missing_files:
            print(f"   - {file}")
        print("构建可能会失败，请检查文件完整性")
    
    print("✅ 构建环境准备完成")

def build_executable():
    """构建可执行文件"""
    print("🚀 开始构建可执行文件...")
    
    try:
        # 清理之前的构建
        if Path('dist').exists():
            shutil.rmtree('dist')
            print("🧹 清理旧的构建文件")
        
        if Path('build').exists():
            shutil.rmtree('build')
        
        # 运行PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'NK_System.spec'
        ]
        
        print("执行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 可执行文件构建成功!")
            return True
        else:
            print("❌ 构建失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def post_build_setup():
    """构建后设置"""
    print("🔧 执行构建后设置...")
    
    dist_path = Path('dist/NK脑机接口康复训练系统')
    
    if not dist_path.exists():
        print("❌ 构建目录不存在")
        return False
    
    try:
        # 创建数据目录结构
        data_dirs = [
            'data/backup',
            'data/raw_eeg_data',
            'data/models',
            'logs',
            'reports'
        ]
        
        for dir_path in data_dirs:
            (dist_path / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 复制重要文件
        important_files = [
            ('README.md', 'README.md'),
            ('密码.txt', '密码.txt'),
            ('全新机器部署指南.md', '全新机器部署指南.md'),
        ]
        
        for src, dst in important_files:
            if Path(src).exists():
                shutil.copy2(src, dist_path / dst)
        
        # 创建启动脚本
        startup_script = '''@echo off
chcp 65001 > nul
title NK脑机接口康复训练系统

echo ========================================
echo    NK脑机接口康复训练系统
echo    山东海天智能工程有限公司
echo ========================================
echo.

echo 正在启动系统...
"NK脑机接口康复训练系统.exe"

if errorlevel 1 (
    echo.
    echo 系统启动失败，请检查：
    echo 1. 是否有足够的系统权限
    echo 2. 是否有杀毒软件阻止运行
    echo 3. 系统文件是否完整
    echo.
    pause
)
'''
        
        with open(dist_path / '启动系统.bat', 'w', encoding='gbk') as f:
            f.write(startup_script)
        
        # 创建用户手册
        user_manual = '''# NK脑机接口康复训练系统 - 用户手册

## 系统要求
- Windows 10/11 (64位)
- 内存: 4GB以上
- 硬盘空间: 2GB以上

## 安装说明
1. 解压所有文件到目标目录
2. 双击"启动系统.bat"启动系统
3. 首次启动使用默认账户: admin / admin123

## 重要文件说明
- NK脑机接口康复训练系统.exe: 主程序
- data/: 数据库和配置文件目录
- libs/: 系统库文件目录
- logs/: 日志文件目录

## 技术支持
如遇问题请联系技术支持。

## 厂家配置密码
详见"密码.txt"文件
'''
        
        with open(dist_path / '用户手册.md', 'w', encoding='utf-8') as f:
            f.write(user_manual)
        
        print("✅ 构建后设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 构建后设置失败: {e}")
        return False

def create_installer():
    """创建安装包"""
    print("📦 创建安装包...")
    
    dist_path = Path('dist/NK脑机接口康复训练系统')
    
    if not dist_path.exists():
        print("❌ 构建目录不存在，无法创建安装包")
        return False
    
    try:
        # 创建压缩包
        import zipfile
        
        zip_name = f"NK脑机接口康复训练系统_v1.0.0_{Path.cwd().name}.zip"
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(dist_path):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(dist_path)
                    zipf.write(file_path, arc_name)
        
        print(f"✅ 安装包创建成功: {zip_name}")
        print(f"📁 文件大小: {Path(zip_name).stat().st_size / 1024 / 1024:.1f} MB")
        return True
        
    except Exception as e:
        print(f"❌ 创建安装包失败: {e}")
        return False

def main():
    """主函数"""
    print("🏗️ NK脑机接口系统打包工具")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 准备构建环境
    prepare_build_environment()
    
    # 创建配置文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 构建后设置
    if not post_build_setup():
        return False
    
    # 创建安装包
    if not create_installer():
        return False
    
    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print("=" * 60)
    print("📁 可执行文件位置: dist/NK脑机接口康复训练系统/")
    print("📦 安装包已创建")
    print("🚀 可以部署到没有Python环境的机器上运行")
    print("🔒 源代码已被保护，无法直接查看")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 打包失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中发生异常: {e}")
        sys.exit(1)
