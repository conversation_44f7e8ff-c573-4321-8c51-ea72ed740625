#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复孤立引用脚本
修复患者表中引用已删除医院id=1的记录
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def check_orphaned_references():
    """检查孤立的引用"""
    print("🔍 检查孤立的外键引用...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 检查患者表中引用不存在医院的记录
        orphaned_patients = db.execute_query("""
            SELECT b.id, b.xingming, b.yiyuanid
            FROM bingren b
            LEFT JOIN yiyuan y ON b.yiyuanid = y.id
            WHERE y.id IS NULL AND b.yiyuanid IS NOT NULL
        """)
        
        if orphaned_patients:
            print(f"📋 发现 {len(orphaned_patients)} 个患者记录引用了不存在的医院:")
            print("-" * 80)
            print(f"{'患者ID':<8} {'姓名':<15} {'引用医院ID':<10} {'状态':<15}")
            print("-" * 80)
            
            for patient in orphaned_patients:
                print(f"{patient['id']:<8} {patient['xingming']:<15} {patient['yiyuanid']:<10} 孤立引用")
        else:
            print("✅ 没有发现孤立的患者引用")
        
        return orphaned_patients
        
    except Exception as e:
        print(f"❌ 检查孤立引用失败: {e}")
        return None

def fix_orphaned_references(orphaned_patients):
    """修复孤立的引用"""
    print(f"\n🛠️ 修复 {len(orphaned_patients)} 个孤立引用...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 获取有效的医院ID（您的实际医院）
        valid_hospitals = db.execute_query("SELECT id, hname FROM yiyuan ORDER BY id")
        
        if not valid_hospitals:
            print("❌ 没有找到有效的医院记录")
            return False
        
        print("📋 可用的医院:")
        for hospital in valid_hospitals:
            print(f"   ID: {hospital['id']}, 名称: {hospital['hname']}")
        
        # 使用第一个有效医院（通常是您的实际医院id=3）
        target_hospital_id = valid_hospitals[0]['id']
        target_hospital_name = valid_hospitals[0]['hname']
        
        print(f"\n🎯 将孤立引用更新为医院: {target_hospital_name} (ID: {target_hospital_id})")
        
        # 临时禁用外键约束进行修复
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已临时禁用外键约束")
            
            # 更新孤立的患者记录
            updated_count = 0
            for patient in orphaned_patients:
                try:
                    cursor.execute(
                        "UPDATE bingren SET yiyuanid = ? WHERE id = ?",
                        (target_hospital_id, patient['id'])
                    )
                    updated_count += 1
                    print(f"   ✅ 更新患者 {patient['xingming']} (ID: {patient['id']})")
                except Exception as e:
                    print(f"   ❌ 更新患者 {patient['xingming']} 失败: {e}")
            
            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 提交事务
            conn.commit()
            print(f"✅ 成功更新 {updated_count} 个患者记录")
            
            return updated_count > 0
            
    except Exception as e:
        print(f"❌ 修复孤立引用失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 再次检查外键约束
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"❌ 仍有 {len(violations)} 个外键约束违规:")
                for violation in violations:
                    print(f"   {violation}")
                return False
            else:
                print("✅ 外键约束检查通过")
        
        # 检查患者-医院关联
        patient_hospital_stats = db.execute_query("""
            SELECT y.hname, COUNT(b.id) as patient_count
            FROM yiyuan y
            LEFT JOIN bingren b ON y.id = b.yiyuanid
            GROUP BY y.id, y.hname
            ORDER BY y.id
        """)
        
        print("\n📊 患者-医院关联统计:")
        print("-" * 40)
        print(f"{'医院名称':<20} {'患者数量':<10}")
        print("-" * 40)
        
        for stat in patient_hospital_stats:
            print(f"{stat['hname']:<20} {stat['patient_count']:<10}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证修复结果失败: {e}")
        return False

def cleanup_test_data():
    """清理其他可能的测试数据"""
    print("\n🧹 清理其他测试数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查是否有测试患者数据
        test_patients = db.execute_query("""
            SELECT * FROM bingren 
            WHERE xingming LIKE '%测试%' OR xingming LIKE '%test%' OR xingming = '张三'
        """)
        
        if test_patients:
            print(f"📋 发现 {len(test_patients)} 个可能的测试患者:")
            for patient in test_patients:
                print(f"   患者: {patient['xingming']} (ID: {patient['id']})")
            
            print("\nℹ️ 这些患者记录将保留，如需删除请手动处理")
        else:
            print("✅ 没有发现明显的测试患者数据")
        
        # 检查是否有测试治疗记录
        test_treatments = db.execute_query("""
            SELECT COUNT(*) as count FROM zhiliao 
            WHERE treat_number LIKE '%test%' OR treat_number LIKE '%测试%'
        """)
        
        if test_treatments and test_treatments[0]['count'] > 0:
            print(f"📋 发现 {test_treatments[0]['count']} 个可能的测试治疗记录")
            print("ℹ️ 这些治疗记录将保留，如需删除请手动处理")
        else:
            print("✅ 没有发现明显的测试治疗记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理测试数据检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 修复孤立外键引用工具")
    print("=" * 80)
    
    # 1. 检查孤立引用
    orphaned_patients = check_orphaned_references()
    if orphaned_patients is None:
        print("❌ 检查孤立引用失败，退出")
        return False
    
    if not orphaned_patients:
        print("✅ 没有发现孤立引用，无需修复")
    else:
        # 2. 修复孤立引用
        if not fix_orphaned_references(orphaned_patients):
            print("❌ 修复孤立引用失败，退出")
            return False
    
    # 3. 验证修复结果
    if not verify_fix():
        print("❌ 修复验证失败")
        return False
    
    # 4. 清理其他测试数据
    if not cleanup_test_data():
        print("❌ 测试数据清理检查失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 数据库修复完成！")
    print("✅ 孤立的外键引用已修复")
    print("✅ 数据库完整性检查通过")
    print("✅ 治疗数据上传平台冲突问题已彻底解决")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
