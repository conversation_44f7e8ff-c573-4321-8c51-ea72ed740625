#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理所有测试数据工具
为全新机器部署准备干净的数据库
"""

import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def backup_current_database():
    """备份当前数据库"""
    print("💾 备份当前数据库...")
    print("=" * 50)
    
    try:
        db_config = AppConfig.get_config('database')
        db_path = Path(db_config['path'])
        backup_path = Path(db_config['backup_path'])
        
        if not db_path.exists():
            print("ℹ️ 数据库文件不存在，无需备份")
            return True
        
        # 创建备份目录
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = backup_path / f"before_cleanup_{timestamp}.db"
        
        # 复制数据库文件
        shutil.copy2(db_path, backup_file)
        
        print(f"✅ 数据库已备份到: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ 备份数据库失败: {e}")
        return False

def identify_test_data():
    """识别测试数据"""
    print("\n🔍 识别测试数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return None
        
        test_data_summary = {
            'test_hospitals': [],
            'test_patients': [],
            'test_treatments': [],
            'test_eeg_data': []
        }
        
        # 1. 识别测试医院
        test_hospitals = db.execute_query("""
            SELECT * FROM yiyuan 
            WHERE hname LIKE '%测试%' OR hname LIKE '%test%' OR hname = '默认医院'
        """)
        test_data_summary['test_hospitals'] = test_hospitals
        
        # 2. 识别测试患者
        test_patients = db.execute_query("""
            SELECT * FROM bingren 
            WHERE name LIKE '%测试%' OR name LIKE '%test%' OR name = '张三' 
               OR bianhao IN (8888, 9999, 1111, 2222, 3333)
        """)
        test_data_summary['test_patients'] = test_patients
        
        # 3. 识别测试治疗记录
        test_treatments = db.execute_query("""
            SELECT * FROM zhiliao 
            WHERE treat_number LIKE '%test%' OR treat_number LIKE '%测试%'
               OR patient_number IN (8888, 9999, 1111, 2222, 3333)
        """)
        test_data_summary['test_treatments'] = test_treatments
        
        # 4. 识别测试脑电数据
        test_eeg_sessions = db.execute_query("""
            SELECT COUNT(*) as count FROM eeg_sessions 
            WHERE patient_id IN (8888, 9999, 1111, 2222, 3333)
        """)
        
        test_eeg_raw = db.execute_query("""
            SELECT COUNT(*) as count FROM eeg_raw_data 
            WHERE patient_id IN (8888, 9999, 1111, 2222, 3333)
        """)
        
        test_data_summary['test_eeg_data'] = {
            'sessions': test_eeg_sessions[0]['count'] if test_eeg_sessions else 0,
            'raw_data': test_eeg_raw[0]['count'] if test_eeg_raw else 0
        }
        
        # 显示识别结果
        print(f"📋 测试数据识别结果:")
        print(f"   测试医院: {len(test_data_summary['test_hospitals'])} 个")
        print(f"   测试患者: {len(test_data_summary['test_patients'])} 个")
        print(f"   测试治疗记录: {len(test_data_summary['test_treatments'])} 个")
        print(f"   测试脑电会话: {test_data_summary['test_eeg_data']['sessions']} 个")
        print(f"   测试脑电原始数据: {test_data_summary['test_eeg_data']['raw_data']} 个")
        
        return test_data_summary
        
    except Exception as e:
        print(f"❌ 识别测试数据失败: {e}")
        return None

def clean_test_data(test_data_summary):
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")
            print("✅ 已禁用外键约束")
            
            cleaned_count = {
                'hospitals': 0,
                'patients': 0,
                'treatments': 0,
                'eeg_sessions': 0,
                'eeg_raw_data': 0
            }
            
            # 1. 清理测试脑电数据
            print("\n📋 清理测试脑电数据...")
            test_patient_ids = [8888, 9999, 1111, 2222, 3333]
            
            for patient_id in test_patient_ids:
                # 删除脑电会话
                cursor.execute("DELETE FROM eeg_sessions WHERE patient_id = ?", (patient_id,))
                cleaned_count['eeg_sessions'] += cursor.rowcount
                
                # 删除脑电原始数据
                cursor.execute("DELETE FROM eeg_raw_data WHERE patient_id = ?", (patient_id,))
                cleaned_count['eeg_raw_data'] += cursor.rowcount
            
            # 2. 清理测试治疗记录
            print("📋 清理测试治疗记录...")
            cursor.execute("""
                DELETE FROM zhiliao 
                WHERE treat_number LIKE '%test%' OR treat_number LIKE '%测试%'
                   OR patient_number IN (8888, 9999, 1111, 2222, 3333)
            """)
            cleaned_count['treatments'] = cursor.rowcount
            
            # 3. 清理测试患者
            print("📋 清理测试患者...")
            cursor.execute("""
                DELETE FROM bingren 
                WHERE name LIKE '%测试%' OR name LIKE '%test%' OR name = '张三'
                   OR bianhao IN (8888, 9999, 1111, 2222, 3333)
            """)
            cleaned_count['patients'] = cursor.rowcount
            
            # 4. 清理测试医院（保留用户的实际医院）
            print("📋 清理测试医院...")
            # 只删除明确的测试医院，保留用户数据
            cursor.execute("""
                DELETE FROM yiyuan 
                WHERE hname LIKE '%测试%' OR hname LIKE '%test%' 
                   OR (hname = '默认医院' AND shebeiid = 'NK001')
            """)
            cleaned_count['hospitals'] = cursor.rowcount
            
            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            print("✅ 已重新启用外键约束")
            
            # 提交事务
            conn.commit()
            print("✅ 事务已提交")
            
            # 显示清理结果
            print(f"\n📊 清理结果:")
            print(f"   删除测试医院: {cleaned_count['hospitals']} 个")
            print(f"   删除测试患者: {cleaned_count['patients']} 个")
            print(f"   删除测试治疗记录: {cleaned_count['treatments']} 个")
            print(f"   删除测试脑电会话: {cleaned_count['eeg_sessions']} 个")
            print(f"   删除测试脑电原始数据: {cleaned_count['eeg_raw_data']} 个")
            
            return cleaned_count
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return None

def verify_cleanup():
    """验证清理结果"""
    print("\n🔍 验证清理结果...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 检查外键约束
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                print(f"❌ 发现 {len(violations)} 个外键约束违规")
                return False
            else:
                print("✅ 外键约束检查通过")
        
        # 统计剩余数据
        print("\n📊 剩余数据统计:")
        tables = ['yiyuan', 'bingren', 'zhiliao', 'eeg_sessions', 'eeg_raw_data']
        
        for table in tables:
            try:
                count_result = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                count = count_result[0]['count'] if count_result else 0
                print(f"   {table}: {count} 条记录")
            except Exception as e:
                print(f"   {table}: 查询失败 - {e}")
        
        # 检查剩余医院信息
        hospitals = db.execute_query("SELECT id, hname, keshi, shebeiid FROM yiyuan")
        if hospitals:
            print(f"\n📋 剩余医院信息:")
            for hospital in hospitals:
                print(f"   ID: {hospital['id']}, 名称: {hospital['hname']}, 科室: {hospital['keshi']}, 设备ID: {hospital['shebeiid']}")
        else:
            print("\n⚠️ 警告: 没有医院信息，系统可能需要重新配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证清理结果失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 测试数据清理工具")
    print("=" * 80)
    print("此工具将清理所有测试数据，为全新机器部署准备干净的数据库")
    print("=" * 80)
    
    # 1. 备份当前数据库
    if not backup_current_database():
        print("❌ 备份失败，为安全起见停止清理")
        return False
    
    # 2. 识别测试数据
    test_data_summary = identify_test_data()
    if test_data_summary is None:
        print("❌ 识别测试数据失败，退出")
        return False
    
    # 3. 确认清理
    total_test_items = (
        len(test_data_summary['test_hospitals']) +
        len(test_data_summary['test_patients']) +
        len(test_data_summary['test_treatments']) +
        test_data_summary['test_eeg_data']['sessions'] +
        test_data_summary['test_eeg_data']['raw_data']
    )
    
    if total_test_items == 0:
        print("✅ 没有发现测试数据，数据库已经很干净")
        return True
    
    print(f"\n⚠️ 即将删除 {total_test_items} 项测试数据")
    print("此操作不可逆，请确认已备份重要数据")
    
    # 4. 清理测试数据
    cleaned_count = clean_test_data(test_data_summary)
    if cleaned_count is None:
        print("❌ 清理测试数据失败，退出")
        return False
    
    # 5. 验证清理结果
    if not verify_cleanup():
        print("❌ 清理验证失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 测试数据清理完成！")
    print("✅ 数据库已准备好用于全新机器部署")
    print("✅ 所有测试数据已清理")
    print("✅ 数据库完整性检查通过")
    print("\n📋 下一步:")
    print("   1. 将整个系统目录复制到新机器")
    print("   2. 在新机器上直接启动系统")
    print("   3. 系统将自动初始化并创建默认数据")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
