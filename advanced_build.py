#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级打包脚本 - 支持多种打包选项和优化
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
from datetime import datetime

class AdvancedBuilder:
    """高级构建器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.build_config = {
            'app_name': 'NK脑机接口康复训练系统',
            'version': '1.0.0',
            'company': '山东海天智能工程有限公司',
            'description': '基于脑机接口技术的康复训练系统',
            'copyright': f'Copyright © {datetime.now().year} 山东海天智能工程有限公司',
        }
    
    def check_dependencies(self):
        """检查打包依赖"""
        print("🔍 检查打包依赖...")
        
        required_packages = [
            ('PyInstaller', 'pyinstaller'),
            ('auto-py-to-exe', 'auto-py-to-exe'),  # 可选的GUI工具
        ]
        
        missing_packages = []
        
        for package_name, pip_name in required_packages:
            try:
                __import__(package_name.lower().replace('-', '_'))
                print(f"✅ {package_name} 已安装")
            except ImportError:
                missing_packages.append((package_name, pip_name))
        
        if missing_packages:
            print("📦 安装缺失的打包工具...")
            for package_name, pip_name in missing_packages:
                try:
                    subprocess.check_call([
                        sys.executable, '-m', 'pip', 'install', pip_name
                    ])
                    print(f"✅ {package_name} 安装成功")
                except subprocess.CalledProcessError:
                    print(f"❌ {package_name} 安装失败")
                    return False
        
        return True
    
    def create_app_icon(self):
        """创建应用图标"""
        icon_path = self.project_root / 'resources' / 'images' / 'app_icon.ico'
        
        if icon_path.exists():
            print(f"✅ 应用图标已存在: {icon_path}")
            return str(icon_path)
        
        print("🎨 创建默认应用图标...")
        
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建256x256的图标
            size = 256
            img = Image.new('RGBA', (size, size), (0, 100, 200, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制简单的脑电波图案
            center_x, center_y = size // 2, size // 2
            
            # 绘制圆形背景
            draw.ellipse([20, 20, size-20, size-20], fill=(255, 255, 255, 200))
            
            # 绘制脑电波
            wave_points = []
            for i in range(0, size-40, 4):
                x = 40 + i
                y = center_y + 30 * (0.5 if i % 20 < 10 else -0.5)
                wave_points.append((x, y))
            
            if len(wave_points) > 1:
                draw.line(wave_points, fill=(0, 100, 200, 255), width=4)
            
            # 添加文字
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            draw.text((center_x, center_y + 60), "NK", fill=(0, 100, 200, 255), 
                     font=font, anchor="mm")
            
            # 保存图标
            icon_path.parent.mkdir(parents=True, exist_ok=True)
            img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            
            print(f"✅ 默认图标创建成功: {icon_path}")
            return str(icon_path)
            
        except ImportError:
            print("⚠️ PIL库未安装，无法创建图标，将使用默认图标")
            return None
        except Exception as e:
            print(f"⚠️ 创建图标失败: {e}")
            return None
    
    def create_version_info(self):
        """创建版本信息文件"""
        version_info = f'''# UTF-8
#
# 版本信息文件
#
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'{self.build_config["company"]}'),
            StringStruct(u'FileDescription', u'{self.build_config["description"]}'),
            StringStruct(u'FileVersion', u'{self.build_config["version"]}'),
            StringStruct(u'InternalName', u'{self.build_config["app_name"]}'),
            StringStruct(u'LegalCopyright', u'{self.build_config["copyright"]}'),
            StringStruct(u'OriginalFilename', u'{self.build_config["app_name"]}.exe'),
            StringStruct(u'ProductName', u'{self.build_config["app_name"]}'),
            StringStruct(u'ProductVersion', u'{self.build_config["version"]}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
        
        version_file = self.project_root / 'version_info.txt'
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print(f"✅ 版本信息文件已创建: {version_file}")
        return str(version_file)
    
    def build_onefile(self):
        """构建单文件版本"""
        print("🚀 构建单文件版本...")
        
        icon_path = self.create_app_icon()
        version_file = self.create_version_info()
        
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--windowed',
            '--clean',
            '--noconfirm',
            f'--name={self.build_config["app_name"]}_单文件版',
            '--add-data=resources;resources',
            '--add-data=libs;libs',
            '--add-data=data/user_config.json;data',
            '--add-data=密码.txt;.',
            '--hidden-import=PySide6.QtCore',
            '--hidden-import=PySide6.QtGui',
            '--hidden-import=PySide6.QtWidgets',
            '--hidden-import=numpy',
            '--hidden-import=matplotlib',
            '--hidden-import=serial',
            '--hidden-import=scipy',
            '--hidden-import=sklearn',
            '--exclude-module=tkinter',
            '--exclude-module=test',
            '--exclude-module=unittest',
        ]
        
        if icon_path:
            cmd.append(f'--icon={icon_path}')
        
        if version_file:
            cmd.append(f'--version-file={version_file}')
        
        cmd.append('main.py')
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 单文件版本构建成功!")
                return True
            else:
                print("❌ 单文件版本构建失败:")
                print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 构建过程中发生错误: {e}")
            return False
    
    def build_onedir(self):
        """构建目录版本"""
        print("🚀 构建目录版本...")
        
        icon_path = self.create_app_icon()
        version_file = self.create_version_info()
        
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onedir',
            '--windowed',
            '--clean',
            '--noconfirm',
            f'--name={self.build_config["app_name"]}',
            '--add-data=resources;resources',
            '--add-data=libs;libs',
            '--add-data=data/user_config.json;data',
            '--add-data=docs;docs',
            '--add-data=密码.txt;.',
            '--hidden-import=PySide6.QtCore',
            '--hidden-import=PySide6.QtGui',
            '--hidden-import=PySide6.QtWidgets',
            '--hidden-import=PySide6.QtCharts',
            '--hidden-import=numpy',
            '--hidden-import=matplotlib',
            '--hidden-import=matplotlib.backends.backend_qt5agg',
            '--hidden-import=serial',
            '--hidden-import=scipy',
            '--hidden-import=scipy.signal',
            '--hidden-import=sklearn',
            '--hidden-import=joblib',
            '--hidden-import=pandas',
            '--hidden-import=sqlite3',
            '--exclude-module=tkinter',
            '--exclude-module=test',
            '--exclude-module=unittest',
            '--exclude-module=pdb',
        ]
        
        if icon_path:
            cmd.append(f'--icon={icon_path}')
        
        if version_file:
            cmd.append(f'--version-file={version_file}')
        
        cmd.append('main.py')
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 目录版本构建成功!")
                return True
            else:
                print("❌ 目录版本构建失败:")
                print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 构建过程中发生错误: {e}")
            return False
    
    def optimize_build(self):
        """优化构建结果"""
        print("⚡ 优化构建结果...")
        
        dist_path = self.project_root / 'dist' / self.build_config["app_name"]
        
        if not dist_path.exists():
            print("❌ 构建目录不存在")
            return False
        
        try:
            # 创建必要的目录结构
            dirs_to_create = [
                'data/backup',
                'data/raw_eeg_data',
                'data/models',
                'logs',
                'reports'
            ]
            
            for dir_name in dirs_to_create:
                (dist_path / dir_name).mkdir(parents=True, exist_ok=True)
            
            # 复制重要文档
            docs_to_copy = [
                'README.md',
                '全新机器部署指南.md',
                '用户手册.md'
            ]
            
            for doc in docs_to_copy:
                if (self.project_root / doc).exists():
                    shutil.copy2(self.project_root / doc, dist_path / doc)
            
            # 创建启动脚本
            self.create_startup_scripts(dist_path)
            
            print("✅ 构建结果优化完成")
            return True
            
        except Exception as e:
            print(f"❌ 优化构建结果失败: {e}")
            return False
    
    def create_startup_scripts(self, dist_path):
        """创建启动脚本"""
        # Windows启动脚本
        startup_bat = f'''@echo off
chcp 65001 > nul
title {self.build_config["app_name"]}

echo ========================================
echo    {self.build_config["app_name"]}
echo    {self.build_config["company"]}
echo    版本: {self.build_config["version"]}
echo ========================================
echo.

echo 正在启动系统...
echo 请稍候...
echo.

"{self.build_config["app_name"]}.exe"

if errorlevel 1 (
    echo.
    echo ========================================
    echo 系统启动失败，可能的原因：
    echo ========================================
    echo 1. 系统权限不足
    echo 2. 杀毒软件阻止运行
    echo 3. 系统文件损坏
    echo 4. 硬件设备未连接
    echo.
    echo 解决方案：
    echo 1. 以管理员身份运行
    echo 2. 添加到杀毒软件白名单
    echo 3. 重新解压安装包
    echo 4. 检查设备连接
    echo ========================================
    echo.
    pause
)
'''
        
        with open(dist_path / '启动系统.bat', 'w', encoding='gbk') as f:
            f.write(startup_bat)
        
        # 创建安装说明
        install_guide = f'''# {self.build_config["app_name"]} - 安装说明

## 系统要求
- Windows 10/11 (64位)
- 内存: 4GB以上
- 硬盘空间: 2GB以上
- 管理员权限

## 安装步骤
1. 解压所有文件到目标目录（建议：C:\\NK_System\\）
2. 右键"启动系统.bat" → "以管理员身份运行"
3. 首次启动使用默认账户登录：
   - 用户名: admin
   - 密码: admin123

## 重要文件说明
- {self.build_config["app_name"]}.exe: 主程序
- 启动系统.bat: 启动脚本（推荐使用）
- data/: 数据库和配置文件
- libs/: 系统库文件
- logs/: 运行日志
- 密码.txt: 厂家配置密码

## 注意事项
1. 首次运行需要管理员权限
2. 请添加到杀毒软件白名单
3. 不要删除任何系统文件
4. 定期备份data目录

## 技术支持
{self.build_config["company"]}
版本: {self.build_config["version"]}
'''
        
        with open(dist_path / '安装说明.md', 'w', encoding='utf-8') as f:
            f.write(install_guide)
    
    def create_installer_package(self):
        """创建安装包"""
        print("📦 创建安装包...")
        
        try:
            import zipfile
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            zip_name = f"{self.build_config['app_name']}_v{self.build_config['version']}_{timestamp}.zip"
            
            dist_path = self.project_root / 'dist' / self.build_config["app_name"]
            
            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
                for root, dirs, files in os.walk(dist_path):
                    for file in files:
                        file_path = Path(root) / file
                        arc_name = file_path.relative_to(dist_path)
                        zipf.write(file_path, arc_name)
            
            file_size = Path(zip_name).stat().st_size / 1024 / 1024
            print(f"✅ 安装包创建成功: {zip_name}")
            print(f"📁 文件大小: {file_size:.1f} MB")
            
            return zip_name
            
        except Exception as e:
            print(f"❌ 创建安装包失败: {e}")
            return None

def main():
    """主函数"""
    print("🏗️ NK脑机接口系统高级打包工具")
    print("=" * 80)
    
    builder = AdvancedBuilder()
    
    # 检查依赖
    if not builder.check_dependencies():
        return False
    
    print("\n请选择打包方式:")
    print("1. 单文件版本 (启动较慢，但只有一个exe文件)")
    print("2. 目录版本 (启动较快，推荐)")
    print("3. 两种版本都构建")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    success = False
    
    if choice == "1":
        success = builder.build_onefile()
    elif choice == "2":
        success = builder.build_onedir()
        if success:
            success = builder.optimize_build()
    elif choice == "3":
        success1 = builder.build_onefile()
        success2 = builder.build_onedir()
        if success2:
            success2 = builder.optimize_build()
        success = success1 or success2
    else:
        print("❌ 无效选择")
        return False
    
    if success:
        # 创建安装包
        installer = builder.create_installer_package()
        
        print("\n" + "=" * 80)
        print("🎉 打包完成!")
        print("=" * 80)
        print("📁 构建文件位置: dist/")
        if installer:
            print(f"📦 安装包: {installer}")
        print("🚀 可以部署到没有Python环境的机器")
        print("🔒 源代码已被完全保护")
        print("💼 适合商业发布")
        print("=" * 80)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 打包失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中发生异常: {e}")
        sys.exit(1)
