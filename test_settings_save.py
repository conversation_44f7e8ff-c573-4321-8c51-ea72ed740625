#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置保存功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def test_database_hospital_info():
    """测试数据库医院信息操作"""
    print("=" * 60)
    print("数据库医院信息操作测试")
    print("=" * 60)
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 初始化数据库
        if not db_manager.initialize():
            print("✗ 数据库初始化失败")
            return False
        
        print("✓ 数据库初始化成功")
        
        # 获取当前医院信息
        current_info = db_manager.get_hospital_info()
        print(f"当前医院信息: {current_info}")
        
        # 测试更新医院信息
        test_hospital_info = {
            'hname': '测试医院',
            'keshi': '测试科室',
            'shebeiid': 'TEST001'
        }
        
        print(f"更新医院信息为: {test_hospital_info}")
        
        if db_manager.update_hospital_info(test_hospital_info):
            print("✓ 医院信息更新成功")
        else:
            print("✗ 医院信息更新失败")
            return False
        
        # 验证更新结果
        updated_info = db_manager.get_hospital_info()
        print(f"更新后的医院信息: {updated_info}")
        
        # 检查更新是否正确
        if (updated_info.get('hname') == test_hospital_info['hname'] and
            updated_info.get('keshi') == test_hospital_info['keshi'] and
            updated_info.get('shebeiid') == test_hospital_info['shebeiid']):
            print("✓ 医院信息更新验证成功")
            return True
        else:
            print("✗ 医院信息更新验证失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库医院信息操作测试失败: {e}")
        return False
    finally:
        try:
            db_manager.close()
        except:
            pass

def test_config_save():
    """测试配置保存功能"""
    print("\n" + "=" * 60)
    print("配置保存功能测试")
    print("=" * 60)
    
    try:
        # 显示当前配置
        print("当前电刺激配置:")
        config = AppConfig.STIMULATION_CONFIG
        print(f"  端口号: {config.get('port_num', 1)}")
        print(f"  最大电流: {config.get('max_current', 50.0)}")
        print(f"  默认频率: {config.get('default_frequency', 20.0)}")
        
        # 修改配置
        test_configs = {
            'port_num': 7,
            'max_current': 30.0,
            'default_frequency': 25.0,
            'default_pulse_width': 250.0
        }
        
        print(f"\n修改配置:")
        for key, value in test_configs.items():
            old_value = config[key]
            config[key] = value
            print(f"  {key}: {old_value} -> {value}")
        
        # 保存配置
        if AppConfig.save_user_config():
            print("✓ 配置保存成功")
        else:
            print("✗ 配置保存失败")
            return False
        
        # 验证配置文件
        if AppConfig.CONFIG_FILE.exists():
            print(f"✓ 配置文件存在: {AppConfig.CONFIG_FILE}")
            return True
        else:
            print("✗ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 配置保存功能测试失败: {e}")
        return False

def test_complete_settings_save():
    """测试完整的设置保存流程"""
    print("\n" + "=" * 60)
    print("完整设置保存流程测试")
    print("=" * 60)
    
    try:
        # 1. 创建数据库管理器
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("✗ 数据库初始化失败")
            return False
        
        # 2. 模拟设置界面的保存操作
        print("模拟设置界面保存操作...")
        
        # 保存电刺激设备配置到AppConfig
        AppConfig.STIMULATION_CONFIG['port_num'] = 7
        AppConfig.STIMULATION_CONFIG['max_current'] = 30.0
        AppConfig.STIMULATION_CONFIG['default_frequency'] = 25.0
        AppConfig.STIMULATION_CONFIG['default_pulse_width'] = 250.0
        
        print("✓ 电刺激配置已更新")
        
        # 保存医院信息到数据库
        hospital_info = {
            'hname': '北京康复医院',
            'keshi': '神经康复科',
            'shebeiid': 'NK2024001'
        }
        
        if db_manager.update_hospital_info(hospital_info):
            print("✓ 医院信息已保存到数据库")
        else:
            print("✗ 医院信息保存失败")
            return False
        
        # 保存配置到文件
        if AppConfig.save_user_config():
            print("✓ 用户配置已保存到文件")
        else:
            print("✗ 用户配置保存失败")
            return False
        
        # 3. 验证保存结果
        print("\n验证保存结果:")
        
        # 验证配置文件
        if AppConfig.CONFIG_FILE.exists():
            print("✓ 配置文件存在")
        else:
            print("✗ 配置文件不存在")
            return False
        
        # 验证数据库中的医院信息
        saved_hospital_info = db_manager.get_hospital_info()
        if (saved_hospital_info.get('hname') == hospital_info['hname'] and
            saved_hospital_info.get('keshi') == hospital_info['keshi'] and
            saved_hospital_info.get('shebeiid') == hospital_info['shebeiid']):
            print("✓ 数据库中的医院信息正确")
        else:
            print("✗ 数据库中的医院信息不正确")
            return False
        
        # 验证配置中的端口号
        if AppConfig.STIMULATION_CONFIG['port_num'] == 7:
            print("✓ 电刺激端口号配置正确")
        else:
            print("✗ 电刺激端口号配置错误")
            return False
        
        print("\n🎉 完整设置保存流程测试成功！")
        return True
        
    except Exception as e:
        print(f"✗ 完整设置保存流程测试失败: {e}")
        return False
    finally:
        try:
            db_manager.close()
        except:
            pass

def main():
    """主函数"""
    print("设置保存功能测试")
    print("测试时间:", os.popen('date /t').read().strip() if os.name == 'nt' else os.popen('date').read().strip())
    
    # 运行测试
    tests = [
        ("数据库医院信息操作", test_database_hospital_info),
        ("配置保存功能", test_config_save),
        ("完整设置保存流程", test_complete_settings_save),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总计: {total} 项测试")
    print(f"通过: {passed} 项")
    print(f"失败: {total - passed} 项")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 设置保存功能现在应该能正常工作")
        return True
    else:
        print("⚠️ 部分测试失败，请检查设置保存功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
