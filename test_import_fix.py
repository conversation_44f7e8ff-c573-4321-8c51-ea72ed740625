#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入修复
验证TreatmentWidget是否可以正常导入和创建
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    print("🧪 测试TreatmentWidget导入和创建")
    print("=" * 50)
    
    try:
        # 测试导入
        print("📦 步骤1: 测试导入...")
        from ui.treatment_ui import TreatmentWidget
        print("✅ TreatmentWidget导入成功")
        
        # 测试创建（不启动Qt应用）
        print("\n🏗️ 步骤2: 测试类创建...")
        
        # 检查类是否有必要的方法
        required_methods = [
            'on_channel_a_current_changed',
            'on_channel_b_current_changed',
            '_execute_continuous_pre_stimulation',
            '_stop_channel_pre_stimulation',
            'on_pre_stimulation_finished'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(TreatmentWidget, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必要方法都存在")
        
        # 检查是否还有已删除的方法引用
        removed_methods = [
            'trigger_channel_a_pre_stimulation',
            'trigger_channel_b_pre_stimulation'
        ]
        
        existing_removed_methods = []
        for method_name in removed_methods:
            if hasattr(TreatmentWidget, method_name):
                existing_removed_methods.append(method_name)
        
        if existing_removed_methods:
            print(f"⚠️ 仍存在已删除的方法: {existing_removed_methods}")
        else:
            print("✅ 已删除的方法已正确移除")
        
        print("\n🎯 测试完成")
        print("✅ TreatmentWidget修复成功，可以正常导入和使用")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except AttributeError as e:
        print(f"❌ 属性错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_continuous_pre_stimulation_logic():
    """测试连续预刺激逻辑"""
    print("\n" + "=" * 50)
    print("🧪 测试连续预刺激逻辑")
    print("=" * 50)
    
    try:
        from ui.treatment_ui import TreatmentWidget
        
        # 检查新的连续预刺激方法
        methods_to_check = {
            '_execute_continuous_pre_stimulation': '执行连续预刺激',
            '_stop_channel_pre_stimulation': '停止通道预刺激',
            'on_channel_a_current_changed': 'A通道电流变化处理',
            'on_channel_b_current_changed': 'B通道电流变化处理'
        }
        
        print("📋 检查连续预刺激相关方法:")
        for method_name, description in methods_to_check.items():
            if hasattr(TreatmentWidget, method_name):
                print(f"   ✅ {method_name}: {description}")
            else:
                print(f"   ❌ {method_name}: {description} - 缺失")
        
        # 检查方法签名
        print("\n📋 检查方法签名:")
        widget_class = TreatmentWidget
        
        # 检查on_channel_a_current_changed方法
        if hasattr(widget_class, 'on_channel_a_current_changed'):
            method = getattr(widget_class, 'on_channel_a_current_changed')
            print(f"   ✅ on_channel_a_current_changed: {method.__doc__ or '无文档'}")
        
        # 检查_execute_continuous_pre_stimulation方法
        if hasattr(widget_class, '_execute_continuous_pre_stimulation'):
            method = getattr(widget_class, '_execute_continuous_pre_stimulation')
            print(f"   ✅ _execute_continuous_pre_stimulation: {method.__doc__ or '无文档'}")
        
        print("\n✅ 连续预刺激逻辑检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 连续预刺激逻辑检查失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_import()
    success2 = test_continuous_pre_stimulation_logic()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！连续预刺激功能修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步修复")
        sys.exit(1)
