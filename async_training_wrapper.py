#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步训练包装器
Asynchronous Training Wrapper for EEGNet Models

提供非阻塞的模型训练功能，避免UI卡住
"""

import logging
import threading
import numpy as np
from typing import Callable, Optional, List, Tuple, Dict
from dataclasses import dataclass

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


@dataclass
class ModelAdjustmentManager:
    """模型调整管理器"""
    
    def __init__(self, model: MotorImageryModel):
        self.model = model
        self.logger = logging.getLogger(__name__)
        self.prediction_history: List[Tuple[int, float]] = []
        self.max_history = 100
        
    def add_prediction(self, prediction: int, confidence: float):
        """添加预测到历史记录"""
        self.prediction_history.append((prediction, confidence))
        if len(self.prediction_history) > self.max_history:
            self.prediction_history.pop(0)
    
    def get_prediction_stats(self) -> Dict[str, float]:
        """获取预测统计"""
        if not self.prediction_history:
            return {'active_ratio': 0.0, 'avg_confidence': 0.0}
        
        predictions = [pred for pred, conf in self.prediction_history]
        confidences = [conf for pred, conf in self.prediction_history]
        
        active_ratio = sum(predictions) / len(predictions)
        avg_confidence = np.mean(confidences)
        
        return {
            'active_ratio': active_ratio,
            'avg_confidence': avg_confidence,
            'total_predictions': len(predictions)
        }
    
    def auto_calibrate(self, target_active_ratio: float = 0.3) -> bool:
        """自动校准模型"""
        try:
            if len(self.prediction_history) < 20:
                self.logger.warning("预测历史不足，无法校准")
                return False
            
            stats = self.get_prediction_stats()
            current_ratio = stats['active_ratio']
            
            if abs(current_ratio - target_active_ratio) < 0.1:
                self.logger.info("模型已校准，无需调整")
                return True
            
            # 调整决策阈值
            model_info = self.model.get_model_info()
            current_threshold = model_info.decision_threshold
            
            if current_ratio < target_active_ratio:
                # 活跃比例太低，降低阈值
                new_threshold = max(0.1, current_threshold - 0.05)
            else:
                # 活跃比例太高，提高阈值
                new_threshold = min(0.9, current_threshold + 0.05)
            
            model_info.decision_threshold = new_threshold
            
            self.logger.info(f"自动校准完成: 阈值 {current_threshold:.3f} → {new_threshold:.3f}")
            return True
            
        except Exception as e:
            self.logger.error(f"自动校准失败: {e}")
            return False
    
    def adjust_difficulty(self, level: int):
        """调整难度等级"""
        self.model.adjust_difficulty(level)


class AsyncTrainingWrapper:
    """异步训练包装器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.training_thread: Optional[threading.Thread] = None
        self.is_training = False
        self.training_progress = 0
        self.training_message = ""
        
    def train_model_async(self, model: MotorImageryModel,
                         neural_network: str = "eegnet",
                         use_deep_learning: bool = True,
                         progress_callback: Callable = None,
                         completion_callback: Callable = None) -> bool:
        """异步训练模型"""
        try:
            if self.is_training:
                self.logger.warning("已有训练任务在进行中")
                return False
            
            # 创建训练线程
            self.training_thread = threading.Thread(
                target=self._training_worker,
                args=(model, neural_network, use_deep_learning, progress_callback, completion_callback),
                daemon=True
            )
            
            self.is_training = True
            self.training_thread.start()
            
            self.logger.info("异步训练已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动异步训练失败: {e}")
            return False
    
    def _training_worker(self, model: MotorImageryModel,
                        neural_network: str,
                        use_deep_learning: bool,
                        progress_callback: Callable,
                        completion_callback: Callable):
        """训练工作线程"""
        try:
            self.logger.info("开始异步训练...")
            
            # 更新进度
            if progress_callback:
                progress_callback("准备训练...", 0)
            
            # 创建训练配置
            config = TrainingConfig(
                epochs=10,  # 适中的epoch数
                batch_size=8,
                learning_rate=0.001,
                validation_split=0.2
            )
            
            # 执行训练
            if progress_callback:
                progress_callback("开始训练EEGNet模型...", 10)
            
            success = model.train_model(neural_network, config, self._progress_wrapper(progress_callback))
            
            # 完成回调
            if completion_callback:
                completion_callback(success, model)
            
            if progress_callback:
                if success:
                    progress_callback("训练完成", 100)
                else:
                    progress_callback("训练失败", 0)
            
            self.logger.info(f"异步训练完成: {'成功' if success else '失败'}")
            
        except Exception as e:
            self.logger.error(f"异步训练失败: {e}")
            if completion_callback:
                completion_callback(False, None)
        finally:
            self.is_training = False
    
    def _progress_wrapper(self, original_callback: Callable) -> Callable:
        """包装进度回调"""
        def wrapped_callback(message: str, progress: int):
            self.training_progress = progress
            self.training_message = message
            if original_callback:
                # 将训练进度映射到10-90%范围
                mapped_progress = 10 + int(progress * 0.8)
                original_callback(message, mapped_progress)
        
        return wrapped_callback
    
    def is_training_active(self) -> bool:
        """检查是否正在训练"""
        return self.is_training
    
    def get_training_progress(self) -> Tuple[int, str]:
        """获取训练进度"""
        return self.training_progress, self.training_message
    
    def stop_training(self):
        """停止训练（注意：实际上很难中断正在进行的训练）"""
        if self.is_training:
            self.logger.warning("请求停止训练（训练可能需要一些时间才能停止）")
            # 实际的训练停止需要在模型训练代码中实现
            # 这里只是标记状态


# 为了兼容性，提供一些常用的函数
def create_async_wrapper() -> AsyncTrainingWrapper:
    """创建异步训练包装器"""
    return AsyncTrainingWrapper()


def create_adjustment_manager(model: MotorImageryModel) -> ModelAdjustmentManager:
    """创建模型调整管理器"""
    return ModelAdjustmentManager(model)


# 导出主要类
__all__ = [
    'AsyncTrainingWrapper',
    'ModelAdjustmentManager',
    'create_async_wrapper',
    'create_adjustment_manager'
]
