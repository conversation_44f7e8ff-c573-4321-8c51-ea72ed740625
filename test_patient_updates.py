#!/usr/bin/env python3
"""
测试患者编号验证和保存后刷新功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_patient_number_validation():
    """测试患者编号验证逻辑"""
    
    def validate_bian<PERSON>(bianhao_text):
        """模拟患者编号验证逻辑"""
        if not bianhao_text:
            return False, "请输入患者编号！"
        
        # 检查是否为纯数字
        if not bianhao_text.isdigit():
            return False, "患者编号必须是数字！"
        
        # 检查长度
        if len(bianhao_text) > 15:
            return False, "患者编号长度不能超过15位！"
        
        return True, "验证通过"
    
    # 测试用例
    test_cases = [
        ("", False, "空编号应该失败"),
        ("0", True, "单个0应该通过"),
        ("01", True, "0开头的编号应该通过"),
        ("123", True, "普通数字编号应该通过"),
        ("000123", True, "多个0开头的编号应该通过"),
        ("123456789012345", True, "15位数字编号应该通过"),
        ("1234567890123456", False, "16位数字编号应该失败"),
        ("12345a", False, "包含字母的编号应该失败"),
        ("123-456", False, "包含特殊字符的编号应该失败"),
        ("123 456", False, "包含空格的编号应该失败"),
    ]
    
    print("患者编号验证测试:")
    print("=" * 50)
    
    all_passed = True
    for bianhao, expected, description in test_cases:
        result, message = validate_bianhao(bianhao)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status} | 输入: '{bianhao}' | 期望: {'通过' if expected else '失败'} | 实际: {'通过' if result else '失败'}")
        print(f"      | 描述: {description}")
        if not passed:
            print(f"      | 错误: 期望 {'通过' if expected else '失败'}，但实际 {'通过' if result else '失败'}")
            print(f"      | 消息: {message}")
        print()
    
    print("=" * 50)
    if all_passed:
        print("✓ 所有测试用例都通过了！")
    else:
        print("✗ 有测试用例失败！")
    
    return all_passed

def test_input_validator_regex():
    """测试更新后的输入验证器正则表达式"""
    import re
    
    print("\n更新后的输入验证器测试:")
    print("=" * 50)
    
    # 测试患者编号验证器
    bianhao_pattern = r'^\d{0,15}$'
    bianhao_regex = re.compile(bianhao_pattern)
    
    bianhao_tests = [
        ("", True, "空字符串"),
        ("0", True, "单个0"),
        ("01", True, "0开头"),
        ("123", True, "普通数字"),
        ("000123", True, "多个0开头"),
        ("123456789012345", True, "15位数字"),
        ("1234567890123456", False, "16位数字（超过限制）"),
        ("abc", False, "非数字"),
        ("12a", False, "包含字母"),
    ]
    
    print("患者编号验证器测试 (^\d{0,15}$):")
    all_passed = True
    for test_input, expected, desc in bianhao_tests:
        result = bool(bianhao_regex.match(test_input))
        passed = result == expected
        all_passed = all_passed and passed
        status = "✓" if passed else "✗"
        print(f"{status} '{test_input}' -> {result} ({desc})")
    
    print(f"\n患者编号验证器测试结果: {'✓ 全部通过' if all_passed else '✗ 有失败'}")
    
    return all_passed

def test_refresh_logic():
    """测试刷新逻辑的设计"""
    print("\n患者信息刷新逻辑测试:")
    print("=" * 50)
    
    print("刷新逻辑步骤:")
    print("1. ✓ 保存患者信息到数据库")
    print("2. ✓ 重新加载患者列表 (load_patients)")
    print("3. ✓ 从数据库获取更新后的患者数据 (get_updated_patient_data)")
    print("4. ✓ 更新当前患者对象 (self.current_patient)")
    print("5. ✓ 刷新表单显示 (populate_patient_form)")
    print("6. ✓ 重新选中表格行 (select_patient_in_table)")
    print("7. ✓ 退出编辑模式 (cancel_edit)")
    
    print("\n预期效果:")
    print("- 编辑保存后，表单立即显示最新的数据")
    print("- 不需要切换患者就能看到更新后的信息")
    print("- 患者列表中的信息也会同步更新")
    print("- 当前选中的患者保持不变")
    
    return True

if __name__ == "__main__":
    print("患者管理功能更新测试")
    print("=" * 60)
    
    test1_passed = test_patient_number_validation()
    test2_passed = test_input_validator_regex()
    test3_passed = test_refresh_logic()
    
    print("\n" + "=" * 60)
    print("总体测试结果:")
    print(f"患者编号验证: {'✓ 通过' if test1_passed else '✗ 失败'}")
    print(f"输入验证器: {'✓ 通过' if test2_passed else '✗ 失败'}")
    print(f"刷新逻辑设计: {'✓ 通过' if test3_passed else '✗ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试都通过了！")
    else:
        print("\n❌ 有测试失败，请检查代码。")
