#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试警告信息清理效果
验证状态不一致警告是否已被清理
"""

import sys
import time
import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from core.stimulation_device import StimulationDevice

def test_warning_cleanup():
    """测试警告信息清理效果"""
    print("🧪 测试警告信息清理效果")
    print("=" * 50)
    
    # 设置日志级别为INFO，确保不显示DEBUG信息
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    app = QApplication(sys.argv)
    
    try:
        # 创建设备实例
        device = StimulationDevice()
        
        print("📱 步骤1: 连接设备...")
        if device.connect(7):
            print("✅ 设备连接成功")
            
            print("\n📊 步骤2: 执行可能触发警告的操作...")
            print("   注意观察：应该不再有状态不一致的WARNING信息")
            
            # 模拟触发警告的操作
            def trigger_warnings():
                try:
                    print("   - 设置电流（可能触发状态异常警告）")
                    device.set_current(1, 5.0)
                    
                    QTimer.singleShot(1000, lambda: device.set_current(1, 10.0))
                    QTimer.singleShot(2000, lambda: device.set_current(2, 8.0))
                    
                    # 尝试启动刺激（可能触发状态不一致警告）
                    QTimer.singleShot(3000, lambda: device.trigger_stimulation(1))
                    QTimer.singleShot(4000, lambda: device.trigger_stimulation(2))
                    
                    # 停止操作
                    QTimer.singleShot(7000, lambda: device.stop_stimulation(1))
                    QTimer.singleShot(8000, lambda: device.stop_stimulation(2))
                    
                    # 结束测试
                    QTimer.singleShot(10000, lambda: app.quit())
                    
                except Exception as e:
                    print(f"❌ 操作失败: {e}")
                    app.quit()
            
            QTimer.singleShot(500, trigger_warnings)
            
            # 运行事件循环
            app.exec()
            
            print("\n📊 步骤3: 断开设备...")
            device.disconnect()
            print("✅ 设备断开成功")
            
        else:
            print("❌ 设备连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎯 测试完成")
    print("检查结果：")
    print("✅ 如果没有看到WARNING级别的状态不一致信息，说明清理成功")
    print("❌ 如果仍有'⚠ 电流设置后通道状态异常'等WARNING，说明清理不完整")

if __name__ == "__main__":
    test_warning_cleanup()
