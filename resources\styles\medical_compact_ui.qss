/* 
脑机接口康复训练系统 - 紧凑型医疗UI样式表 v1.0
Brain-Computer Interface Medical UI Stylesheet - Compact Version
专为医疗器械设计的紧凑型界面样式，解决内容遮挡问题
*/

/* ==================== 全局样式 ==================== */
* {
    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    outline: none;
}

/* 主窗口样式 */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8fafb, stop:0.5 #f0f4f8, stop:1 #f8fafb);
    color: #2c3e50;
}

/* ==================== 菜单栏样式 ==================== */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1d4ed8);
    color: white;
    border: none;
    padding: 2px;
    font-weight: 600;
    font-size: 12px;
    border-bottom: 1px solid #3b82f6;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
    margin: 1px 2px;
    font-weight: 600;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
}

QMenuBar::item:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #06b6d4, stop:1 #0891b2);
}

QMenu {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
    margin: 1px;
    color: #374151;
    font-weight: 500;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
}

/* ==================== 状态栏样式 ==================== */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f9fafb);
    border-top: 1px solid #3b82f6;
    color: #374151;
    font-size: 11px;
    font-weight: 500;
    padding: 2px;
}

QStatusBar::item {
    border: none;
    padding: 2px 4px;
    margin: 0 2px;
    border-radius: 3px;
}

QStatusBar QLabel {
    color: #4b5563;
    font-weight: 500;
    padding: 1px 3px;
    border-radius: 2px;
}

/* ==================== 导航区域样式 ==================== */
QFrame#navigation_frame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0f172a, stop:0.3 #1e293b, stop:0.7 #334155, stop:1 #1e293b);
    border-right: 3px solid qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490);
    border-radius: 0px 8px 8px 0px;
}

/* ==================== 导航按钮样式 ==================== */
QToolButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(30, 41, 59, 0.8), stop:1 rgba(51, 65, 85, 0.6));
    border: 1px solid rgba(100, 116, 139, 0.3);
    color: #cbd5e1;
    padding: 10px 8px;
    text-align: center;
    border-radius: 8px;
    margin: 3px 6px;
    font-size: 12px;
    font-weight: 600;
    min-height: 28px;
    min-width: 85px;
}

QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0891b2, stop:0.5 #06b6d4, stop:1 #0e7490);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
    font-weight: 700;
}

QToolButton:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0891b2, stop:0.3 #06b6d4, stop:0.7 #22d3ee, stop:1 #06b6d4);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    font-weight: 700;
}

QToolButton:disabled {
    color: #475569;
    background: rgba(30, 41, 59, 0.3);
    border: 1px solid rgba(100, 116, 139, 0.2);
}

/* ==================== 按钮样式 ==================== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    min-width: 60px;
    min-height: 24px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1d4ed8, stop:1 #1e40af);
}

QPushButton:disabled {
    background: #d1d5db;
    color: #9ca3af;
}

/* 医疗主要按钮样式 */
QPushButton.primary {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #06b6d4, stop:1 #0891b2);
}

QPushButton.primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0891b2, stop:1 #0e7490);
}

/* 危险按钮样式 */
QPushButton.danger {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ef4444, stop:1 #dc2626);
}

QPushButton.danger:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #dc2626, stop:1 #b91c1c);
}

/* 成功按钮样式 */
QPushButton.success {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #10b981, stop:1 #059669);
}

QPushButton.success:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #059669, stop:1 #047857);
}

/* 警告按钮样式 */
QPushButton.warning {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f59e0b, stop:1 #d97706);
}

QPushButton.warning:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #d97706, stop:1 #b45309);
}

/* 大尺寸按钮 */
QPushButton.large {
    padding: 10px 20px;
    font-size: 14px;
    min-height: 32px;
    min-width: 100px;
}

/* 小尺寸按钮 */
QPushButton.small {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 20px;
    min-width: 50px;
}

/* ==================== 输入框样式 ==================== */
QLineEdit {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
    color: #374151;
    font-weight: 500;
    selection-background-color: #3b82f6;
    selection-color: white;
}

QLineEdit:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 2px;
}

QLineEdit:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

QLineEdit:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    border-color: #d1d5db;
}

QLineEdit[readOnly="true"] {
    background-color: #f9fafb;
    color: #6b7280;
    border-color: #d1d5db;
}

/* ==================== 文本区域样式 ==================== */
QTextEdit {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
    color: #374151;
    font-weight: 500;
    selection-background-color: #3b82f6;
    selection-color: white;
}

QTextEdit:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 2px;
}

QTextEdit:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

/* 系统日志文本区域特殊样式 */
QTextEdit#system_log {
    background-color: #1e293b;
    color: #e2e8f0;
    border: 2px solid #475569;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 11px;
}

/* ==================== 表格样式 ==================== */
QTableWidget {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    gridline-color: #f3f4f6;
    selection-background-color: #3b82f6;
    selection-color: white;
    alternate-background-color: #f8fafc;
    font-size: 11px;
}

QTableWidget::item {
    padding: 4px 6px;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    font-weight: 500;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    font-weight: 600;
}

QTableWidget::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1e3a8a);
    color: white;
    padding: 6px 4px;
    border: none;
    font-weight: 600;
    font-size: 11px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QHeaderView::section:first {
    border-top-left-radius: 6px;
}

QHeaderView::section:last {
    border-top-right-radius: 6px;
}

/* ==================== 列表样式 ==================== */
QListWidget {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 4px;
}

QListWidget::item {
    padding: 6px 8px;
    border-radius: 4px;
    margin: 1px;
    color: #374151;
    font-weight: 500;
    border: 1px solid transparent;
}

QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    color: white;
    font-weight: 600;
}

QListWidget::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #3b82f6;
}

/* ==================== 组合框样式 ==================== */
QComboBox {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
    color: #374151;
    font-weight: 500;
    min-width: 100px;
    min-height: 20px;
}

QComboBox:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 2px;
}

QComboBox:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #6b7280;
    width: 0;
    height: 0;
}

QComboBox QAbstractItemView {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    selection-background-color: #3b82f6;
    selection-color: white;
    padding: 2px;
}

QComboBox QAbstractItemView::item {
    padding: 6px 8px;
    border-radius: 3px;
    margin: 1px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #eff6ff;
    color: #1e40af;
}

/* ==================== 复选框样式 ==================== */
QCheckBox {
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-color: #3b82f6;
    image: none;
}

QCheckBox::indicator:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

QCheckBox::indicator:disabled {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

/* ==================== 分组框样式 ==================== */
QGroupBox {
    font-weight: 600;
    font-size: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-top: 8px;
    padding-top: 8px;
    color: #374151;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 2px 8px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e40af, stop:1 #3b82f6);
    color: white;
    border-radius: 4px;
    font-weight: 600;
    font-size: 11px;
}

/* ==================== 进度条样式 ==================== */
QProgressBar {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    font-size: 11px;
    color: #374151;
    background-color: #f9fafb;
    min-height: 16px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #06b6d4, stop:0.5 #3b82f6, stop:1 #2563eb);
    border-radius: 5px;
}

/* ==================== 滚动条样式 ==================== */
QScrollBar:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    width: 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 5px;
    min-height: 20px;
    margin: 1px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
    background: transparent;
}

QScrollBar:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    height: 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 5px;
    min-width: 20px;
    margin: 1px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:1 #1d4ed8);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
    background: transparent;
}

/* ==================== 标签页样式 ==================== */
QTabWidget::pane {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: white;
    padding: 2px;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f9fafb, stop:1 #f3f4f6);
    border: 1px solid #e5e7eb;
    padding: 6px 12px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    font-weight: 600;
    color: #6b7280;
    font-size: 12px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1e3a8a);
    color: white;
    border-bottom-color: #1e40af;
    font-weight: 700;
}

QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #eff6ff, stop:1 #dbeafe);
    color: #1e40af;
}

/* ==================== 数值输入框样式 ==================== */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    min-width: 60px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3b82f6;
    background-color: #f8fafc;
    border-width: 2px;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-top-right-radius: 4px;
    width: 16px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
    border-bottom-right-radius: 4px;
    width: 16px;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 3px solid white;
    width: 0;
    height: 0;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    image: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 3px solid white;
    width: 0;
    height: 0;
}

/* ==================== 医疗设备状态指示器 ==================== */

/* 设备连接状态标签 */
QLabel.device_status {
    font-weight: 600;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid transparent;
}

QLabel.device_connected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #10b981, stop:1 #059669);
    color: white;
    border-color: #10b981;
}

QLabel.device_disconnected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ef4444, stop:1 #dc2626);
    color: white;
    border-color: #ef4444;
}

QLabel.device_connecting {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f59e0b, stop:1 #d97706);
    color: white;
    border-color: #f59e0b;
}

/* 实时数据显示区域 */
QFrame.realtime_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e293b, stop:1 #334155);
    border: 2px solid #3b82f6;
    border-radius: 8px;
    padding: 8px;
}

/* 脑电信号显示区域 */
QFrame.eeg_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0f172a, stop:1 #1e293b);
    border: 2px solid #06b6d4;
    border-radius: 6px;
    padding: 6px;
}

/* 控制面板区域 */
QFrame.control_panel {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #ffffff);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px;
}

/* 治疗状态指示器 */
QLabel.treatment_status {
    font-weight: 600;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid transparent;
}

QLabel.treatment_active {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #06b6d4, stop:1 #0891b2);
    color: white;
    border-color: #06b6d4;
}

QLabel.treatment_paused {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f59e0b, stop:1 #d97706);
    color: white;
    border-color: #f59e0b;
}

QLabel.treatment_stopped {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #6b7280, stop:1 #4b5563);
    color: white;
    border-color: #6b7280;
}

/* ==================== 对话框样式 ==================== */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #ffffff, stop:1 #f8fafc);
    border: 2px solid #3b82f6;
    border-radius: 8px;
}

QMessageBox {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #ffffff, stop:1 #f8fafc);
    border: 2px solid #3b82f6;
    border-radius: 8px;
}

QMessageBox QLabel {
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    padding: 8px;
}

/* ==================== 特殊医疗标签样式 ==================== */
QLabel.medical_title {
    font-size: 16px;
    font-weight: 700;
    color: #1e40af;
    padding: 8px 0;
    border-bottom: 2px solid #3b82f6;
    margin-bottom: 8px;
}

QLabel.medical_subtitle {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    padding: 4px 0;
}

QLabel.medical_value {
    font-size: 14px;
    font-weight: 700;
    color: #1e293b;
    background-color: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 6px 8px;
    min-width: 50px;
    text-align: center;
}

/* ==================== 工具提示样式 ==================== */
QToolTip {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e293b, stop:1 #334155);
    color: white;
    border: 1px solid #3b82f6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 11px;
    font-weight: 500;
}

/* ==================== 登录界面特殊样式 ==================== */
QWidget#login_widget {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.3 #e8f4f8, stop:0.7 #f0f8ff, stop:1 #f8fafc);
    border-radius: 12px;
}

/* ==================== 医疗警告样式 ==================== */
QFrame.medical_warning {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #fef3c7, stop:1 #fde68a);
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 8px;
}

QFrame.medical_error {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #fee2e2, stop:1 #fecaca);
    border: 1px solid #ef4444;
    border-radius: 6px;
    padding: 8px;
}

QFrame.medical_success {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #d1fae5, stop:1 #a7f3d0);
    border: 1px solid #10b981;
    border-radius: 6px;
    padding: 8px;
}
