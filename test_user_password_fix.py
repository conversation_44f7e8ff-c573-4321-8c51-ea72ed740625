#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户密码修改功能测试脚本
Test script for user password modification functionality

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

from core.auth_manager import AuthManager, UserRole, Permission
from core.database_manager import DatabaseManager
from ui.user_management_ui import UserManagementWidget

class TestWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("用户密码修改功能测试")
        self.setGeometry(100, 100, 1200, 800)

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # 初始化数据库和权限管理器
        self.init_managers()

        # 创建UI
        self.init_ui()

        # 测试不同角色的登录
        self.test_scenarios()

    def init_managers(self):
        """初始化管理器"""
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()

            # 初始化权限管理器
            self.auth_manager = AuthManager(self.db_manager)

            self.logger.info("管理器初始化成功")

        except Exception as e:
            self.logger.error(f"管理器初始化失败: {e}")
            sys.exit(1)

    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 状态标签
        self.status_label = QLabel("当前未登录")
        self.status_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)

        # 测试按钮
        self.admin_login_btn = QPushButton("以管理员身份登录 (admin)")
        self.admin_login_btn.clicked.connect(lambda: self.login_as_role("admin"))
        layout.addWidget(self.admin_login_btn)

        self.doctor_login_btn = QPushButton("以医生身份登录 (test_doctor)")
        self.doctor_login_btn.clicked.connect(lambda: self.login_as_role("test_doctor"))
        layout.addWidget(self.doctor_login_btn)

        self.technician_login_btn = QPushButton("以技师身份登录 (test_operator)")
        self.technician_login_btn.clicked.connect(lambda: self.login_as_role("test_operator"))
        layout.addWidget(self.technician_login_btn)

        self.logout_btn = QPushButton("退出登录")
        self.logout_btn.clicked.connect(self.logout)
        layout.addWidget(self.logout_btn)

        # 用户管理界面
        self.user_management = UserManagementWidget()
        self.user_management.set_auth_manager(self.auth_manager)
        self.user_management.set_database_manager(self.db_manager)
        layout.addWidget(self.user_management)

    def login_as_role(self, username: str):
        """以指定角色登录"""
        try:
            # 使用默认密码登录
            success = self.auth_manager.login(username, "admin123")

            if success:
                current_user = self.auth_manager.get_current_user()
                self.status_label.setText(f"当前登录用户: {current_user['name']} ({current_user['role']})")
                self.status_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #d4edda; color: #155724;")

                # 刷新用户管理界面
                self.user_management.refresh_user_list()

                self.logger.info(f"登录成功: {username}")
            else:
                self.status_label.setText(f"登录失败: {username}")
                self.status_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f8d7da; color: #721c24;")
                self.logger.error(f"登录失败: {username}")

        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            self.status_label.setText(f"登录异常: {e}")
            self.status_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f8d7da; color: #721c24;")

    def logout(self):
        """退出登录"""
        self.auth_manager.logout()
        self.status_label.setText("当前未登录")
        self.status_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f0f0f0;")

        # 刷新用户管理界面
        self.user_management.refresh_user_list()

        self.logger.info("已退出登录")

    def test_scenarios(self):
        """测试场景"""
        self.logger.info("=== 用户密码修改功能测试场景 ===")
        self.logger.info("1. 以管理员身份登录 - 应该能够修改所有用户信息包括密码")
        self.logger.info("2. 以医生身份登录 - 应该只能修改自己的密码，其他用户信息为灰色")
        self.logger.info("3. 以技师身份登录 - 应该只能修改自己的密码，其他用户信息为灰色")
        self.logger.info("4. 选择自己的用户记录 - 密码字段应该可以编辑")
        self.logger.info("5. 选择其他用户记录 - 根据权限决定是否可以编辑")

def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("用户密码修改功能测试")
        app.setApplicationVersion("1.0.0")

        # 创建并显示测试窗口
        window = TestWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec())
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
