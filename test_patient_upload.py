#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
患者数据上传功能测试脚本
Test Script for Patient Data Upload Functionality

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader, UploadStatus
from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_upload_functionality():
    """测试上传功能"""
    print("=" * 60)
    print("患者数据上传功能测试")
    print("=" * 60)
    
    # 初始化数据库管理器
    print("\n1. 初始化数据库管理器...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return False
    print("✅ 数据库初始化成功")
    
    # 获取医院信息
    print("\n2. 获取医院信息...")
    hospital_info = db_manager.get_hospital_info()
    if not hospital_info:
        print("❌ 未找到医院信息")
        return False
    print(f"✅ 医院信息: {hospital_info}")
    
    # 创建测试患者数据
    print("\n3. 创建测试患者数据...")
    test_patient = {
        'bianhao': 999999,
        'name': '测试患者',
        'age': 30,
        'xingbie': '男',
        'cardid': '110101199001011234',
        'zhenduan': '测试诊断',
        'bingshi': '测试既往史',
        'brhc': '左侧',
        'zhuzhi': '测试医生',
        'czy': '测试操作员',
        'keshi': '康复科',
        'shebeiid': 'TEST001',
        'yiyuanid': hospital_info.get('id', 1)
    }
    print(f"✅ 测试患者数据: {test_patient}")
    
    # 初始化上传器
    print("\n4. 初始化数据上传器...")
    uploader = PatientDataUploader()
    print("✅ 数据上传器初始化成功")
    
    # 测试网络连接
    print("\n5. 测试网络连接...")
    connection_ok = uploader.test_connection()
    if connection_ok:
        print("✅ 网络连接正常")
    else:
        print("⚠️ 网络连接异常，但会继续测试上传功能")
    
    # 测试JSON数据准备
    print("\n6. 测试JSON数据准备...")
    try:
        json_data = uploader._prepare_json_data(test_patient, hospital_info)
        print(f"✅ JSON数据准备成功: {json_data}")
    except Exception as e:
        print(f"❌ JSON数据准备失败: {e}")
        return False
    
    # 测试上传功能
    print("\n7. 测试上传功能...")
    try:
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        print(f"上传结果:")
        print(f"  - 成功: {upload_result.success}")
        print(f"  - 状态: {upload_result.status.value}")
        print(f"  - 消息: {upload_result.message}")
        if upload_result.response_data:
            print(f"  - 响应数据: {upload_result.response_data}")
        if upload_result.error_code:
            print(f"  - 错误代码: {upload_result.error_code}")
        
        if upload_result.success:
            print("✅ 上传测试成功")
        else:
            print("⚠️ 上传测试失败（这可能是正常的，如果服务器不可用）")
            
    except Exception as e:
        print(f"❌ 上传测试异常: {e}")
        return False
    
    # 测试数据库保存（带状态）
    print("\n8. 测试数据库保存...")
    try:
        # 设置上传状态
        test_patient['status'] = upload_result.status.value
        
        # 保存到数据库
        success = db_manager.add_patient(test_patient)
        if success:
            print("✅ 患者数据保存到数据库成功")
            
            # 验证保存的数据
            saved_patients = db_manager.execute_query(
                "SELECT * FROM bingren WHERE bianhao = ?",
                (test_patient['bianhao'],)
            )
            if saved_patients:
                saved_patient = saved_patients[0]
                print(f"✅ 验证保存的数据: 状态={saved_patient.get('status', 'N/A')}")
            else:
                print("❌ 未找到保存的患者数据")
        else:
            print("❌ 患者数据保存失败")
            
    except Exception as e:
        print(f"❌ 数据库保存测试异常: {e}")
        return False
    
    # 清理测试数据
    print("\n9. 清理测试数据...")
    try:
        db_manager.execute_non_query(
            "DELETE FROM bingren WHERE bianhao = ?",
            (test_patient['bianhao'],)
        )
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️ 测试数据清理失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    return True

def main():
    """主函数"""
    setup_logging()
    
    try:
        success = test_upload_functionality()
        if success:
            print("\n🎉 所有测试通过！患者数据上传功能已成功集成。")
        else:
            print("\n❌ 测试失败，请检查错误信息。")
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
