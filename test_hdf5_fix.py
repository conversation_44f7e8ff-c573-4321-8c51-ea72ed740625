#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HDF5数据保存修复
Test HDF5 Data Saving Fix

验证修复后的HDF5数据保存功能

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.training_data_integration import TrainingDataIntegration
from core.eeg_device import EEGDataPacket
from core.motor_imagery_trainer import TrainingState

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_hdf5_fix.log')
        ]
    )

def create_test_eeg_packet(packet_id: int) -> EEGDataPacket:
    """创建测试脑电数据包"""
    # 生成8通道的随机数据，每组4个样本
    channel_data_4groups = []
    for group in range(4):
        group_data = []
        for ch in range(8):
            # 生成模拟脑电数据
            value = int(np.random.randn() * 1000 + packet_id * 10)
            group_data.append(value)
        channel_data_4groups.append(group_data)
    
    return EEGDataPacket(
        timestamp=datetime.now().timestamp(),
        channel_data=channel_data_4groups,
        packet_number=packet_id,
        is_valid=True
    )

def test_hdf5_fix():
    """测试HDF5修复"""
    print("🔧 测试HDF5数据保存修复")
    print("=" * 50)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 初始化训练数据集成
        integration = TrainingDataIntegration(db_manager)
        
        # 测试患者ID
        test_patient_id = 8888
        
        # 创建测试患者
        print(f"👤 创建测试患者 {test_patient_id}...")
        
        # 先创建测试医院
        hospital_sql = "INSERT OR IGNORE INTO yiyuan (id, hname, keshi, shebeiid) VALUES (?, ?, ?, ?)"
        hospital_params = (1, "测试医院", "测试科室", "001")
        db_manager.execute_non_query(hospital_sql, hospital_params)
        
        # 创建测试患者
        patient_sql = """
            INSERT OR IGNORE INTO bingren (bianhao, name, xingbie, age, czy, keshi, shebeiid, yiyuanid)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        patient_params = (test_patient_id, "HDF5测试患者", "男", 25, "测试操作员", "测试科室", "001", 1)
        db_manager.execute_non_query(patient_sql, patient_params)
        print("✅ 测试患者创建成功")
        
        # 开始训练会话
        print(f"📊 开始测试患者 {test_patient_id} 的数据记录...")
        success = integration.start_training_session(test_patient_id)
        if not success:
            print("❌ 启动训练会话失败")
            return False
        
        print("✅ 训练会话启动成功")
        
        # 测试多个试验的快速保存（模拟实际训练场景）
        print("\n🧠 测试快速连续试验保存...")
        
        for trial_num in range(5):
            print(f"\n试验 {trial_num + 1}:")
            
            # 开始试验记录
            state = TrainingState.MOTOR_IMAGERY if trial_num % 2 == 0 else TrainingState.QUIET
            success = integration.start_trial_recording(
                training_state=state,
                round_number=1,
                trial_number=trial_num
            )
            
            if success:
                state_name = "运动想象" if state == TrainingState.MOTOR_IMAGERY else "休息"
                print(f"  ✅ {state_name}试验记录开始")
                
                # 快速处理多个数据包（需要足够的数据长度）
                for i in range(100):  # 增加数据包数量以满足最小长度要求
                    packet = create_test_eeg_packet(i)
                    integration.process_eeg_data(packet)

                print(f"  📈 已处理 100 个数据包")
                
                # 结束试验记录
                success = integration.end_trial_recording()
                if success:
                    print(f"  ✅ {state_name}试验记录完成")
                else:
                    print(f"  ❌ {state_name}试验记录结束失败")
            else:
                print(f"  ❌ 试验记录开始失败")
        
        # 结束训练会话
        success = integration.end_training_session()
        if success:
            print(f"\n✅ 训练会话结束，共记录 5 个试验")
        else:
            print("\n❌ 训练会话结束失败")
        
        # 验证数据
        print("\n📊 验证保存的数据...")
        from core.eeg_data_loader import EEGDataLoader
        loader = EEGDataLoader(db_manager)
        
        # 获取统计信息
        stats = loader.get_data_statistics([test_patient_id])
        print(f"  患者试验数: {stats.get('total_trials', 0)}")
        avg_quality = stats.get('avg_quality') or 0
        print(f"  平均质量: {avg_quality:.3f}")
        
        # 搜索试验数据
        criteria = {
            'patient_ids': [test_patient_id],
            'limit': 10
        }
        trials = loader.search_trials(criteria)
        print(f"  找到试验: {len(trials)} 个")
        
        for i, trial in enumerate(trials):
            print(f"    试验 {i+1}: 标签={'运动想象' if trial.get('label', 0) == 1 else '休息'}, "
                  f"质量={trial.get('data_quality', 0):.3f}, "
                  f"大小={trial.get('file_size_bytes', 0) / 1024:.2f}KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 HDF5数据保存修复测试")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 运行测试
    success = test_hdf5_fix()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    if success:
        print("✅ HDF5修复测试通过！")
        print("🎉 数据保存功能正常工作")
        return 0
    else:
        print("❌ HDF5修复测试失败")
        print("⚠️ 请检查错误信息和日志")
        return 1

if __name__ == "__main__":
    sys.exit(main())
