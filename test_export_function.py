#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据导出功能
Test Data Export Function

验证数据管理界面的导出功能是否正常工作

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.eeg_data_loader import EEGDataLoader
from ui.data_management_ui import DataExportWorker

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_export.log')
        ]
    )

def test_export_function():
    """测试导出功能"""
    print("🔧 测试数据导出功能")
    print("=" * 50)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 初始化数据加载器
        data_loader = EEGDataLoader(db_manager)
        
        # 测试患者ID
        test_patient_id = **********
        
        print(f"📊 测试导出患者 {test_patient_id} 的数据...")
        
        # 创建测试输出目录
        output_dir = Path("test_export_output")
        output_dir.mkdir(exist_ok=True)
        
        print(f"📁 输出目录: {output_dir.absolute()}")
        
        # 测试不同格式的导出
        formats = ['csv', 'hdf5', 'mat']
        
        for format_type in formats:
            print(f"\n🔄 测试 {format_type.upper()} 格式导出...")
            
            # 配置导出参数
            export_config = {
                'patient_ids': [test_patient_id],
                'output_dir': str(output_dir.absolute()),
                'format': format_type,
                'include_metadata': True,
                'compress': True
            }
            
            # 创建导出工作线程
            export_worker = DataExportWorker(data_loader, export_config)
            
            # 手动运行导出（不使用线程）
            try:
                export_worker.run()
                print(f"✅ {format_type.upper()} 格式导出测试完成")
            except Exception as e:
                print(f"❌ {format_type.upper()} 格式导出失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 检查输出文件
        print(f"\n📋 检查输出文件:")
        output_files = list(output_dir.glob("*"))
        
        if output_files:
            print(f"✅ 找到 {len(output_files)} 个输出文件:")
            for file_path in output_files:
                file_size = file_path.stat().st_size
                print(f"  📄 {file_path.name} ({file_size} bytes)")
                
                # 简单验证文件内容
                if file_path.suffix == '.csv':
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            print(f"    📊 CSV文件包含 {len(lines)} 行数据")
                            if len(lines) > 0:
                                print(f"    📝 表头: {lines[0].strip()}")
                    except Exception as e:
                        print(f"    ❌ 读取CSV文件失败: {e}")
                
                elif file_path.suffix == '.h5':
                    try:
                        import h5py
                        with h5py.File(file_path, 'r') as f:
                            print(f"    📊 HDF5文件包含组: {list(f.keys())}")
                    except Exception as e:
                        print(f"    ❌ 读取HDF5文件失败: {e}")
                
                elif file_path.suffix == '.mat':
                    try:
                        import scipy.io as sio
                        data = sio.loadmat(str(file_path))
                        keys = [k for k in data.keys() if not k.startswith('__')]
                        print(f"    📊 MAT文件包含变量: {keys}")
                    except Exception as e:
                        print(f"    ❌ 读取MAT文件失败: {e}")
        else:
            print("❌ 未找到任何输出文件")
            return False
        
        # 测试数据完整性
        print(f"\n🔍 验证导出数据完整性:")
        
        # 查询原始数据
        criteria = {
            'patient_ids': [test_patient_id],
            'limit': 1000
        }
        original_trials = data_loader.search_trials(criteria)
        print(f"📊 原始数据: {len(original_trials)} 个试验")
        
        # 检查CSV导出的数据
        csv_files = list(output_dir.glob("*metadata.csv"))
        if csv_files:
            csv_file = csv_files[0]
            try:
                import csv
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    exported_trials = list(reader)
                    print(f"📊 CSV导出: {len(exported_trials)} 个试验")
                    
                    if len(exported_trials) == len(original_trials):
                        print("✅ 数据完整性验证通过")
                    else:
                        print(f"⚠️ 数据数量不匹配: 原始{len(original_trials)} vs 导出{len(exported_trials)}")
            except Exception as e:
                print(f"❌ CSV数据验证失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    try:
        output_dir = Path("test_export_output")
        if output_dir.exists():
            import shutil
            shutil.rmtree(output_dir)
            print("🧹 清理测试文件完成")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

def main():
    """主函数"""
    print("🔧 数据导出功能测试")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 运行测试
    success = test_export_function()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    if success:
        print("✅ 数据导出功能测试通过！")
        print("🎉 导出功能正常工作")
        
        # 询问是否清理测试文件
        try:
            response = input("\n是否清理测试文件? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                cleanup_test_files()
        except:
            pass
        
        return 0
    else:
        print("❌ 数据导出功能测试失败")
        print("⚠️ 请检查错误信息和日志")
        return 1

if __name__ == "__main__":
    sys.exit(main())
