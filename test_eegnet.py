#!/usr/bin/env python3
"""
测试EEGNet模型功能
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tensorflow():
    """测试TensorFlow安装"""
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow 版本: {tf.__version__}")
        return True
    except ImportError as e:
        print(f"❌ TensorFlow 导入失败: {e}")
        return False

def test_eegnet_import():
    """测试EEGNet模型导入"""
    try:
        from core.eegnet_model import EEGNetModel, MotorImageryModel
        print("✅ EEGNet 模型导入成功")
        return True
    except ImportError as e:
        print(f"❌ EEGNet 模型导入失败: {e}")
        return False

def test_ml_adapter():
    """测试ML接口适配器"""
    try:
        from core.ml_model import MotorImageryModel, ModelManager
        print("✅ ML 接口适配器导入成功")
        
        # 创建模型
        model = MotorImageryModel('test_model')
        print(f"✅ 模型创建成功，使用EEGNet: {model.use_eegnet}")
        
        # 创建管理器
        manager = ModelManager()
        print(f"✅ 管理器创建成功，使用EEGNet: {manager.use_eegnet}")
        
        return True
    except Exception as e:
        print(f"❌ ML 接口适配器测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from core.ml_model import MotorImageryModel
        
        model = MotorImageryModel('test_model')
        
        # 测试添加训练数据
        test_data = np.random.randn(8, 250)  # 8通道，250个采样点
        success = model.add_training_data(test_data, 1)
        print(f"✅ 添加训练数据: {success}")
        
        # 添加更多数据
        for i in range(5):
            data = np.random.randn(8, 250)
            label = i % 2
            model.add_training_data(data, label)
        
        print(f"✅ 总训练数据: {len(model.training_data)} 个样本")
        
        # 测试模型信息
        info = model.get_model_info()
        print(f"✅ 模型信息: {info.name}, 样本数: {info.total_samples}")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("EEGNet 模型功能测试")
    print("=" * 60)
    
    tests = [
        ("TensorFlow 安装", test_tensorflow),
        ("EEGNet 模型导入", test_eegnet_import),
        ("ML 接口适配器", test_ml_adapter),
        ("基本功能", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   ❌ {test_name} 失败")
        except Exception as e:
            print(f"   ❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！EEGNet 功能正常")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    main()
