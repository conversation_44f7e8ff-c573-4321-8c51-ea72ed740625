#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轮次判断逻辑分析测试
Round Logic Analysis Test

分析轮次判断逻辑和移除模型对训练的影响

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


def test_round_logic():
    """测试轮次判断逻辑"""
    print("🧪 轮次判断逻辑分析")
    print("=" * 60)
    
    print("\n📋 轮次判断机制分析:")
    print("-" * 40)
    print("1. UI层轮次 (current_round):")
    print("   - 初始值: 0")
    print("   - 每次开始训练时 +1")
    print("   - 停止训练时重置为 0")
    print("   - 用于UI显示和训练器调用")
    
    print("\n2. 模型层轮次 (training_rounds):")
    print("   - 初始值: 0")
    print("   - 每次训练完成时 +1")
    print("   - 保存在模型信息中")
    print("   - 用于判断是否多轮训练")
    
    print("\n3. 轮次判断逻辑:")
    print("   - 第一轮: current_model == None 或 training_rounds == 0")
    print("   - 后续轮: current_model != None 且 training_rounds > 0")
    
    return True


def test_model_removal_impact():
    """测试移除模型对训练的影响"""
    print("\n📋 移除模型影响分析")
    print("=" * 60)
    
    try:
        # 场景1: 正常多轮训练
        print("\n🔍 场景1: 正常多轮训练")
        print("-" * 40)
        
        model1 = MotorImageryModel("Normal_Multi_Round")
        
        # 第一轮训练
        print("第1轮训练:")
        for i in range(10):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model1.add_training_data(data, label)
        
        config = TrainingConfig(epochs=2, batch_size=4, learning_rate=0.001)
        success1 = model1.train_model(config=config)
        
        if success1:
            model_info1 = model1.get_model_info()
            print(f"   - 训练轮次: {model_info1.training_rounds}")
            print(f"   - 总样本数: {model_info1.total_samples}")
            print(f"   - 是否已训练: {model1.is_trained}")
        
        # 第二轮训练
        print("第2轮训练:")
        for i in range(10):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model1.add_training_data(data, label)
        
        success2 = model1.train_model(config=config)
        
        if success2:
            model_info1 = model1.get_model_info()
            print(f"   - 训练轮次: {model_info1.training_rounds}")
            print(f"   - 总样本数: {model_info1.total_samples}")
            print(f"   - 判断为: {'多轮训练' if model_info1.training_rounds > 1 else '首次训练'}")
        
        # 场景2: 移除模型后重新训练
        print("\n🔍 场景2: 移除模型后重新训练")
        print("-" * 40)
        
        # 模拟移除模型（设置为None）
        print("移除当前模型...")
        removed_model = model1  # 保存引用用于对比
        current_model = None    # 模拟UI中的current_model = None
        
        # 创建新模型（模拟重新开始训练）
        print("创建新模型:")
        model2 = MotorImageryModel("After_Removal")
        
        # 添加数据并训练
        for i in range(10):
            data = np.random.randn(8, 250) * 50
            label = i % 2
            model2.add_training_data(data, label)
        
        success3 = model2.train_model(config=config)
        
        if success3:
            model_info2 = model2.get_model_info()
            print(f"   - 训练轮次: {model_info2.training_rounds}")
            print(f"   - 总样本数: {model_info2.total_samples}")
            print(f"   - 判断为: {'多轮训练' if model_info2.training_rounds > 1 else '首次训练'}")
        
        # 对比分析
        print("\n📊 对比分析:")
        print("-" * 40)
        print(f"移除前模型轮次: {removed_model.get_model_info().training_rounds}")
        print(f"新模型轮次: {model2.get_model_info().training_rounds}")
        print(f"结论: 移除模型后重新开始会被判断为第1轮")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_transfer_learning_scenarios():
    """测试迁移学习场景下的轮次判断"""
    print("\n📋 迁移学习场景分析")
    print("=" * 60)
    
    scenarios = [
        ("勾选迁移学习 + 第1轮", True, 1),
        ("勾选迁移学习 + 第2轮", True, 2),
        ("不勾选迁移学习 + 第1轮", False, 1),
        ("不勾选迁移学习 + 第2轮", False, 2),
    ]
    
    for scenario_name, use_transfer, round_num in scenarios:
        print(f"\n🔍 场景: {scenario_name}")
        print("-" * 30)
        
        try:
            model = MotorImageryModel(f"Scenario_{scenario_name.replace(' ', '_')}")
            
            # 设置迁移学习
            model_info = model.get_model_info()
            model_info.transfer_learning = use_transfer
            model_info.finetune_layers = 3
            
            # 模拟多轮训练
            for current_round in range(1, round_num + 1):
                print(f"   第{current_round}轮:")
                
                # 添加数据
                for i in range(8):
                    data = np.random.randn(8, 250) * 50
                    label = i % 2
                    model.add_training_data(data, label)
                
                # 训练
                config = TrainingConfig(epochs=1, batch_size=4, learning_rate=0.001)
                success = model.train_model(config=config)
                
                if success:
                    model_info = model.get_model_info()
                    training_rounds = model_info.training_rounds
                    
                    # 判断训练策略
                    if use_transfer:
                        if training_rounds == 1:
                            strategy = "使用预训练模型"
                        else:
                            strategy = "继续训练当前模型"
                    else:
                        if training_rounds == 1:
                            strategy = "从头创建新模型"
                        else:
                            strategy = "继续训练当前模型"
                    
                    print(f"     - 模型轮次: {training_rounds}")
                    print(f"     - 训练策略: {strategy}")
                    print(f"     - 样本总数: {model_info.total_samples}")
                else:
                    print(f"     - 训练失败")
            
        except Exception as e:
            print(f"     - 错误: {e}")
    
    return True


def analyze_removal_impact():
    """分析移除模型的具体影响"""
    print("\n📋 移除模型影响详细分析")
    print("=" * 60)
    
    print("\n🔍 移除模型的影响:")
    print("-" * 40)
    print("1. UI层面:")
    print("   - current_model 设置为 None")
    print("   - current_round 保持当前值（不重置）")
    print("   - 界面显示'无'模型")
    
    print("\n2. 训练判断:")
    print("   - 下次训练时 current_model == None")
    print("   - 被判断为'首次训练'")
    print("   - 会创建全新的模型")
    
    print("\n3. 迁移学习影响:")
    print("   勾选迁移学习时:")
    print("   - 第1轮: 使用预训练模型")
    print("   - 移除后: 重新使用预训练模型（重新开始）")
    print("   - 不会继续之前的训练")
    print("")
    print("   不勾选迁移学习时:")
    print("   - 第1轮: 创建新模型")
    print("   - 移除后: 重新创建新模型（重新开始）")
    print("   - 不会继续之前的训练")
    
    print("\n4. 数据影响:")
    print("   - 之前收集的训练数据会丢失")
    print("   - 需要重新收集数据")
    print("   - 模型权重完全重新开始")
    
    print("\n5. 轮次计数:")
    print("   - UI轮次: 继续计数（如第3轮、第4轮）")
    print("   - 模型轮次: 重新开始（第1轮）")
    print("   - 可能造成显示不一致")
    
    return True


def main():
    """主函数"""
    print("🧪 轮次判断逻辑和移除模型影响分析")
    print("=" * 70)
    print("分析目标:")
    print("1. 理解轮次判断机制")
    print("2. 分析移除模型的影响")
    print("3. 评估对迁移学习的影响")
    print("4. 提供使用建议")
    print()
    
    # 运行分析
    test1_success = test_round_logic()
    test2_success = test_model_removal_impact()
    test3_success = test_transfer_learning_scenarios()
    test4_success = analyze_removal_impact()
    
    print(f"\n📊 分析结果总结:")
    print(f"   轮次逻辑分析: {'✅ 完成' if test1_success else '❌ 失败'}")
    print(f"   移除影响测试: {'✅ 完成' if test2_success else '❌ 失败'}")
    print(f"   迁移学习场景: {'✅ 完成' if test3_success else '❌ 失败'}")
    print(f"   影响详细分析: {'✅ 完成' if test4_success else '❌ 失败'}")
    
    if all([test1_success, test2_success, test3_success, test4_success]):
        print("\n🎊 分析完成！")
        print("\n📋 关键结论:")
        print("✅ 轮次判断基于 current_model 是否为 None")
        print("✅ 移除模型会重置为第1轮训练")
        print("✅ 迁移学习会重新开始，不继续之前的训练")
        print("✅ 数据和权重会完全丢失")
        print("\n🎯 使用建议:")
        print("✅ 如果对第1轮结果不满意，可以安全移除重新开始")
        print("✅ 移除后的训练不会受到之前训练的影响")
        print("✅ 迁移学习会重新使用预训练模型")
        print("✅ 普通训练会重新创建模型")
        return True
    else:
        print("\n❌ 部分分析失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
