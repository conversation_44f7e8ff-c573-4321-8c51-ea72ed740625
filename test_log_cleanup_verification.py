#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志清理验证测试脚本

验证电流调节相关的日志信息已经从INFO级别降级为DEBUG级别

作者: NK系统开发团队
日期: 2024-12-19
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device import StimulationDevice

def setup_logging():
    """设置日志配置 - 只显示INFO及以上级别"""
    logging.basicConfig(
        level=logging.INFO,  # 只显示INFO及以上级别
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('log_cleanup_test.log', encoding='utf-8')
        ]
    )

def test_current_adjustment_logs():
    """测试电流调节日志清理效果"""
    logger = logging.getLogger(__name__)
    logger.info("=== 开始电流调节日志清理验证测试 ===")
    
    device = StimulationDevice()
    
    # 测试连接
    port_num = 7  # 根据实际情况调整
    success = device.connect(port_num)
    
    if not success:
        logger.error("设备连接失败，跳过测试")
        return
    
    logger.info("✅ 设备连接成功")
    
    # 模拟连续快速调节电流
    logger.info("🔄 开始连续电流调节测试...")
    logger.info("📝 注意：应该不再看到大量的电流设置成功日志")
    
    channel_num = 1
    current_values = [3, 5, 8, 12, 15, 18, 20, 15, 10, 5, 0]
    
    for i, current_value in enumerate(current_values):
        logger.info(f"第{i+1}次调节: 设置电流为{current_value}mA")
        
        if current_value == 0:
            # 停止刺激
            device.stop_stimulation(channel_num)
        else:
            # 设置电流（这里应该不再有INFO级别的日志输出）
            device.set_current(channel_num, current_value)
        
        time.sleep(0.3)  # 短暂间隔
    
    logger.info("✅ 电流调节测试完成")
    
    # 测试预刺激功能
    logger.info("🔄 开始预刺激功能测试...")
    logger.info("📝 注意：应该不再看到预刺激启动成功的INFO日志")
    
    for i, current_value in enumerate([5, 10, 15]):
        logger.info(f"预刺激测试{i+1}: 启动{current_value}mA预刺激")
        
        # 启动预刺激（这里应该不再有INFO级别的日志输出）
        device.start_pre_stimulation(channel_num, float(current_value), 2.0)
        
        time.sleep(2.5)  # 等待预刺激结束
    
    logger.info("✅ 预刺激测试完成")
    
    # 断开设备
    device.disconnect()
    logger.info("✅ 设备断开成功")

def test_debug_level_logs():
    """测试DEBUG级别日志是否正常工作"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试DEBUG级别日志 ===")
    
    # 临时设置DEBUG级别来验证日志确实存在
    debug_logger = logging.getLogger('core.stimulation_device')
    original_level = debug_logger.level
    debug_logger.setLevel(logging.DEBUG)
    
    logger.info("🔧 临时启用DEBUG级别日志...")
    
    device = StimulationDevice()
    
    if device.connect(7):
        logger.info("✅ 设备连接成功，现在应该能看到DEBUG级别的电流调节日志")
        
        # 设置电流（现在应该能看到DEBUG日志）
        device.set_current(1, 10.0)
        device.start_pre_stimulation(1, 10.0, 1.0)
        
        time.sleep(1.5)
        
        device.disconnect()
        logger.info("✅ DEBUG级别测试完成")
    else:
        logger.error("❌ 设备连接失败")
    
    # 恢复原始日志级别
    debug_logger.setLevel(original_level)
    logger.info("🔧 恢复原始日志级别")

def analyze_log_output():
    """分析日志输出，统计清理效果"""
    logger = logging.getLogger(__name__)
    logger.info("=== 分析日志清理效果 ===")
    
    try:
        with open('log_cleanup_test.log', 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 统计各种日志出现次数
        current_set_count = log_content.count("电流设置成功")
        pre_stim_success_count = log_content.count("预刺激启动成功")
        timer_start_count = log_content.count("预刺激定时器已启动")
        auto_stop_count = log_content.count("预刺激自动停止")
        
        logger.info(f"📊 日志统计结果:")
        logger.info(f"   - '电流设置成功' 出现次数: {current_set_count}")
        logger.info(f"   - '预刺激启动成功' 出现次数: {pre_stim_success_count}")
        logger.info(f"   - '预刺激定时器已启动' 出现次数: {timer_start_count}")
        logger.info(f"   - '预刺激自动停止' 出现次数: {auto_stop_count}")
        
        # 评估清理效果
        if current_set_count == 0 and pre_stim_success_count == 0:
            logger.info("✅ 日志清理成功！电流调节相关的INFO日志已被清除")
        else:
            logger.warning("⚠️ 日志清理不完整，仍有部分INFO级别的电流调节日志")
        
        if timer_start_count == 0 and auto_stop_count == 0:
            logger.info("✅ 预刺激定时器相关的INFO日志已被清除")
        else:
            logger.warning("⚠️ 预刺激定时器相关的INFO日志清理不完整")
            
    except FileNotFoundError:
        logger.error("❌ 日志文件未找到")
    except Exception as e:
        logger.error(f"❌ 分析日志文件时发生错误: {e}")

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始日志清理验证测试")
    logger.info("=" * 50)
    
    try:
        # 1. 测试电流调节日志清理
        test_current_adjustment_logs()
        
        # 2. 测试DEBUG级别日志
        test_debug_level_logs()
        
        # 3. 分析日志输出
        analyze_log_output()
        
        logger.info("=" * 50)
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
