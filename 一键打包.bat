@echo off
chcp 65001 >nul 2>&1
title NK脑机接口系统一键打包工具
cd /d "%~dp0"

echo ========================================
echo    NK脑机接口系统一键打包工具
echo    山东海天智能工程有限公司
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 检查打包工具...
python -c "import PyInstaller" > nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyInstaller未安装，正在安装...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装成功
) else (
    echo ✅ PyInstaller已安装
)
echo.

echo 🛠️ 选择打包方式:
echo 1. 智能打包 (推荐 - 只打包必要文件，排除用户数据)
echo 2. 快速打包 (基础版本)
echo 3. 高级打包 (包含优化)
echo 4. GUI打包工具 (图形界面)
echo 5. 退出
echo.
echo 💡 推荐使用智能打包，可大幅减少包大小并保护用户隐私
echo.

set /p choice=请输入选择 (1-5):

if "%choice%"=="1" goto smart_build
if "%choice%"=="2" goto basic_build
if "%choice%"=="3" goto advanced_build
if "%choice%"=="4" goto gui_build
if "%choice%"=="5" goto exit
goto invalid_choice

:smart_build
echo.
echo ========================================
echo 🧠 开始智能打包...
echo ========================================
python smart_build.py
goto build_complete

:basic_build
echo.
echo ========================================
echo 🚀 开始快速打包...
echo ========================================
python build_executable.py
goto build_complete

:advanced_build
echo.
echo ========================================
echo 🚀 开始高级打包...
echo ========================================
python advanced_build.py
goto build_complete

:gui_build
echo.
echo ========================================
echo 🎨 启动GUI打包工具...
echo ========================================
python -c "import auto_py_to_exe" > nul 2>&1
if errorlevel 1 (
    echo ⚠️ auto-py-to-exe未安装，正在安装...
    python -m pip install auto-py-to-exe
    if errorlevel 1 (
        echo ❌ auto-py-to-exe安装失败
        pause
        exit /b 1
    )
)
echo ✅ 启动GUI界面...
auto-py-to-exe
goto exit

:build_complete
echo.
if exist "dist" (
    echo ========================================
    echo 🎉 打包完成！
    echo ========================================
    echo 📁 构建文件位置: dist\
    echo 📦 安装包已创建
    echo.
    echo 📋 下一步操作:
    echo 1. 测试运行: 进入dist目录运行程序
    echo 2. 部署安装: 将安装包复制到目标机器
    echo 3. 解压运行: 解压后以管理员身份运行
    echo.
    echo 💡 提示:
    echo - 首次运行使用账户: admin / admin123
    echo - 厂家配置密码见"密码.txt"文件
    echo - 建议添加到杀毒软件白名单
    echo ========================================
    
    set /p open_folder=是否打开构建目录? (y/n): 
    if /i "%open_folder%"=="y" (
        explorer dist
    )
) else (
    echo ========================================
    echo ❌ 打包失败！
    echo ========================================
    echo 请检查错误信息并重试
    echo 常见问题:
    echo 1. 依赖包缺失 - 运行: python check_dependencies.py
    echo 2. 文件权限不足 - 以管理员身份运行
    echo 3. 路径包含中文 - 使用英文路径
    echo ========================================
)
pause
goto exit

:invalid_choice
echo ❌ 无效选择，请输入1-5
pause
echo.
goto menu

:menu
echo.
echo 🛠️ 选择打包方式:
echo 1. 智能打包 (推荐 - 只打包必要文件，排除用户数据)
echo 2. 快速打包 (基础版本)
echo 3. 高级打包 (包含优化)
echo 4. GUI打包工具 (图形界面)
echo 5. 退出
echo.
echo 💡 推荐使用智能打包，可大幅减少包大小并保护用户隐私
echo.

set /p choice=请输入选择 (1-5):

if "%choice%"=="1" goto smart_build
if "%choice%"=="2" goto basic_build
if "%choice%"=="3" goto advanced_build
if "%choice%"=="4" goto gui_build
if "%choice%"=="5" goto exit
goto invalid_choice

:exit
echo.
echo 感谢使用NK脑机接口系统打包工具！
pause
