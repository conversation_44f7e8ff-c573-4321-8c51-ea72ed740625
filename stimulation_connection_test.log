2025-06-03 09:10:59,196 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:10:59,197 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:10:59,199 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:10:59,199 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:10:59,199 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:10:59,200 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:10:59,200 - core.stimulation_device - DEBUG - 强制关闭端口成功 (尝试 1)
2025-06-03 09:10:59,200 - core.stimulation_device - DEBUG - 临时打开端口失败，错误码: -1
2025-06-03 09:10:59,201 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:11:00,203 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:11:00,910 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:11:01,034 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:01,035 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,036 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,037 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,038 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,065 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,065 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,065 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,066 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,081 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:11:01,270 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:11:01,271 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:11:01,272 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:11:01,272 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:11:01,286 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,286 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,287 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,287 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,288 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:01,301 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,301 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,301 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,302 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,316 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,316 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,332 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,332 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,333 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,333 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,348 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,348 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,363 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,363 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,364 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:01,364 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,365 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,379 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,379 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,395 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,395 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,396 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,396 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,410 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,410 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,411 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,411 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,441 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,441 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,442 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,442 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,457 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,458 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,472 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,472 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,473 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:01,474 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:01,596 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:11:01,596 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:02,099 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:02,099 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:02,099 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:11:02,100 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:11:03,101 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 09:11:03,813 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 09:11:03,937 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:03,937 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:03,938 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:03,938 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:03,939 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:03,952 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:03,953 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:03,968 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:03,968 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:03,969 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:03,969 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:03,983 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 09:11:04,172 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 09:11:04,173 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 09:11:04,174 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:11:04,174 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 09:11:04,188 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,188 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,189 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,189 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,190 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:04,190 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,190 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,219 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,219 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,220 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,220 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,235 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,235 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,251 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,251 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,266 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,266 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,267 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,267 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,268 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 09:11:04,282 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,282 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,298 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,298 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,299 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,299 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,314 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,314 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,329 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,329 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,345 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,345 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,346 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,346 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,360 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,360 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,376 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,377 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,377 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 09:11:04,378 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 09:11:04,485 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:11:04,485 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:04,992 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:04,993 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:04,994 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:11:04,995 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:11:04,996 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:11:04,996 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:11:04,997 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:05,501 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:05,502 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:05,503 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:11:05,504 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:11:05,505 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:11:05,507 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:05,508 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:05,510 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:11:05,510 - core.stimulation_device - DEBUG - 强制关闭端口成功 (尝试 1)
2025-06-03 09:11:05,511 - core.stimulation_device - DEBUG - 临时打开端口失败，错误码: -1
2025-06-03 09:11:05,511 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:11:05,512 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:06,015 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:06,016 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:06,017 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:11:06,018 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:11:06,019 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:11:06,020 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:06,525 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:06,526 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:06,527 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 09:11:06,528 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 09:11:06,529 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 09:11:06,531 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:11:06,532 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 09:11:06,532 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 09:11:06,533 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:11:06,533 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:11:06,533 - core.stimulation_device - DEBUG - 强制关闭端口成功 (尝试 1)
2025-06-03 09:11:06,534 - core.stimulation_device - DEBUG - 临时打开端口失败，错误码: -1
2025-06-03 09:11:06,534 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:11:07,036 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:11:07,038 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:11:07,039 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:11:07,040 - core.stimulation_device - DEBUG - 强制关闭端口成功 (尝试 1)
2025-06-03 09:11:07,040 - core.stimulation_device - DEBUG - 临时打开端口失败，错误码: -1
2025-06-03 09:11:07,041 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:11:07,544 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 09:11:07,544 - core.stimulation_device - ERROR - 连接电刺激设备失败，错误码: -1
2025-06-03 09:11:07,545 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 09:11:07,545 - core.stimulation_device - DEBUG - 强制关闭端口成功 (尝试 1)
2025-06-03 09:11:07,545 - core.stimulation_device - DEBUG - 临时打开端口失败，错误码: -1
2025-06-03 09:11:07,545 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 09:11:08,051 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 09:11:08,052 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 09:11:08,052 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 09:11:08,052 - core.stimulation_device - ERROR - 增强DLL重置失败: [WinError 6] 句柄无效。
2025-06-03 09:11:08,053 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
