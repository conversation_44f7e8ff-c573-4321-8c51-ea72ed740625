#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单配置测试
Simple Configuration Test
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    try:
        from utils.app_config import AppConfig
        
        config = AppConfig.STIMULATION_CONFIG
        
        print("电刺激配置测试:")
        print(f"频率: {config['default_frequency']} (类型: {type(config['default_frequency'])})")
        print(f"脉宽: {config['default_pulse_width']} (类型: {type(config['default_pulse_width'])})")
        print(f"最大电流: {config['max_current']} (类型: {type(config['max_current'])})")
        print(f"最小电流: {config['min_current']} (类型: {type(config['min_current'])})")
        print(f"电流步长: {config['current_step']} (类型: {type(config['current_step'])})")
        
        # 检查是否为整数
        all_int = all(isinstance(config[key], int) for key in [
            'default_frequency', 'default_pulse_width', 'max_current', 
            'min_current', 'current_step', 'default_relax_time',
            'default_climb_time', 'default_work_time', 'default_fall_time',
            'connection_timeout'
        ])
        
        print(f"\n所有参数都是整数: {'是' if all_int else '否'}")
        
        return 0
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
