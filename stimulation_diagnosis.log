2025-05-29 10:54:53,064 - INFO - 电刺激设备控制器初始化完成
2025-05-29 10:54:53,065 - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 10:54:53,067 - INFO - DLL函数原型定义完成
2025-05-29 10:54:53,067 - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 10:54:53,765 - INFO - 电刺激设备连接成功
2025-05-29 10:54:53,875 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:53,891 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,891 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, <PERSON>通道=0
2025-05-29 10:54:53,892 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,893 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:53,893 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,893 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:53,907 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,907 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:53,922 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,923 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:53,923 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:53,923 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:53,938 - INFO - 设备状态切换成功
2025-05-29 10:54:54,128 - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 10:54:54,130 - INFO - 状态监控线程启动
2025-05-29 10:54:54,143 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,143 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,159 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,159 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,160 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:54,160 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,160 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,175 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,175 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,176 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,176 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,190 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,190 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,206 - INFO - 刺激参数设置成功: 通道1, 频率25.0Hz
2025-05-29 10:54:54,222 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,222 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,237 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:54,238 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,238 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,239 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,239 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,252 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,252 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,253 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,254 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,268 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,269 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,299 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,299 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,319 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,322 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,323 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,324 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,327 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,328 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,362 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,363 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,363 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,364 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,364 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,364 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,364 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,365 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,377 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,377 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,398 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,398 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,399 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,399 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,409 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,410 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,425 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,425 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,425 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,430 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,441 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,442 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,442 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,442 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,463 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,501 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,502 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,502 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,503 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,503 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,504 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,504 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,504 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,505 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,505 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,505 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,518 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,518 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,519 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,519 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,533 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,533 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,548 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,548 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,548 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,548 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,563 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,563 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,579 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,579 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,579 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,579 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,595 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,595 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,610 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,610 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,610 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,610 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,641 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,641 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,641 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,642 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,642 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,642 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,673 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,673 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,674 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,674 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,674 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,674 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,704 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,704 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,705 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,705 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,705 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,706 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,706 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,706 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,736 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,736 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,752 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,753 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,753 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,753 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,753 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,754 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,783 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,783 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,784 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,785 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,799 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,799 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,799 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,800 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,815 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:54,815 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,816 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,831 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,831 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,832 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,832 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,846 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,846 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,878 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,878 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,894 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,894 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,894 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,895 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,910 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,910 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,911 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,911 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,926 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,926 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,942 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,942 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,943 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,943 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,958 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,958 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,973 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,973 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,973 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,973 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,989 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,989 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:54,989 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:54,990 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,004 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,004 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,020 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,021 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,022 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,022 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,035 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,036 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,051 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,052 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,053 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,053 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,066 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,066 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,067 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,068 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,082 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,082 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,098 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,098 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,098 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,099 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,113 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,113 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,129 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,129 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,129 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,129 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,144 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,144 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,159 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,159 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,160 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,160 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,175 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,176 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,176 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:55,176 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,177 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,191 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,191 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,207 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,207 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,207 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,207 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,223 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,223 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,254 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,255 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,255 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,256 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,269 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,270 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,285 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,286 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,286 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,287 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,301 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,302 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,316 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,316 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,316 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,317 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,332 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,332 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,333 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,333 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,348 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,348 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,364 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,364 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,365 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,366 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,380 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,380 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,396 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,396 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,397 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,397 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,411 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,412 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,426 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,426 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,426 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,427 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,442 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,442 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,442 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,442 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,458 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:54:55,458 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,459 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,474 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,475 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,475 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,476 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,505 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,506 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,507 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,507 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,508 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,508 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,535 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,535 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,535 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,536 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,551 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,552 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,566 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,566 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,567 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,567 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,582 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,582 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,597 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,597 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,597 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,597 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,613 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,613 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,613 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,613 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,628 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,628 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,644 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,644 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,645 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,645 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,659 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,659 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,674 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,674 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,674 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,675 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,690 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,690 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,706 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,707 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,708 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,708 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,722 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,723 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,723 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,724 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,737 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,738 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,753 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,753 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,753 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,754 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,768 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,769 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,784 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,784 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,785 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,785 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,800 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,800 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,800 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,800 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,815 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,815 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,830 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,830 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,831 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,831 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,846 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,846 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,861 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,861 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,861 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,862 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,877 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,877 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,877 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,878 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,892 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,892 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,908 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,908 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,908 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,909 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,923 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,923 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,939 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,939 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,939 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,940 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,954 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,954 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,969 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,970 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:55,970 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:55,971 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,000 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,001 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,001 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,002 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,002 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,003 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,031 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,031 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,031 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,032 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,047 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,047 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,062 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,062 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,062 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,063 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,078 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,078 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 12, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,078 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,079 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 12, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,094 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,094 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 12, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,110 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,110 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 12, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,111 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,112 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 12, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,125 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,125 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 13, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,141 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,142 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 13, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,142 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,143 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 13, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,157 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,158 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 13, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,158 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,159 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 13, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,172 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,172 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 14, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,188 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,189 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 14, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,189 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,190 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 14, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,204 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,204 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 14, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,220 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,220 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 14, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,221 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,221 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 15, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,235 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,235 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 15, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,250 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,250 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 15, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,251 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,252 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 15, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,266 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,266 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 15, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,267 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,268 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 16, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,281 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,281 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 16, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,297 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,297 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 16, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,298 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,298 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 16, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,313 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,314 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 16, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,329 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,329 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 17, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,330 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,330 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 17, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,345 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,345 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 17, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,346 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,346 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 17, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,361 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,361 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 17, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,376 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,376 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 18, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,377 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,377 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 18, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,391 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,391 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 18, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,407 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,407 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 18, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,407 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,408 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 18, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,422 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,422 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 19, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,423 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,423 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 19, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,437 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,437 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 19, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,453 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,454 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 19, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,455 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,456 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 19, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,469 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,470 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 20, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,484 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,485 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 20, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,486 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,487 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 20, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,499 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,499 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 20, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,515 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,515 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 20, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,516 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,516 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 21, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,531 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,532 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 21, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,532 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,532 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 21, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,547 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,547 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 21, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,563 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,563 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 21, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,564 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,565 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 22, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,579 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,579 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 22, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,594 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,594 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 22, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,595 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,595 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 22, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,609 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,609 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 22, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,609 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,610 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 23, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,625 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,625 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 23, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,641 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,641 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 23, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,641 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,641 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 23, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,657 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,657 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 23, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,687 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,688 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 24, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,689 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,689 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 24, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,690 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,691 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 24, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,691 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,692 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 24, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,718 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,719 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 24, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,734 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,734 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 25, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,734 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,734 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 25, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,749 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,750 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 25, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,764 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,764 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 25, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,765 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,765 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 25, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,780 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,780 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 26, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,795 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,795 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 26, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,796 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,796 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 26, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,810 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,810 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 26, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,811 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,811 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 26, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,826 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,827 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 27, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,842 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,842 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 27, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,843 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,843 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 27, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,858 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,858 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 27, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,873 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,874 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 27, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,874 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,875 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 28, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,888 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,889 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 28, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,889 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,890 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 28, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,904 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,905 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 28, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,920 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,920 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 28, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,921 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,921 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 29, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,936 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,936 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 29, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,952 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,953 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 29, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,953 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,954 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 29, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,967 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,967 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 29, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,968 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,969 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 30, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,983 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,984 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 30, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:56,999 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:56,999 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 30, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,000 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,000 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 30, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,014 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,014 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 30, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,029 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,029 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 31, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,030 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,030 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 31, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,044 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,044 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 31, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,060 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,060 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 31, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,060 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,061 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 31, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,076 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,076 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 32, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,077 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,077 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 32, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,092 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,092 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 32, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,107 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,107 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 32, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,108 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,108 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 32, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,123 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,123 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 33, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,138 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,139 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 33, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,139 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,140 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 33, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,154 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,155 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 33, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,156 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,157 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 33, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,170 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,170 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 34, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,185 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,186 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 34, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,187 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,188 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 34, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,201 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,201 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 34, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,231 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,231 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 34, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,231 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,233 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 35, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,233 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,233 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 35, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,233 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,233 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 35, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,263 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,263 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 35, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,279 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,279 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 35, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,279 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,280 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 36, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,294 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,294 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 36, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,310 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,310 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 36, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,311 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,311 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 36, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,325 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,325 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 36, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,341 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,341 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 37, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,341 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,342 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 37, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,357 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,357 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 37, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,357 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,358 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 37, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,373 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,374 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 37, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,389 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,389 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 38, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,389 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,390 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 38, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,405 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,405 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 38, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,420 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,421 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 38, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,421 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,422 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 38, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,436 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,436 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 39, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,437 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,437 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 39, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,451 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,451 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 39, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,466 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,466 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 39, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,467 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,467 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 39, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,482 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,482 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 40, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,498 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,498 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 40, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,498 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,499 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 40, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,513 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,513 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 40, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,514 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,514 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 40, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,529 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,529 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 41, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,544 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,545 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 41, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,545 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,545 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 41, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,560 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,560 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 41, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,576 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,577 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 41, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,577 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,578 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 42, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,592 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,592 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 42, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,608 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,608 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 42, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,609 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,609 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 42, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,624 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,624 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 42, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,625 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,625 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 43, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,640 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,640 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 43, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,655 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,655 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 43, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,655 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,655 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 43, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,670 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,672 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 43, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,686 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,687 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 44, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,688 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,689 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 44, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,702 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,702 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 44, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,703 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,703 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 44, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,717 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,717 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 44, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,732 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,733 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 45, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,733 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,733 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 45, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,748 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,748 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 45, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,763 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,763 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 45, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,764 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,764 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 45, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,779 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,779 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 46, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,780 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,781 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 46, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,794 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,794 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 46, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,810 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,810 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 46, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,811 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,811 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 46, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,826 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,826 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 47, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,841 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,841 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 47, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,841 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,842 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 47, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,857 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,857 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 47, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,873 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,874 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 47, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,874 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,875 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 48, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,888 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,889 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 48, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,890 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,890 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 48, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,919 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,919 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 48, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,934 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,934 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 48, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,934 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,935 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 49, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,950 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,950 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 49, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,965 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,965 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 49, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,965 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,966 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 49, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,980 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,980 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 49, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,980 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,981 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 50, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:57,995 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:57,996 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 50, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,011 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,011 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 50, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,011 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,012 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 50, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,027 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,027 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 50, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,043 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,043 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 51, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,043 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,044 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 51, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,058 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,058 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 51, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,058 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,059 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 51, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,073 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,074 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 51, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,089 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,089 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 52, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,090 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,091 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 52, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,104 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,105 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 52, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,119 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,120 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 52, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,121 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,122 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 52, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,134 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,135 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 53, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,150 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,150 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 53, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,150 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,151 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 53, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,164 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,164 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 53, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,165 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,165 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 53, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,180 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,180 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 54, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,195 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,195 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 54, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,196 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,196 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 54, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,212 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,212 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 54, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,227 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,227 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 54, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,227 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,228 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 55, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,243 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,243 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 55, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,243 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,244 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 55, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,259 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,259 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 55, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,274 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,274 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 55, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,275 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,275 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 56, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,290 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,291 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 56, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,305 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,306 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 56, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,307 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,307 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 56, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,321 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,321 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 56, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,322 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,322 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 57, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,337 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,338 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 57, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,352 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,353 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 57, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,353 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,354 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 57, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,368 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,369 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 57, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,399 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,400 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 58, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,400 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,401 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 58, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,401 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,402 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 58, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,430 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,430 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 58, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,431 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,431 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 58, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,445 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,445 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 59, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,446 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,446 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 59, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,461 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,461 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 59, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,477 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,477 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 59, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,478 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,478 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 59, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,492 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,492 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 60, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,508 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,508 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 60, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,509 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,509 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 60, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,523 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,524 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 60, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,524 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,525 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 60, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,539 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,540 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 61, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,554 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,555 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 61, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,555 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,556 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 61, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,569 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,570 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 61, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,585 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,586 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 61, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,587 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,588 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 62, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,601 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,601 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 62, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,601 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,601 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 62, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,616 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,616 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 62, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,631 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,631 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 62, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,631 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,632 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 63, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,647 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,648 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 63, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,663 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,663 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 63, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,663 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,664 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 63, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,678 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,678 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 63, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,693 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,694 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 64, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,694 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,694 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 64, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,709 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,709 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 64, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,709 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,709 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 64, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,724 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,724 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 64, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,739 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,739 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 65, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,740 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,740 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 65, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,754 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,754 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 65, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,771 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,771 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 65, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,772 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,773 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 65, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,787 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,787 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 66, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,788 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,788 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 66, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,802 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,803 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 66, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,817 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,817 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 66, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,818 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,818 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 66, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,833 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,833 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 67, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,849 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,850 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 67, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,851 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,851 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 67, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,865 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,865 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 67, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,865 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,865 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 67, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,880 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,880 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 68, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,896 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,896 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 68, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,897 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,897 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 68, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,927 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,927 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 68, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,928 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,928 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 68, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,928 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,928 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 69, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,958 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,958 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 69, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,973 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,974 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 69, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,974 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,975 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 69, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,976 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,976 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 69, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:58,976 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:58,976 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 70, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,005 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,006 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 70, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,020 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,020 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 70, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,021 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,021 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 70, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,036 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,036 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 70, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,052 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,053 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 71, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,053 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,054 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 71, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,067 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,068 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 71, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,068 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,069 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 71, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,084 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,084 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 71, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,098 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,099 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 72, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,099 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,099 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 72, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,114 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,114 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 72, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,129 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,129 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 72, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,129 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,130 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 72, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,145 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,145 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 73, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,161 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,161 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 73, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,161 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,162 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 73, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,175 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,175 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 73, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,176 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,176 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 73, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,191 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,191 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 74, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,207 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,208 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 74, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,208 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,209 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 74, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,223 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,223 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 74, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,239 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,239 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 74, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,239 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,240 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 75, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,254 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,255 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 75, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,255 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,256 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 75, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,269 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,270 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 75, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,285 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,286 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 75, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,286 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,287 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 76, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,300 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,300 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 76, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,315 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,315 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 76, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,316 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,316 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 76, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,331 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,331 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 76, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,331 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,331 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 77, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,346 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,346 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 77, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,361 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,361 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 77, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,362 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,362 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 77, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,377 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,377 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 77, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,392 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,392 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 78, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,392 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,393 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 78, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,423 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,423 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 78, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,424 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,424 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 78, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,425 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,425 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 78, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,454 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,454 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 79, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,455 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,455 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 79, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,456 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,456 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 79, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,484 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,485 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 79, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,485 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,486 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 79, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,499 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,501 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 80, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,515 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,515 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 80, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,516 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,516 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 80, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,530 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,530 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 80, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,531 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,531 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 80, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,546 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,546 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 81, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,561 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,562 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 81, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,562 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,563 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 81, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,577 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,577 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 81, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,592 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,592 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 81, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,593 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,593 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 82, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,608 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,608 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 82, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,608 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,609 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 82, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,624 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,624 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 82, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,639 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,639 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 82, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,639 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,639 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 83, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,654 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,655 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 83, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,669 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,670 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 83, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,671 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,671 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 83, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,685 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,685 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 83, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,701 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,701 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 84, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,701 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,702 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 84, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,715 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,715 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 84, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,715 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,716 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 84, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,731 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,731 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 84, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,747 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,747 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 85, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,748 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,748 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 85, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,762 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,762 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 85, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,778 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,778 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 85, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,779 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,779 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 85, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,793 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,793 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 86, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,793 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,794 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 86, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,808 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,808 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 86, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,824 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,824 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 86, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,825 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,825 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 86, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,840 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,840 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 87, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,856 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,856 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 87, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,857 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,857 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 87, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,871 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,872 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 87, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,872 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,873 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 87, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,902 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,903 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 88, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,903 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,904 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 88, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,905 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,906 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 88, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,933 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,934 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 88, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,949 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,949 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 88, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,950 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,950 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 89, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,964 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,964 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 89, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,979 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,979 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 89, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,979 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,980 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 89, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,994 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,994 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 89, 0, 0], A通道=0, B通道=0
2025-05-29 10:54:59,995 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:54:59,995 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 90, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,009 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,009 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 90, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,025 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,025 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 90, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,026 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,026 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 90, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,041 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,041 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 90, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,057 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,057 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 91, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,058 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,058 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 91, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,072 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,072 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 91, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,073 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,073 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 91, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,088 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,089 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 91, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,103 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,104 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 92, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,105 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,105 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 92, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,118 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,119 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 92, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,134 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,134 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 92, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,135 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,135 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 92, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,149 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,149 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 93, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,150 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,150 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 93, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,166 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,166 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 93, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,181 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,181 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 93, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,182 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,182 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 93, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,196 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,196 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 94, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,212 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,212 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 94, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,212 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,213 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 94, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,227 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,227 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 94, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,243 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,243 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 94, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,243 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,243 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 95, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,259 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,259 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 95, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,259 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,260 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 95, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,274 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,274 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 95, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,290 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,290 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 95, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,291 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,291 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 96, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,305 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,306 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 96, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,322 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,322 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 96, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,323 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,323 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 96, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,337 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,338 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 96, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,338 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,339 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 97, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,352 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,353 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 97, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,367 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,367 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 97, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,368 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,369 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 97, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,398 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,399 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 97, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,400 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,400 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 98, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,401 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,401 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 98, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,430 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,430 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 98, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,430 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,431 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 98, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,431 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,432 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 98, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,460 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,460 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 99, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,461 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,461 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 99, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,476 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,476 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 99, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,492 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,492 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 99, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,492 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,493 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 99, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,507 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,507 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 100, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,523 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,524 - INFO - ✅ A通道状态变化: 0(停止) -> 2(电流调节)
2025-05-29 10:55:00,524 - DEBUG - 设备状态数据(6个short): [21930, 1, 2, 0, 0, 0], A通道=2, B通道=0
2025-05-29 10:55:00,525 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,526 - DEBUG - 设备状态数据(6个short): [21930, 1, 2, 0, 0, 0], A通道=2, B通道=0
2025-05-29 10:55:00,539 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,539 - DEBUG - 设备状态数据(6个short): [21930, 1, 2, 1, 0, 0], A通道=2, B通道=0
2025-05-29 10:55:00,539 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,540 - DEBUG - 设备状态数据(6个short): [21930, 1, 2, 1, 0, 0], A通道=2, B通道=0
2025-05-29 10:55:00,554 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:55:00,554 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,554 - INFO - ✅ A通道状态变化: 2(电流调节) -> 0(停止)
2025-05-29 10:55:00,555 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,570 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,570 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,571 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,571 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,586 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,586 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,602 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,602 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,618 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,618 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,619 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,619 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,633 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,633 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,649 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,649 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,650 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,650 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,665 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,665 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,680 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,680 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,680 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,681 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,696 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,696 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,696 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,697 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,712 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,712 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,727 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,727 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,728 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,728 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,743 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,743 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,758 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,759 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,759 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,759 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,774 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,775 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,789 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,789 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,790 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,790 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,804 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,805 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,806 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,806 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,821 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,822 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,836 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,836 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,836 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,837 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,851 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,851 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,867 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,867 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,867 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,867 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,882 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,883 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,883 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,884 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,898 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,898 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,913 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,913 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,913 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,914 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,928 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,929 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,944 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,944 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,945 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,946 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,960 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,960 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,961 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,962 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,976 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,976 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,991 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,991 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:00,992 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:00,992 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,007 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,007 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,038 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,039 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,039 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,039 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,039 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,040 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,070 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,071 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,072 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,072 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,073 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,074 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,074 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,075 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,102 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,103 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,104 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,105 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,106 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,106 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,133 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:55:01,133 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,134 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,135 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,135 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,135 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,136 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,163 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,163 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,164 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,164 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,194 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,194 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,195 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,195 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,209 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,209 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,224 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,224 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 1, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,224 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,225 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,240 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,240 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,241 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,241 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,255 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,255 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,271 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,271 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 2, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,272 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,272 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,287 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,288 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,302 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,302 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,303 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,303 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,318 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,318 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 3, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,334 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,334 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,335 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,336 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,349 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,349 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,349 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,350 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,364 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,365 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 4, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,379 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,379 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,379 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,380 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,395 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,395 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,411 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,411 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,411 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,412 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 5, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,426 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,427 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,427 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,427 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,442 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,442 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,458 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,458 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,459 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,460 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 6, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,473 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,473 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,489 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,489 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,489 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,489 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,504 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,504 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,505 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,505 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 7, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,520 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,521 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,536 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,536 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,537 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,538 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,551 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,551 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,567 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,568 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 8, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,568 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,569 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,598 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,599 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,599 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,600 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,600 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,601 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,628 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,628 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 9, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,629 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,632 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,632 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,632 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,660 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,661 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,661 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,662 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,662 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,663 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 10, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,680 - INFO - 正在断开电刺激设备连接
2025-05-29 10:55:01,681 - INFO - 状态监控线程停止
2025-05-29 10:55:01,691 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,691 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,691 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,692 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,706 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,706 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 11, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,707 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:55:01,707 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,707 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,722 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,722 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,738 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,738 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,739 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,739 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,769 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,769 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,769 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,770 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,770 - DEBUG - 回调函数收到数据: nSize=0
2025-05-29 10:55:01,785 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,785 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,786 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,786 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,800 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,800 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,816 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,816 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,817 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,817 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,847 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,847 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,848 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,848 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,862 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,862 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,877 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,877 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:01,877 - DEBUG - 回调函数收到数据: nSize=6
2025-05-29 10:55:01,878 - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-05-29 10:55:02,001 - INFO - 电刺激设备断开成功
2025-05-29 10:55:02,002 - INFO - 正在断开电刺激设备连接
2025-05-29 10:55:02,002 - INFO - 电刺激设备断开成功
