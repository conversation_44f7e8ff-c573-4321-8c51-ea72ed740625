#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试治疗工作流程修复
Test Treatment Workflow Fixes

验证以下修复：
1. 运动想象触发后启动电刺激
2. 治疗时长检查使用配置文件而不是硬编码
3. 语音提示逻辑正确

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_min_duration_config():
    """测试最小治疗时长配置读取"""
    print("=== 测试最小治疗时长配置读取 ===")
    
    try:
        from utils.app_config import AppConfig
        
        # 检查配置值
        min_duration = AppConfig.DATABASE_CONFIG.get('min_treatment_duration', 3)
        print(f"配置文件中的最小治疗时长: {min_duration}分钟")
        
        # 测试治疗工作流程控制器的获取方法
        from core.treatment_workflow import TreatmentWorkflowController
        controller = TreatmentWorkflowController()
        
        # 测试获取最小时长的方法
        actual_min_duration = controller._get_min_treatment_duration_from_settings()
        print(f"工作流程控制器获取的最小时长: {actual_min_duration}分钟")
        
        if actual_min_duration == min_duration:
            print("✓ 最小治疗时长配置读取正确")
            return True
        else:
            print(f"✗ 配置读取不一致: 期望{min_duration}, 实际{actual_min_duration}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_treatment_duration_config():
    """测试治疗时长配置读取"""
    print("\n=== 测试治疗时长配置读取 ===")
    
    try:
        from utils.app_config import AppConfig
        
        # 检查配置值
        treatment_duration = AppConfig.DATABASE_CONFIG.get('treatment_duration', 20)
        print(f"配置文件中的治疗时长: {treatment_duration}分钟")
        
        # 模拟UI获取方法
        class MockTreatmentUI:
            def _get_treatment_duration_setting(self):
                from utils.app_config import AppConfig
                duration = AppConfig.DATABASE_CONFIG.get('treatment_duration', 20)
                return duration
        
        mock_ui = MockTreatmentUI()
        actual_duration = mock_ui._get_treatment_duration_setting()
        print(f"UI获取的治疗时长: {actual_duration}分钟")
        
        if actual_duration == treatment_duration:
            print("✓ 治疗时长配置读取正确")
            return True
        else:
            print(f"✗ 配置读取不一致: 期望{treatment_duration}, 实际{actual_duration}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_stimulation_trigger_logic():
    """测试电刺激触发逻辑"""
    print("\n=== 测试电刺激触发逻辑 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.trigger_calls = []
                
            def is_connected(self):
                return self.connected
                
            def trigger_stimulation(self, channel_num):
                self.trigger_calls.append(channel_num)
                print(f"模拟触发通道{channel_num}电刺激")
                return True
        
        # 设置模拟设备
        mock_device = MockStimulationDevice()
        controller.stimulation_device = mock_device
        
        # 测试启动刺激方法
        success = controller._start_stimulation()
        
        if success:
            print("✓ 电刺激启动成功")
            print(f"触发的通道: {mock_device.trigger_calls}")
            
            # 检查是否触发了A和B通道
            if 1 in mock_device.trigger_calls and 2 in mock_device.trigger_calls:
                print("✓ A和B通道都被触发")
                return True
            else:
                print("✗ 通道触发不完整")
                return False
        else:
            print("✗ 电刺激启动失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_workflow_state_transitions():
    """测试工作流程状态转换"""
    print("\n=== 测试工作流程状态转换 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController, TreatmentState
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 测试状态变化
        initial_state = controller.get_current_state()
        print(f"初始状态: {initial_state.value}")
        
        # 测试状态变化方法
        controller._change_state(TreatmentState.MOTOR_IMAGERY_DETECTION)
        current_state = controller.get_current_state()
        print(f"变化后状态: {current_state.value}")
        
        if current_state == TreatmentState.MOTOR_IMAGERY_DETECTION:
            print("✓ 状态转换正常")
            return True
        else:
            print("✗ 状态转换失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_configuration_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    
    try:
        from utils.app_config import AppConfig
        
        # 检查关键配置项
        configs_to_check = [
            ('DATABASE_CONFIG.min_treatment_duration', AppConfig.DATABASE_CONFIG.get('min_treatment_duration')),
            ('DATABASE_CONFIG.treatment_duration', AppConfig.DATABASE_CONFIG.get('treatment_duration')),
            ('TREATMENT_WORKFLOW_CONFIG.motor_imagery_timeout', AppConfig.TREATMENT_WORKFLOW_CONFIG.get('motor_imagery_timeout')),
        ]
        
        all_ok = True
        for config_name, config_value in configs_to_check:
            if config_value is not None:
                print(f"✓ {config_name}: {config_value}")
            else:
                print(f"✗ {config_name}: 未找到")
                all_ok = False
        
        return all_ok
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试治疗工作流程修复...")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("配置加载", test_configuration_loading),
        ("最小治疗时长配置", test_min_duration_config),
        ("治疗时长配置", test_treatment_duration_config),
        ("电刺激触发逻辑", test_stimulation_trigger_logic),
        ("工作流程状态转换", test_workflow_state_transitions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查代码修改。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
