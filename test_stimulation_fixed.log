2025-05-29 09:16:30,746 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:16:30,747 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:16:30,747 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:16:30,747 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:16:31,453 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:16:31,622 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:16:31,813 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:16:31,815 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:16:31,816 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:31,816 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:16:32,142 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:32,143 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:32,144 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:32,147 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:16:32,147 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:16:32,148 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:16:32,149 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:16:32,875 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:16:33,045 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:16:33,231 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:16:33,231 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:16:33,232 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:16:33,309 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:16:33,388 - core.stimulation_device - INFO - 通道 1 电流设置成功: 10.0mA (内部值: 100)
2025-05-29 09:16:33,465 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 10.0mA
2025-05-29 09:16:35,545 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:16:38,626 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:16:40,696 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:16:42,773 - core.stimulation_device - INFO - 所有刺激已停止
2025-05-29 09:16:42,774 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:42,775 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:16:43,098 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:43,099 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:43,100 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:43,102 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:16:43,102 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:16:43,103 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:16:43,104 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:16:43,811 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:16:43,981 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:16:44,166 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:16:44,166 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:16:44,167 - core.stimulation_device - INFO - 开始启动通道1刺激，电流8.0mA
2025-05-29 09:16:44,244 - core.stimulation_device - INFO - 设备状态切换为循环刺激模式
2025-05-29 09:16:44,322 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:16:44,323 - core.stimulation_device - INFO - 刺激参数设置完成
2025-05-29 09:16:44,400 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:16:44,400 - core.stimulation_device - INFO - 电流设置完成: 8.0mA
2025-05-29 09:16:44,477 - core.stimulation_device - INFO - 通道1刺激启动成功
2025-05-29 09:16:44,579 - core.stimulation_device - WARNING - 刺激状态验证失败，但启动命令已发送
2025-05-29 09:16:47,650 - core.stimulation_device - INFO - 通道 1 停止刺激
2025-05-29 09:16:47,651 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:47,652 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:16:47,976 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:47,976 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:47,977 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:47,977 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:16:47,977 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:16:47,978 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:16:47,978 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:16:48,691 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:16:48,861 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:16:49,049 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:16:49,050 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:16:49,050 - core.stimulation_device - INFO - 准备通道1刺激，电流8.0mA
2025-05-29 09:16:49,126 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:16:49,204 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:16:49,281 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 8.0mA
2025-05-29 09:16:49,282 - core.stimulation_device - INFO - 准备通道2刺激，电流6.0mA
2025-05-29 09:16:49,359 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:16:49,438 - core.stimulation_device - INFO - 通道 2 电流设置成功: 6.0mA (内部值: 60)
2025-05-29 09:16:49,517 - core.stimulation_device - INFO - 通道2电刺激准备完成，电流: 6.0mA
2025-05-29 09:16:49,593 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:16:51,675 - core.stimulation_device - INFO - 通道2电刺激触发成功
2025-05-29 09:16:53,754 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:16:54,825 - core.stimulation_device - INFO - 通道2电刺激暂停成功
2025-05-29 09:16:55,900 - core.stimulation_device - INFO - 所有刺激已停止
2025-05-29 09:16:55,900 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:55,902 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:16:56,226 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:56,227 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:16:56,227 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:16:56,230 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:16:56,230 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:16:56,231 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:16:56,232 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:16:56,943 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:16:57,114 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:16:57,302 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:16:57,302 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:16:57,303 - core.stimulation_device - INFO - 准备通道1刺激，电流12.0mA
2025-05-29 09:16:57,380 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:16:57,457 - core.stimulation_device - INFO - 通道 1 电流设置成功: 12.0mA (内部值: 120)
2025-05-29 09:16:57,534 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 12.0mA
2025-05-29 09:16:58,606 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:16:59,681 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:17:00,758 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:17:01,829 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:17:02,903 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:17:03,977 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:17:05,045 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:17:06,115 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:17:07,187 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:17:08,257 - core.stimulation_device - INFO - 所有刺激已停止
2025-05-29 09:17:08,257 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:17:08,258 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:17:08,570 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:17:08,571 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:17:08,571 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:17:08,573 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:17:08,574 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:17:08,575 - core.stimulation_device - ERROR - 设备未连接，无法准备刺激
2025-05-29 09:17:08,577 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchChannelState
2025-05-29 09:17:08,577 - core.stimulation_device - ERROR - 通道1电刺激触发失败: 未知错误码: None。建议：请联系技术支持
2025-05-29 09:17:08,578 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:17:08,579 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:17:08,580 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:17:09,289 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:17:09,460 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:17:09,645 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:17:09,645 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:17:09,646 - core.stimulation_device - INFO - 准备通道1刺激，电流100.0mA
2025-05-29 09:17:09,721 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:17:09,721 - core.stimulation_device - ERROR - 电流值超出安全范围: 100.0mA, 允许范围: 0.1-30.0mA
2025-05-29 09:17:09,722 - core.stimulation_device - ERROR - 电流设置失败
2025-05-29 09:17:09,723 - core.stimulation_device - ERROR - 通道5电刺激触发失败: 命令错误 - 不支持的命令或通道号。建议：检查通道号是否正确（1或2）
2025-05-29 09:17:09,724 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:17:09,724 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:17:10,027 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:17:10,028 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:17:10,028 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:11,888 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:11,889 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:11,891 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:11,891 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:12,605 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:12,789 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:12,970 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:12,970 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:12,970 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:12,970 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:13,288 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:13,993 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:14,162 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:14,340 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:14,340 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:14,340 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:30:14,427 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:14,506 - core.stimulation_device - INFO - 通道 1 电流设置成功: 10.0mA (内部值: 100)
2025-05-29 09:30:14,590 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 10.0mA
2025-05-29 09:30:16,656 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:19,722 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:21,805 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:23,810 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:30:23,887 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:30:23,950 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:30:24,121 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:30:24,122 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:24,123 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:24,436 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:25,140 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:25,323 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:25,524 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:25,524 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:25,524 - core.stimulation_device - INFO - 开始启动通道1刺激，电流8.0mA
2025-05-29 09:30:25,524 - core.stimulation_device - INFO - 重置所有通道状态
2025-05-29 09:30:25,872 - core.stimulation_device - INFO - 设备状态切换为循环刺激模式
2025-05-29 09:30:25,940 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:25,940 - core.stimulation_device - INFO - 刺激参数设置完成
2025-05-29 09:30:26,023 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:30:26,023 - core.stimulation_device - INFO - 电流设置完成: 8.0mA
2025-05-29 09:30:26,089 - core.stimulation_device - INFO - 通道1刺激启动成功
2025-05-29 09:30:26,190 - core.stimulation_device - WARNING - 刺激状态验证失败，但启动命令已发送
2025-05-29 09:30:29,194 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:30:29,270 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:30:29,343 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:30:29,522 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:30:31,527 - core.stimulation_device - INFO - 开始启动通道1刺激，电流10.0mA
2025-05-29 09:30:31,528 - core.stimulation_device - INFO - 重置所有通道状态
2025-05-29 09:30:31,871 - core.stimulation_device - INFO - 设备状态切换为循环刺激模式
2025-05-29 09:30:31,942 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:31,942 - core.stimulation_device - INFO - 刺激参数设置完成
2025-05-29 09:30:32,022 - core.stimulation_device - INFO - 通道 1 电流设置成功: 10.0mA (内部值: 100)
2025-05-29 09:30:32,022 - core.stimulation_device - INFO - 电流设置完成: 10.0mA
2025-05-29 09:30:32,089 - core.stimulation_device - INFO - 通道1刺激启动成功
2025-05-29 09:30:32,194 - core.stimulation_device - WARNING - 刺激状态验证失败，但启动命令已发送
2025-05-29 09:30:34,196 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:30:34,270 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:30:34,339 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:30:34,504 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:30:34,504 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:34,504 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:34,809 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:34,811 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:34,812 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:34,812 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:34,812 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:34,812 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:34,817 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:35,506 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:35,671 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:35,855 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:35,855 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:35,855 - core.stimulation_device - INFO - 准备通道1刺激，电流8.0mA
2025-05-29 09:30:35,924 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:35,988 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:30:36,056 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 8.0mA
2025-05-29 09:30:36,056 - core.stimulation_device - INFO - 准备通道2刺激，电流6.0mA
2025-05-29 09:30:36,139 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:36,204 - core.stimulation_device - INFO - 通道 2 电流设置成功: 6.0mA (内部值: 60)
2025-05-29 09:30:36,293 - core.stimulation_device - INFO - 通道2电刺激准备完成，电流: 6.0mA
2025-05-29 09:30:36,372 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:38,455 - core.stimulation_device - INFO - 通道2电刺激触发成功
2025-05-29 09:30:40,521 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:41,590 - core.stimulation_device - INFO - 通道2电刺激暂停成功
2025-05-29 09:30:42,593 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:30:42,663 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:30:42,744 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:30:42,923 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:30:42,923 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:42,923 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:43,228 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:43,939 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:44,122 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:44,304 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:44,304 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:44,304 - core.stimulation_device - INFO - 准备通道1刺激，电流12.0mA
2025-05-29 09:30:44,372 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:44,455 - core.stimulation_device - INFO - 通道 1 电流设置成功: 12.0mA (内部值: 120)
2025-05-29 09:30:44,526 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 12.0mA
2025-05-29 09:30:45,605 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:46,686 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:47,755 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:48,823 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:49,890 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:50,957 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:52,024 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:53,103 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:30:54,173 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:30:55,177 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:30:55,247 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:30:55,321 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:30:55,494 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:30:55,494 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:55,494 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:55,825 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:55,825 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:55,827 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:55,828 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:30:55,828 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:30:55,828 - core.stimulation_device - ERROR - 设备未连接，无法准备刺激
2025-05-29 09:30:55,828 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchChannelState
2025-05-29 09:30:55,828 - core.stimulation_device - ERROR - 通道1电刺激触发失败: 未知错误码: None。建议：请联系技术支持
2025-05-29 09:30:55,833 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:30:55,834 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:30:55,834 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:30:56,523 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:30:56,710 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:30:56,886 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:30:56,886 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:30:56,886 - core.stimulation_device - INFO - 准备通道1刺激，电流100.0mA
2025-05-29 09:30:56,956 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:30:56,956 - core.stimulation_device - ERROR - 电流值超出安全范围: 100.0mA, 允许范围: 0.1-30.0mA
2025-05-29 09:30:56,956 - core.stimulation_device - ERROR - 电流设置失败
2025-05-29 09:30:56,956 - core.stimulation_device - ERROR - 通道5电刺激触发失败: 命令错误 - 不支持的命令或通道号。建议：检查通道号是否正确（1或2）
2025-05-29 09:30:56,956 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:56,956 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:30:57,272 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:30:57,272 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:30:57,272 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:26,113 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:35:26,113 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:35:26,113 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:35:26,113 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:35:26,824 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:35:27,007 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:35:27,190 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:35:27,190 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:35:27,190 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:27,190 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:35:27,489 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:27,489 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:27,489 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:27,489 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:35:27,494 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:35:27,495 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:35:27,497 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:35:28,190 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:35:28,357 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:35:28,540 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:35:28,540 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:35:28,540 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:35:28,622 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:35:28,689 - core.stimulation_device - INFO - 通道 1 电流设置成功: 10.0mA (内部值: 100)
2025-05-29 09:35:28,773 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 10.0mA
2025-05-29 09:35:30,856 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:35:33,938 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:35:36,013 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:35:38,014 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:35:38,090 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:35:38,157 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:35:38,338 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:35:38,338 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:38,338 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:35:38,629 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:35:39,340 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:35:39,506 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:35:39,705 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:35:39,705 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:35:39,705 - core.stimulation_device - INFO - 开始启动通道1刺激，电流8.0mA
2025-05-29 09:35:39,705 - core.stimulation_device - INFO - 重置所有通道状态
2025-05-29 09:35:40,023 - core.stimulation_device - INFO - 设备状态切换为循环刺激模式
2025-05-29 09:35:40,090 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:35:40,090 - core.stimulation_device - INFO - 刺激参数设置完成
2025-05-29 09:35:40,172 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:35:40,172 - core.stimulation_device - INFO - 电流设置完成: 8.0mA
2025-05-29 09:35:40,245 - core.stimulation_device - INFO - 通道1刺激启动成功
2025-05-29 09:35:40,347 - core.stimulation_device - WARNING - 刺激状态验证失败，但启动命令已发送
2025-05-29 09:35:43,348 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:35:43,424 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:35:43,506 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:35:43,672 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:35:45,678 - core.stimulation_device - INFO - 开始启动通道1刺激，电流10.0mA
2025-05-29 09:35:45,679 - core.stimulation_device - INFO - 重置所有通道状态
2025-05-29 09:35:46,005 - core.stimulation_device - INFO - 设备状态切换为循环刺激模式
2025-05-29 09:35:46,073 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:35:46,073 - core.stimulation_device - INFO - 刺激参数设置完成
2025-05-29 09:35:46,140 - core.stimulation_device - INFO - 通道 1 电流设置成功: 10.0mA (内部值: 100)
2025-05-29 09:35:46,140 - core.stimulation_device - INFO - 电流设置完成: 10.0mA
2025-05-29 09:35:46,228 - core.stimulation_device - INFO - 通道1刺激启动成功
2025-05-29 09:35:46,330 - core.stimulation_device - WARNING - 刺激状态验证失败，但启动命令已发送
2025-05-29 09:35:48,332 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:35:48,407 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:35:48,475 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:35:48,641 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:35:48,641 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:48,641 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:35:48,944 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:48,944 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:48,945 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:48,947 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:35:48,947 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:35:48,947 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:35:48,947 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:35:49,638 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:35:49,805 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:35:49,990 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:35:49,990 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:35:49,990 - core.stimulation_device - INFO - 准备通道1刺激，电流8.0mA
2025-05-29 09:35:50,076 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:35:50,157 - core.stimulation_device - INFO - 通道 1 电流设置成功: 8.0mA (内部值: 80)
2025-05-29 09:35:50,239 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 8.0mA
2025-05-29 09:35:50,239 - core.stimulation_device - INFO - 准备通道2刺激，电流6.0mA
2025-05-29 09:35:50,307 - core.stimulation_device - INFO - 刺激参数设置成功: 通道2, 频率20.0Hz
2025-05-29 09:35:50,374 - core.stimulation_device - INFO - 通道 2 电流设置成功: 6.0mA (内部值: 60)
2025-05-29 09:35:50,443 - core.stimulation_device - INFO - 通道2电刺激准备完成，电流: 6.0mA
2025-05-29 09:35:50,523 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:35:52,605 - core.stimulation_device - INFO - 通道2电刺激触发成功
2025-05-29 09:35:54,684 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:35:55,756 - core.stimulation_device - INFO - 通道2电刺激暂停成功
2025-05-29 09:35:56,761 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:35:56,839 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:35:56,908 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:35:57,073 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:35:57,073 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:57,073 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:35:57,389 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:57,389 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:35:57,389 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:35:57,389 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:35:57,394 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:35:57,396 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:35:57,396 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:35:58,073 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:35:58,243 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:35:58,422 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:35:58,422 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:35:58,422 - core.stimulation_device - INFO - 准备通道1刺激，电流12.0mA
2025-05-29 09:35:58,493 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:35:58,562 - core.stimulation_device - INFO - 通道 1 电流设置成功: 12.0mA (内部值: 120)
2025-05-29 09:35:58,643 - core.stimulation_device - INFO - 通道1电刺激准备完成，电流: 12.0mA
2025-05-29 09:35:59,707 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:36:00,795 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:36:01,872 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:36:02,946 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:36:04,021 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:36:05,090 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:36:06,169 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:36:07,244 - core.stimulation_device - INFO - 通道1电刺激暂停成功
2025-05-29 09:36:08,324 - core.stimulation_device - INFO - 通道1电刺激触发成功
2025-05-29 09:36:09,328 - core.stimulation_device - INFO - 停止所有通道刺激
2025-05-29 09:36:09,394 - core.stimulation_device - INFO - 通道1已停止
2025-05-29 09:36:09,472 - core.stimulation_device - INFO - 通道2已停止
2025-05-29 09:36:09,658 - core.stimulation_device - INFO - 设备已切换到空闲状态，所有刺激已停止
2025-05-29 09:36:09,658 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:36:09,658 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 准备通道1刺激，电流10.0mA
2025-05-29 09:36:09,945 - core.stimulation_device - ERROR - 设备未连接，无法准备刺激
2025-05-29 09:36:09,945 - core.stimulation_device - ERROR - DLL未加载，无法调用 SwitchChannelState
2025-05-29 09:36:09,945 - core.stimulation_device - ERROR - 通道1电刺激触发失败: 未知错误码: None。建议：请联系技术支持
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-05-29 09:36:09,945 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-05-29 09:36:10,649 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-05-29 09:36:10,821 - core.stimulation_device - INFO - 设备状态切换成功
2025-05-29 09:36:11,004 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-05-29 09:36:11,005 - core.stimulation_device - INFO - 状态监控线程启动
2025-05-29 09:36:11,005 - core.stimulation_device - INFO - 准备通道1刺激，电流100.0mA
2025-05-29 09:36:11,069 - core.stimulation_device - INFO - 刺激参数设置成功: 通道1, 频率20.0Hz
2025-05-29 09:36:11,069 - core.stimulation_device - ERROR - 电流值超出安全范围: 100.0mA, 允许范围: 0.1-30.0mA
2025-05-29 09:36:11,070 - core.stimulation_device - ERROR - 电流设置失败
2025-05-29 09:36:11,070 - core.stimulation_device - ERROR - 通道5电刺激触发失败: 命令错误 - 不支持的命令或通道号。建议：检查通道号是否正确（1或2）
2025-05-29 09:36:11,070 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:36:11,070 - core.stimulation_device - INFO - 状态监控线程停止
2025-05-29 09:36:11,395 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-05-29 09:36:11,395 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-05-29 09:36:11,396 - core.stimulation_device - INFO - 电刺激设备断开成功
