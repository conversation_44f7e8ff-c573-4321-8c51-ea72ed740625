#!/usr/bin/env python3
"""
测试difficulty_level属性修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_difficulty_level_fix():
    """测试difficulty_level属性修复"""
    print("=" * 60)
    print("测试difficulty_level属性修复")
    print("=" * 60)
    
    try:
        print("1. 测试ModelInfo的difficulty_level属性...")
        from core.eegnet_model import ModelInfo, ModelPerformance
        
        # 创建模型信息实例
        model_info = ModelInfo(
            name="Test_Difficulty_Model",
            version=1,
            created_time=1234567890,
            last_updated=1234567890,
            training_rounds=1,
            total_samples=20,
            performance=None
        )
        
        print(f"   ✅ ModelInfo创建成功: {model_info.name}")
        
        # 测试difficulty_level属性
        try:
            difficulty = model_info.difficulty_level
            print(f"   ✅ difficulty_level属性访问成功: {difficulty}")
        except AttributeError as e:
            print(f"   ❌ difficulty_level属性访问失败: {e}")
            return False
        
        # 测试设置difficulty_level
        try:
            model_info.difficulty_level = 3
            print(f"   ✅ difficulty_level设置成功: {model_info.difficulty_level}")
        except Exception as e:
            print(f"   ❌ difficulty_level设置失败: {e}")
            return False
        
        print("\n2. 测试EEGNet模型的difficulty调整...")
        from core.ml_model import MotorImageryModel
        
        model = MotorImageryModel("Test_Difficulty_Model")
        print(f"   ✅ 模型创建成功: {model.model_name}")
        
        # 测试获取模型信息
        info = model.get_model_info()
        print(f"   ✅ 模型信息获取成功: {info.name}")
        
        # 测试difficulty_level属性
        try:
            difficulty = info.difficulty_level
            print(f"   ✅ 模型info.difficulty_level访问成功: {difficulty}")
        except AttributeError as e:
            print(f"   ❌ 模型info.difficulty_level访问失败: {e}")
            return False
        
        print("\n3. 测试adjust_difficulty方法...")
        
        # 测试调整难度等级
        try:
            model.adjust_difficulty(2)
            new_difficulty = model.get_model_info().difficulty_level
            print(f"   ✅ adjust_difficulty(2)成功: {new_difficulty}")
        except Exception as e:
            print(f"   ❌ adjust_difficulty失败: {e}")
            return False
        
        # 测试不同的难度等级
        for level in [1, 3, 5]:
            try:
                model.adjust_difficulty(level)
                current_level = model.get_model_info().difficulty_level
                print(f"   ✅ 难度等级{level}设置成功: {current_level}")
            except Exception as e:
                print(f"   ❌ 难度等级{level}设置失败: {e}")
                return False
        
        print("\n4. 测试边界值...")
        
        # 测试无效的难度等级
        try:
            model.adjust_difficulty(0)  # 无效值
            model.adjust_difficulty(6)  # 无效值
            print("   ✅ 无效难度等级处理正常")
        except Exception as e:
            print(f"   ❌ 无效难度等级处理失败: {e}")
            return False
        
        print("\n5. 测试模型加载后的difficulty_level...")
        
        # 添加一些训练数据
        import numpy as np
        for i in range(10):
            data = np.random.randn(8, 250) * 100
            label = i % 2
            model.add_training_data(data, label)
        
        # 保存模型
        from core.ml_model import ModelManager
        manager = ModelManager()
        save_success = manager.save_model(model, "test_difficulty_model")
        
        if save_success:
            print("   ✅ 模型保存成功")
            
            # 加载模型
            loaded_model = manager.load_model("test_difficulty_model")
            if loaded_model:
                print("   ✅ 模型加载成功")
                
                # 测试加载后的difficulty_level
                try:
                    loaded_info = loaded_model.get_model_info()
                    difficulty = loaded_info.difficulty_level
                    print(f"   ✅ 加载后difficulty_level访问成功: {difficulty}")
                    
                    # 测试加载后的调整
                    loaded_model.adjust_difficulty(4)
                    new_difficulty = loaded_model.get_model_info().difficulty_level
                    print(f"   ✅ 加载后adjust_difficulty成功: {new_difficulty}")
                    
                except AttributeError as e:
                    print(f"   ❌ 加载后difficulty_level访问失败: {e}")
                    return False
            else:
                print("   ❌ 模型加载失败")
                return False
        else:
            print("   ❌ 模型保存失败")
            return False
        
        print("\n6. 测试深度学习参数完整性...")
        
        # 测试所有深度学习参数
        dl_params = [
            'temperature',
            'decision_threshold', 
            'confidence_threshold',
            'class_weight_ratio',
            'smoothing_window',
            'adaptive_learning',
            'transfer_learning',
            'finetune_layers',
            'difficulty_level'
        ]
        
        info = model.get_model_info()
        missing_params = []
        
        for param in dl_params:
            if not hasattr(info, param):
                missing_params.append(param)
            else:
                value = getattr(info, param)
                print(f"   ✅ {param}: {value}")
        
        if missing_params:
            print(f"   ❌ 缺少参数: {missing_params}")
            return False
        else:
            print("   ✅ 所有深度学习参数都存在")
        
        print("\n" + "=" * 60)
        print("🎉 difficulty_level属性修复测试完成！")
        print("=" * 60)
        
        # 总结
        print("\n📊 修复结果:")
        print("✅ ModelInfo.difficulty_level - 属性存在")
        print("✅ 模型信息访问 - 正常工作")
        print("✅ adjust_difficulty方法 - 正常工作")
        print("✅ 边界值处理 - 正常工作")
        print("✅ 模型保存加载 - 正常工作")
        print("✅ 深度学习参数 - 完整存在")
        
        print("\n🎯 修复状态:")
        print("- 'ModelInfo' object has no attribute 'difficulty_level' 错误已修复")
        print("- 深度学习参数调整功能完全正常")
        print("- 敏感度级别控制正常工作")
        print("- 系统已准备好进行参数调整")
        
        print("\n💡 使用建议:")
        print("1. 现在可以正常调整敏感度级别")
        print("2. 所有深度学习参数都可以实时调整")
        print("3. 模型保存和加载包含完整参数")
        print("4. 系统稳定性得到提升")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_difficulty_level_fix()
    if success:
        print("\n🎯 difficulty_level属性修复成功！深度学习参数调整功能完全正常！")
    else:
        print("\n⚠️  修复仍有问题，需要进一步调试。")
    
    sys.exit(0 if success else 1)
