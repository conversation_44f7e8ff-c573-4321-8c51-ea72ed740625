#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的上传功能
Test Improved Upload Functionality

测试减少重试次数和添加进度指示器的功能

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader
from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_improved_upload():
    """测试改进后的上传功能"""
    print("🚀 测试改进后的上传功能")
    print("=" * 50)
    
    # 1. 检查配置修改
    print("\n1. 检查重试次数配置...")
    retry_count = AppConfig.NETWORK_CONFIG['http']['retry_count']
    print(f"✅ 重试次数已设置为: {retry_count} 次")
    
    if retry_count == 1:
        print("✅ 重试次数配置正确！")
    else:
        print(f"⚠️ 重试次数应该是1，当前是{retry_count}")
    
    # 2. 初始化组件
    print("\n2. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 3. 获取医院信息
    hospital_info = db_manager.get_hospital_info()
    
    # 4. 创建测试患者数据
    print("\n3. 创建测试患者数据...")
    test_patient = {
        'bianhao': int(time.time()) % 1000000,  # 使用时间戳生成唯一编号
        'name': '改进测试患者',
        'age': 35,
        'xingbie': '女',
        'cardid': '110101199001011234',
        'zhenduan': '改进测试诊断',
        'bingshi': '改进测试既往史',
        'brhc': '右侧',
        'zhuzhi': '改进测试医生',
        'czy': '改进测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1),
        'status': 'pending'
    }
    
    print(f"患者编号: {test_patient['bianhao']}")
    print(f"患者姓名: {test_patient['name']}")
    
    # 5. 测试快速上传（减少重试次数）
    print("\n4. 测试快速上传...")
    start_time = time.time()
    
    try:
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        end_time = time.time()
        upload_duration = end_time - start_time
        
        print(f"⏱️ 上传耗时: {upload_duration:.2f} 秒")
        
        if upload_result.success:
            print(f"✅ 上传成功: {upload_result.message}")
        else:
            if "重复或已存在" in upload_result.message:
                print(f"ℹ️ 数据重复: {upload_result.message}")
            else:
                print(f"⚠️ 上传失败: {upload_result.message}")
        
        # 验证上传时间是否合理
        if upload_duration < 5.0:  # 应该在5秒内完成（包括重试）
            print("✅ 上传速度符合预期！")
        else:
            print(f"⚠️ 上传时间过长: {upload_duration:.2f}秒")
        
    except Exception as e:
        end_time = time.time()
        upload_duration = end_time - start_time
        print(f"❌ 上传测试失败: {e}")
        print(f"⏱️ 失败耗时: {upload_duration:.2f} 秒")
    
    # 6. 测试网络连接失败的情况
    print("\n5. 测试网络连接失败情况...")
    
    # 临时修改URL为无效地址
    original_base_url = uploader.base_url
    uploader.base_url = "http://invalid-url-for-testing.com/"
    
    start_time = time.time()
    try:
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        end_time = time.time()
        failure_duration = end_time - start_time
        
        print(f"⏱️ 失败耗时: {failure_duration:.2f} 秒")
        print(f"📊 失败原因: {upload_result.message}")
        
        # 验证失败时间是否合理（应该在3秒内失败，因为只重试1次）
        if failure_duration < 5.0:
            print("✅ 失败响应速度符合预期！")
        else:
            print(f"⚠️ 失败响应时间过长: {failure_duration:.2f}秒")
            
    except Exception as e:
        end_time = time.time()
        failure_duration = end_time - start_time
        print(f"❌ 网络测试异常: {e}")
        print(f"⏱️ 异常耗时: {failure_duration:.2f} 秒")
    
    # 恢复原始URL
    uploader.base_url = original_base_url
    
    print("\n" + "=" * 50)
    print("🎉 改进测试完成！")
    
    # 7. 总结改进内容
    print("\n📋 改进内容总结:")
    print("🔧 重试次数从3次减少到1次")
    print("🔧 添加了保存进度指示器")
    print("🔧 优化了用户体验")
    print("🔧 减少了等待时间")
    
    print("\n✅ 改进后的优势:")
    print("- 大幅减少上传等待时间")
    print("- 用户立即看到保存进度")
    print("- 避免界面无响应的问题")
    print("- 提供更好的用户反馈")
    
    print("\n⏱️ 时间对比:")
    print("- 修改前：最多4秒等待（3次重试 + 原始请求）")
    print("- 修改后：最多2秒等待（1次重试 + 原始请求）")
    print("- 时间节省：50%以上")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_improved_upload()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
