#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的治疗界面布局
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow
from ui.treatment_ui import TreatmentWidget

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("治疗界面布局测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建治疗界面
        self.treatment_widget = TreatmentWidget()
        self.setCentralWidget(self.treatment_widget)
        
        # 模拟一些数据
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据"""
        try:
            # 模拟患者信息
            self.treatment_widget.update_patient_info("张三", "P001")
            
            # 模拟设备状态
            self.treatment_widget.eeg_connected = True
            self.treatment_widget.stimulation_connected = True
            self.treatment_widget.update_device_status_display()
            
            # 模拟治疗统计
            self.treatment_widget.successful_triggers = 15
            self.treatment_widget.total_classifications = 20
            self.treatment_widget._update_treatment_statistics()
            
            # 模拟运动想象检测
            if hasattr(self.treatment_widget, 'mi_detection_label'):
                self.treatment_widget.mi_detection_label.setText("✓ 检测到")
                self.treatment_widget.mi_detection_label.setStyleSheet("font-weight: bold; color: #28a745;")
            
            # 模拟置信度
            from PySide6.QtWidgets import QLabel
            if hasattr(self.treatment_widget, 'confidence_label'):
                # 查找新界面的置信度标签
                for widget in self.treatment_widget.findChildren(QLabel):
                    if widget.text().startswith("--"):
                        widget.setText("85.2%")
                        widget.setStyleSheet("font-weight: bold; color: #28a745;")
                        break
            
            # 模拟刺激状态
            if hasattr(self.treatment_widget, 'stim_status_label'):
                self.treatment_widget.stim_status_label.setText("🟢 进行中")
                self.treatment_widget.stim_status_label.setStyleSheet("font-weight: bold; color: #28a745;")
            
            # 模拟治疗时间
            import time
            self.treatment_widget.treatment_start_time = time.time() - 300  # 5分钟前开始
            self.treatment_widget._update_treatment_time()
            
            # 添加一些系统日志
            self.treatment_widget.add_system_log("脑电设备连接成功", "设备")
            self.treatment_widget.add_system_log("电刺激设备连接成功", "设备")
            self.treatment_widget.add_system_log("开始治疗计时", "治疗")
            self.treatment_widget.add_system_log("检测到运动想象信号", "分类")
            self.treatment_widget.add_system_log("触发电刺激", "刺激")
            
        except Exception as e:
            print(f"设置测试数据失败: {e}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
