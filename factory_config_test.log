2025-05-30 18:01:51,650 - __main__ - INFO - 开始厂家配置功能测试
2025-05-30 18:01:51,650 - __main__ - INFO - ==================================================
2025-05-30 18:01:51,650 - __main__ - INFO - === 测试厂家密码功能 ===
2025-05-30 18:01:51,650 - __main__ - INFO - 厂家密码: NK2024Factory
2025-05-30 18:01:51,650 - __main__ - INFO - ✅ 密码 'NK2024Factory' 验证成功
2025-05-30 18:01:51,650 - __main__ - INFO - ❌ 密码 'wrong_password' 验证失败
2025-05-30 18:01:51,651 - __main__ - INFO - ❌ 密码 '' 验证失败
2025-05-30 18:01:51,651 - __main__ - INFO - === 测试医院编号安全更新功能 ===
2025-05-30 18:01:51,653 - core.database_manager - INFO - 数据库表创建成功
2025-05-30 18:01:51,654 - core.database_manager - INFO - 基础数据初始化成功
2025-05-30 18:01:51,654 - core.database_manager - INFO - 数据库初始化成功
2025-05-30 18:01:51,654 - __main__ - INFO - ✅ 数据库初始化成功
2025-05-30 18:01:51,655 - __main__ - INFO - 当前医院信息: {'id': 1, 'hname': '海天智能', 'keshi': '康复科', 'shebeiid': 'NK2024001', 'created_at': '2025-05-26 02:17:25', 'updated_at': '2025-05-30 09:51:16'}
2025-05-30 18:01:51,655 - __main__ - INFO - 当前医院编号: 1
2025-05-30 18:01:51,656 - __main__ - INFO - 创建测试患者数据
2025-05-30 18:01:51,668 - __main__ - INFO - ✅ 测试患者创建成功
2025-05-30 18:01:51,668 - __main__ - INFO - 测试更新医院编号: 1 -> 999
2025-05-30 18:01:51,669 - core.database_manager - INFO - 开始安全更新医院编号：1 -> 999
2025-05-30 18:01:51,669 - core.database_manager - ERROR - 数据库操作异常: no such column: updated_at
2025-05-30 18:01:51,670 - core.database_manager - ERROR - 安全更新医院编号时发生错误: no such column: updated_at
2025-05-30 18:01:51,670 - __main__ - ERROR - ❌ 医院编号更新失败
2025-05-30 18:01:51,671 - __main__ - ERROR - ❌ 医院编号更新测试失败
2025-05-30 18:02:22,708 - __main__ - INFO - 开始厂家配置功能测试
2025-05-30 18:02:22,708 - __main__ - INFO - ==================================================
2025-05-30 18:02:22,709 - __main__ - INFO - === 测试厂家密码功能 ===
2025-05-30 18:02:22,709 - __main__ - INFO - 厂家密码: NK2024Factory
2025-05-30 18:02:22,709 - __main__ - INFO - ✅ 密码 'NK2024Factory' 验证成功
2025-05-30 18:02:22,709 - __main__ - INFO - ❌ 密码 'wrong_password' 验证失败
2025-05-30 18:02:22,709 - __main__ - INFO - ❌ 密码 '' 验证失败
2025-05-30 18:02:22,710 - __main__ - INFO - === 测试医院编号安全更新功能 ===
2025-05-30 18:02:22,711 - core.database_manager - INFO - 数据库表创建成功
2025-05-30 18:02:22,711 - core.database_manager - INFO - 基础数据初始化成功
2025-05-30 18:02:22,712 - core.database_manager - INFO - 数据库初始化成功
2025-05-30 18:02:22,712 - __main__ - INFO - ✅ 数据库初始化成功
2025-05-30 18:02:22,712 - __main__ - INFO - 当前医院信息: {'id': 1, 'hname': '海天智能', 'keshi': '康复科', 'shebeiid': 'NK2024001', 'created_at': '2025-05-26 02:17:25', 'updated_at': '2025-05-30 09:51:16'}
2025-05-30 18:02:22,712 - __main__ - INFO - 当前医院编号: 1
2025-05-30 18:02:22,712 - __main__ - INFO - 创建测试患者数据
2025-05-30 18:02:22,735 - __main__ - INFO - ✅ 测试患者创建成功
2025-05-30 18:02:22,735 - __main__ - INFO - 测试更新医院编号: 1 -> 999
2025-05-30 18:02:22,735 - core.database_manager - INFO - 开始安全更新医院编号：1 -> 999
2025-05-30 18:02:22,736 - core.database_manager - INFO - 已更新 2 个患者记录的医院编号
2025-05-30 18:02:22,746 - core.database_manager - INFO - 医院编号更新成功：1 -> 999
2025-05-30 18:02:22,746 - __main__ - INFO - ✅ 医院编号更新成功
2025-05-30 18:02:22,746 - __main__ - INFO - ✅ 验证成功，医院编号已更新为: 999
2025-05-30 18:02:22,746 - __main__ - INFO - ✅ 患者表中的医院编号也已正确更新
2025-05-30 18:02:22,747 - __main__ - INFO - 恢复原始医院编号: 999 -> 1
2025-05-30 18:02:22,747 - core.database_manager - INFO - 开始安全更新医院编号：999 -> 1
2025-05-30 18:02:22,748 - core.database_manager - INFO - 已更新 2 个患者记录的医院编号
2025-05-30 18:02:22,759 - core.database_manager - INFO - 医院编号更新成功：999 -> 1
2025-05-30 18:02:22,760 - __main__ - INFO - ✅ 医院编号恢复成功
2025-05-30 18:02:22,770 - __main__ - INFO - ✅ 测试数据清理完成
2025-05-30 18:02:22,771 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-30 18:02:22,771 - __main__ - INFO - ✅ 医院编号更新测试通过
2025-05-30 18:02:22,771 - __main__ - INFO - === 测试边界情况 ===
2025-05-30 18:02:22,774 - core.database_manager - INFO - 数据库表创建成功
2025-05-30 18:02:22,774 - core.database_manager - INFO - 基础数据初始化成功
2025-05-30 18:02:22,775 - core.database_manager - INFO - 数据库初始化成功
2025-05-30 18:02:22,775 - __main__ - INFO - 测试更新到已存在的ID
2025-05-30 18:02:22,775 - core.database_manager - INFO - 开始安全更新医院编号：1 -> 1
2025-05-30 18:02:22,776 - core.database_manager - ERROR - 医院编号 1 已存在
2025-05-30 18:02:22,776 - __main__ - INFO - ✅ 正确拒绝了更新到已存在的ID
2025-05-30 18:02:22,776 - __main__ - INFO - 测试更新不存在的ID
2025-05-30 18:02:22,776 - core.database_manager - INFO - 开始安全更新医院编号：9999 -> 8888
2025-05-30 18:02:22,777 - core.database_manager - INFO - 已更新 0 个患者记录的医院编号
2025-05-30 18:02:22,777 - core.database_manager - ERROR - 未找到医院编号 9999
2025-05-30 18:02:22,777 - __main__ - INFO - ✅ 正确拒绝了更新不存在的ID
2025-05-30 18:02:22,778 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-30 18:02:22,778 - __main__ - INFO - ✅ 边界情况测试通过
2025-05-30 18:02:22,778 - __main__ - INFO - ==================================================
2025-05-30 18:02:22,778 - __main__ - INFO - ✅ 所有测试完成
2025-05-30 18:02:22,779 - __main__ - INFO - 🎉 厂家配置功能测试总结:
2025-05-30 18:02:22,779 - __main__ - INFO -    1. ✅ 厂家密码验证功能正常
2025-05-30 18:02:22,779 - __main__ - INFO -    2. ✅ 医院编号安全更新功能正常
2025-05-30 18:02:22,779 - __main__ - INFO -    3. ✅ 外键约束处理正确
2025-05-30 18:02:22,780 - __main__ - INFO -    4. ✅ 边界情况处理正确
2025-05-30 18:02:22,780 - __main__ - INFO - 
2025-05-30 18:02:22,780 - __main__ - INFO - 📋 使用说明:
2025-05-30 18:02:22,780 - __main__ - INFO -    - 厂家密码: NK2024Factory
2025-05-30 18:02:22,781 - __main__ - INFO -    - 在系统设置界面点击'厂家配置'按钮
2025-05-30 18:02:22,781 - __main__ - INFO -    - 输入密码后可以安全修改医院编号
2025-05-30 18:02:22,781 - __main__ - INFO -    - 系统会自动处理所有相关数据的更新
