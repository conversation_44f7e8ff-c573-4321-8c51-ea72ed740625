#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果测试
1. 验证预刺激输出是否正常
2. 验证断开连接后状态颜色是否正确
"""

import sys
import time
import logging
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit, QSpinBox
from PySide6.QtCore import QTimer

from core.stimulation_device import StimulationDevice

class FixesVerificationTest(QWidget):
    def __init__(self):
        super().__init__()
        self.device = None
        self.test_results = []
        self.channel_a_pre_stimulating = False
        self.channel_b_pre_stimulating = False
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("修复效果验证测试")
        self.setGeometry(100, 100, 600, 500)
        
        layout = QVBoxLayout()
        
        # 状态显示
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 设备连接控制
        self.connect_button = QPushButton("连接设备")
        self.connect_button.clicked.connect(self.connect_device)
        layout.addWidget(self.connect_button)
        
        self.disconnect_button = QPushButton("断开设备")
        self.disconnect_button.clicked.connect(self.disconnect_device)
        self.disconnect_button.setEnabled(False)
        layout.addWidget(self.disconnect_button)
        
        # 通道状态显示
        self.channel_a_status = QLabel("A通道: 关闭")
        self.channel_b_status = QLabel("B通道: 关闭")
        layout.addWidget(self.channel_a_status)
        layout.addWidget(self.channel_b_status)
        
        # 电流调节测试
        layout.addWidget(QLabel("A通道电流测试:"))
        self.current_a_spinbox = QSpinBox()
        self.current_a_spinbox.setRange(0, 100)
        self.current_a_spinbox.setValue(0)
        self.current_a_spinbox.setSuffix(" mA")
        self.current_a_spinbox.valueChanged.connect(lambda v: self.test_pre_stimulation('A', v))
        layout.addWidget(self.current_a_spinbox)
        
        # 测试按钮
        self.test_pre_stimulation_button = QPushButton("测试预刺激输出")
        self.test_pre_stimulation_button.clicked.connect(self.test_pre_stimulation_output)
        layout.addWidget(self.test_pre_stimulation_button)
        
        self.test_disconnect_status_button = QPushButton("测试断开连接状态")
        self.test_disconnect_status_button.clicked.connect(self.test_disconnect_status)
        layout.addWidget(self.test_disconnect_status_button)
        
        # 结果显示
        self.result_display = QTextEdit()
        layout.addWidget(QLabel("测试结果:"))
        layout.addWidget(self.result_display)
        
        self.setLayout(layout)
        
    def add_result(self, message):
        """添加测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        result_message = f"[{timestamp}] {message}"
        self.test_results.append(result_message)
        self.result_display.append(result_message)
        print(result_message)
    
    def connect_device(self):
        """连接设备"""
        try:
            self.device = StimulationDevice()
            if self.device.connect(7):
                self.status_label.setText("✅ 设备连接成功")
                self.add_result("设备连接成功")
                self.connect_button.setEnabled(False)
                self.disconnect_button.setEnabled(True)
                
                # 更新通道状态显示
                self.update_channel_status_display()
            else:
                self.status_label.setText("❌ 设备连接失败")
                self.add_result("设备连接失败")
        except Exception as e:
            self.status_label.setText(f"❌ 设备初始化失败: {e}")
            self.add_result(f"设备初始化失败: {e}")
    
    def disconnect_device(self):
        """断开设备"""
        try:
            if self.device:
                self.device.disconnect()
                self.device = None
            
            self.status_label.setText("设备已断开")
            self.add_result("设备已断开")
            self.connect_button.setEnabled(True)
            self.disconnect_button.setEnabled(False)
            
            # 更新通道状态显示
            self.update_channel_status_display()
            
        except Exception as e:
            self.add_result(f"断开设备失败: {e}")
    
    def update_channel_status_display(self):
        """更新通道状态显示"""
        try:
            if not self.device:
                # 设备未连接时显示关闭状态（灰色字体）
                self.channel_a_status.setText("A通道: 关闭")
                self.channel_a_status.setStyleSheet("color: gray; font-weight: normal;")
                self.channel_b_status.setText("B通道: 关闭")
                self.channel_b_status.setStyleSheet("color: gray; font-weight: normal;")
                
                # 重置预刺激状态
                self.channel_a_pre_stimulating = False
                self.channel_b_pre_stimulating = False
                return
            
            # 检查预刺激状态优先级更高
            if self.channel_a_pre_stimulating:
                self.channel_a_status.setText("A通道: 预刺激中")
                self.channel_a_status.setStyleSheet("color: blue; font-weight: bold;")
            else:
                # 获取设备实际通道状态
                a_status = self.device.get_channel_status(1)
                a_status_text, a_style = self._get_channel_display_info(a_status)
                self.channel_a_status.setText(f"A通道: {a_status_text}")
                self.channel_a_status.setStyleSheet(a_style)
            
            if self.channel_b_pre_stimulating:
                self.channel_b_status.setText("B通道: 预刺激中")
                self.channel_b_status.setStyleSheet("color: blue; font-weight: bold;")
            else:
                # 获取设备实际通道状态
                b_status = self.device.get_channel_status(2)
                b_status_text, b_style = self._get_channel_display_info(b_status)
                self.channel_b_status.setText(f"B通道: {b_status_text}")
                self.channel_b_status.setStyleSheet(b_style)
                
        except Exception as e:
            self.add_result(f"更新通道状态显示失败: {e}")
    
    def _get_channel_display_info(self, status: int) -> tuple:
        """根据通道状态获取显示信息"""
        if status == 1:  # 刺激中
            return "刺激中", "color: green; font-weight: bold;"
        else:  # 所有非1的状态都是暂停
            return "暂停", "color: red; font-weight: bold;"
    
    def test_pre_stimulation(self, channel, value):
        """测试预刺激功能"""
        try:
            if not self.device:
                return
            
            if value <= 0:
                # 电流为0时停止预刺激
                self.stop_channel_pre_stimulation(channel)
                return
            
            # 执行预刺激
            self.execute_pre_stimulation(channel, value)
            
        except Exception as e:
            self.add_result(f"{channel}通道预刺激测试失败: {e}")
    
    def execute_pre_stimulation(self, channel, current_value):
        """执行预刺激"""
        try:
            channel_num = 1 if channel == 'A' else 2
            
            # 设置预刺激状态
            if channel == 'A':
                self.channel_a_pre_stimulating = True
            else:
                self.channel_b_pre_stimulating = True
            
            # 更新状态显示
            self.update_channel_status_display()
            
            # 启动预刺激
            self.add_result(f"{channel}通道电流调节为{current_value}mA，启动预刺激")
            
            if self.device.fast_pre_stimulation(channel_num, float(current_value), 3.0):
                self.add_result(f"{channel}通道预刺激启动成功")
                
                # 启动3秒结束定时器
                timer = QTimer()
                timer.setSingleShot(True)
                timer.timeout.connect(lambda: self.on_pre_stimulation_finished(channel))
                timer.start(3000)
                
                # 保存定时器引用
                if channel == 'A':
                    if hasattr(self, 'channel_a_timer'):
                        self.channel_a_timer.stop()
                    self.channel_a_timer = timer
                else:
                    if hasattr(self, 'channel_b_timer'):
                        self.channel_b_timer.stop()
                    self.channel_b_timer = timer
            else:
                self.add_result(f"{channel}通道预刺激启动失败")
                if channel == 'A':
                    self.channel_a_pre_stimulating = False
                else:
                    self.channel_b_pre_stimulating = False
                self.update_channel_status_display()
                
        except Exception as e:
            self.add_result(f"{channel}通道预刺激执行失败: {e}")
    
    def stop_channel_pre_stimulation(self, channel):
        """停止通道预刺激"""
        try:
            if channel == 'A':
                if hasattr(self, 'channel_a_timer') and self.channel_a_timer.isActive():
                    self.channel_a_timer.stop()
                self.channel_a_pre_stimulating = False
            else:
                if hasattr(self, 'channel_b_timer') and self.channel_b_timer.isActive():
                    self.channel_b_timer.stop()
                self.channel_b_pre_stimulating = False
            
            self.update_channel_status_display()
            self.add_result(f"{channel}通道预刺激已停止")
            
        except Exception as e:
            self.add_result(f"停止{channel}通道预刺激失败: {e}")
    
    def on_pre_stimulation_finished(self, channel):
        """预刺激结束回调"""
        try:
            if channel == 'A':
                self.channel_a_pre_stimulating = False
            else:
                self.channel_b_pre_stimulating = False
            
            self.update_channel_status_display()
            self.add_result(f"{channel}通道预刺激结束")
            
        except Exception as e:
            self.add_result(f"{channel}通道预刺激结束处理失败: {e}")
    
    def test_pre_stimulation_output(self):
        """测试预刺激输出"""
        if not self.device:
            self.add_result("❌ 设备未连接，无法测试预刺激输出")
            return
        
        self.add_result("🧪 开始测试预刺激输出...")
        
        # 测试A通道预刺激
        self.add_result("测试A通道15mA预刺激...")
        self.current_a_spinbox.setValue(15)
    
    def test_disconnect_status(self):
        """测试断开连接状态"""
        self.add_result("🧪 开始测试断开连接状态...")
        
        if self.device:
            self.add_result("断开设备连接...")
            self.disconnect_device()
            
            # 检查状态颜色
            a_style = self.channel_a_status.styleSheet()
            b_style = self.channel_b_status.styleSheet()
            
            if "color: gray" in a_style and "color: gray" in b_style:
                self.add_result("✅ 断开连接后状态颜色正确（灰色）")
            else:
                self.add_result(f"❌ 断开连接后状态颜色不正确")
                self.add_result(f"   A通道样式: {a_style}")
                self.add_result(f"   B通道样式: {b_style}")
        else:
            self.add_result("设备已断开，状态应为灰色")

def main():
    app = QApplication(sys.argv)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_widget = FixesVerificationTest()
    test_widget.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
