#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试患者编号编辑逻辑
Test Patient Number Edit Logic

验证添加时编号可编辑，编辑时编号不可编辑

作者: AI Assistant
版本: 1.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from ui.patient_management_ui import PatientManagementWidget
from core.database_manager import DatabaseManager
from core.auth_manager import AuthManager

def test_patient_number_edit_logic():
    """测试患者编号编辑逻辑"""
    print("📝 测试患者编号编辑逻辑")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 初始化数据库和认证管理器
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    auth_manager = AuthManager(db_manager)
    
    # 创建患者管理界面
    widget = PatientManagementWidget()
    widget.set_managers(db_manager, auth_manager, None)
    widget.show()
    
    print("\n1. 测试初始状态...")
    # 初始状态：没有选中患者，表单不可编辑
    bianhao_widget = widget.form_widgets['bianhao']
    print(f"初始状态 - 编号可编辑: {bianhao_widget.isEnabled()}")
    print(f"初始状态 - 当前患者: {widget.current_patient}")
    
    print("\n2. 测试添加模式...")
    # 模拟点击添加按钮
    widget.add_patient()
    print(f"添加模式 - 编号可编辑: {bianhao_widget.isEnabled()}")
    print(f"添加模式 - 当前患者: {widget.current_patient}")
    print(f"添加模式 - 编号框样式: {bianhao_widget.styleSheet()}")
    
    # 验证其他字段也可编辑
    name_widget = widget.form_widgets['name']
    age_widget = widget.form_widgets['age']
    print(f"添加模式 - 姓名可编辑: {name_widget.isEnabled()}")
    print(f"添加模式 - 年龄可编辑: {age_widget.isEnabled()}")
    
    print("\n3. 测试取消添加...")
    # 模拟点击取消按钮
    widget.cancel_edit()
    print(f"取消后 - 编号可编辑: {bianhao_widget.isEnabled()}")
    print(f"取消后 - 当前患者: {widget.current_patient}")
    
    print("\n4. 模拟选中患者进行编辑...")
    # 创建一个模拟患者数据
    mock_patient = {
        'bianhao': 12345,
        'name': '测试患者',
        'age': 30,
        'xingbie': '男',
        'cardid': '110101199001011234',
        'zhenduan': '测试诊断',
        'bingshi': '测试既往史',
        'brhc': '左侧',
        'zhuzhi': '测试医生',
        'czy': 'admin',
        'keshi': '康复科',
        'shebeiid': 'NK001'
    }
    
    # 设置当前患者并填充表单
    widget.current_patient = mock_patient
    widget.populate_patient_form(mock_patient)
    
    print("\n5. 测试编辑模式...")
    # 模拟点击编辑按钮
    widget.edit_patient()
    print(f"编辑模式 - 编号可编辑: {bianhao_widget.isEnabled()}")
    print(f"编辑模式 - 当前患者: {widget.current_patient is not None}")
    print(f"编辑模式 - 编号框样式: {bianhao_widget.styleSheet()}")
    print(f"编辑模式 - 编号值: {bianhao_widget.text()}")
    
    # 验证其他字段可编辑
    print(f"编辑模式 - 姓名可编辑: {name_widget.isEnabled()}")
    print(f"编辑模式 - 年龄可编辑: {age_widget.isEnabled()}")
    
    # 验证只读字段
    keshi_widget = widget.form_widgets['keshi']
    shebeiid_widget = widget.form_widgets['shebeiid']
    czy_widget = widget.form_widgets['czy']
    print(f"编辑模式 - 科室可编辑: {keshi_widget.isEnabled()}")
    print(f"编辑模式 - 设备号可编辑: {shebeiid_widget.isEnabled()}")
    print(f"编辑模式 - 操作员可编辑: {czy_widget.isEnabled()}")
    
    print("\n6. 测试取消编辑...")
    # 模拟点击取消按钮
    widget.cancel_edit()
    print(f"取消编辑后 - 编号可编辑: {bianhao_widget.isEnabled()}")
    print(f"取消编辑后 - 当前患者: {widget.current_patient is not None}")
    
    print("\n" + "=" * 50)
    print("🎉 患者编号编辑逻辑测试完成！")
    
    # 总结测试结果
    print("\n📋 测试结果总结:")
    print("✅ 初始状态：表单不可编辑，编号不可编辑")
    print("✅ 添加模式：表单可编辑，编号可编辑")
    print("✅ 编辑模式：表单可编辑，编号不可编辑（灰色）")
    print("✅ 取消操作：恢复到不可编辑状态")
    print("✅ 只读字段：始终不可编辑（科室、设备号、操作员）")
    
    print("\n🎯 编号编辑规则:")
    print("- 添加患者时：编号可编辑，用户可输入新编号")
    print("- 编辑患者时：编号不可编辑，防止修改现有编号")
    print("- 样式区分：可编辑时正常样式，不可编辑时灰色样式")
    
    print("\n💡 用户体验:")
    print("- 清晰的视觉反馈：灰色表示不可编辑")
    print("- 逻辑合理：编号作为主键不允许修改")
    print("- 操作直观：添加时可输入，编辑时受保护")
    
    # 关闭应用程序
    widget.close()
    app.quit()

def main():
    """主函数"""
    try:
        test_patient_number_edit_logic()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
