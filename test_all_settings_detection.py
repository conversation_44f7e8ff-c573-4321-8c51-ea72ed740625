#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有设置项的变化检测
验证每个配置项都能被正确检测到变化
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_all_settings_detection():
    """测试所有设置项的变化检测逻辑"""
    print("=" * 80)
    print("所有设置项变化检测测试")
    print("=" * 80)
    
    # 模拟原始配置
    original_config = {
        # 医院信息
        'hospital_info': {
            'hname': '原医院',
            'keshi': '原科室', 
            'shebeiid': 'ORIG001'
        },
        
        # 电刺激设备配置
        'stimulation_port': 1,
        'max_current': 50,
        'min_current': 1,
        'current_step': 1,
        'frequency': 20,
        'pulse_width': 200,
        'relax_time': 5,
        'work_time': 10,
        'climb_time': 2,
        'fall_time': 2,
        'wave_type': 0,
        'timeout': 5,
        
        # 脑电设备配置
        'eeg_port': 'COM8',
        'eeg_baud': 115200,
        'eeg_sample_rate': 125.0,
        'eeg_channels': 8,
        
        # 信号处理配置
        'highpass': 0.5,
        'lowpass': 50.0,
        'notch': 50.0,
        'filter_order': 4,
        
        # UI配置
        'font_size': 10,
        
        # 日志配置
        'log_level': 'INFO',
        'log_file_size': 10,
        'console_output': True,
        
        # 数据库配置
        'auto_backup': True,
        'backup_interval': 24
    }
    
    # 测试各个配置项的变化检测
    test_cases = [
        # 医院信息测试
        {
            'name': '医院信息变化',
            'changes': {
                'hospital_info': {
                    'hname': '新医院',
                    'keshi': '新科室',
                    'shebeiid': 'NEW001'
                }
            },
            'expected_count': 3
        },
        
        # 电刺激设备配置测试
        {
            'name': '电刺激设备配置变化',
            'changes': {
                'stimulation_port': 7,
                'max_current': 60,
                'frequency': 25,
                'pulse_width': 250,
                'wave_type': 1
            },
            'expected_count': 5
        },
        
        # 脑电设备配置测试
        {
            'name': '脑电设备配置变化',
            'changes': {
                'eeg_port': 'COM9',
                'eeg_baud': 230400,
                'eeg_sample_rate': 250.0,
                'eeg_channels': 16
            },
            'expected_count': 4
        },
        
        # 信号处理配置测试
        {
            'name': '信号处理配置变化',
            'changes': {
                'highpass': 1.0,
                'lowpass': 40.0,
                'notch': 60.0,
                'filter_order': 6
            },
            'expected_count': 4
        },
        
        # UI配置测试
        {
            'name': 'UI配置变化',
            'changes': {
                'font_size': 12
            },
            'expected_count': 1
        },
        
        # 日志配置测试
        {
            'name': '日志配置变化',
            'changes': {
                'log_level': 'DEBUG',
                'log_file_size': 20,
                'console_output': False
            },
            'expected_count': 3
        },
        
        # 数据库配置测试
        {
            'name': '数据库配置变化',
            'changes': {
                'auto_backup': False,
                'backup_interval': 48
            },
            'expected_count': 2
        },
        
        # 混合配置测试
        {
            'name': '混合配置变化',
            'changes': {
                'stimulation_port': 3,
                'font_size': 14,
                'log_level': 'WARNING',
                'auto_backup': False
            },
            'expected_count': 4
        }
    ]
    
    # 执行测试
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}...")
        
        # 创建修改后的配置
        current_config = original_config.copy()
        current_config.update(test_case['changes'])
        
        # 如果有医院信息变化，需要特殊处理
        if 'hospital_info' in test_case['changes']:
            current_config['hospital_info'] = test_case['changes']['hospital_info']
        
        # 检测变化
        changes = detect_all_changes(original_config, current_config)
        
        # 验证结果
        if len(changes) == test_case['expected_count']:
            print(f"   ✓ 检测到 {len(changes)} 个变化（期望 {test_case['expected_count']} 个）")
            for change in changes:
                print(f"     • {change}")
        else:
            print(f"   ✗ 检测到 {len(changes)} 个变化，期望 {test_case['expected_count']} 个")
            print(f"     实际变化: {changes}")
            all_passed = False
    
    # 测试无变化情况
    print(f"\n{len(test_cases) + 1}. 测试无变化情况...")
    changes = detect_all_changes(original_config, original_config.copy())
    if len(changes) == 0:
        print("   ✓ 正确检测到无变化")
    else:
        print(f"   ✗ 错误检测到变化: {changes}")
        all_passed = False
    
    # 输出测试结果
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！变化检测功能完整且正确！")
    else:
        print("❌ 部分测试失败，需要检查变化检测逻辑")
    print("=" * 80)
    
    return all_passed

def detect_all_changes(original, current):
    """检测所有配置变化（模拟设置界面的完整逻辑）"""
    changes = []
    
    # 检查医院信息变化
    orig_hospital = original.get('hospital_info', {})
    curr_hospital = current.get('hospital_info', {})
    
    if curr_hospital.get('hname') != orig_hospital.get('hname'):
        changes.append(f"医院名称: {orig_hospital.get('hname', '')} → {curr_hospital.get('hname', '')}")
    
    if curr_hospital.get('keshi') != orig_hospital.get('keshi'):
        changes.append(f"科室名称: {orig_hospital.get('keshi', '')} → {curr_hospital.get('keshi', '')}")
    
    if curr_hospital.get('shebeiid') != orig_hospital.get('shebeiid'):
        changes.append(f"设备编号: {orig_hospital.get('shebeiid', '')} → {curr_hospital.get('shebeiid', '')}")
    
    # 检查电刺激设备配置变化
    stimulation_fields = [
        ('stimulation_port', '端口号', lambda x: f'COM{x}'),
        ('max_current', '最大电流', lambda x: f'{x}mA'),
        ('min_current', '最小电流', lambda x: f'{x}mA'),
        ('current_step', '电流步长', lambda x: f'{x}mA'),
        ('frequency', '默认频率', lambda x: f'{x}Hz'),
        ('pulse_width', '默认脉宽', lambda x: f'{x}μs'),
        ('relax_time', '默认休息时间', lambda x: f'{x}s'),
        ('work_time', '默认工作时间', lambda x: f'{x}s'),
        ('climb_time', '默认上升时间', lambda x: f'{x}s'),
        ('fall_time', '默认下降时间', lambda x: f'{x}s'),
        ('wave_type', '默认波形类型', lambda x: ['双相波', '单相波'][x]),
        ('timeout', '连接超时', lambda x: f'{x}s')
    ]
    
    for field, name, formatter in stimulation_fields:
        if current.get(field) != original.get(field):
            changes.append(f"{name}: {formatter(original.get(field))} → {formatter(current.get(field))}")
    
    # 检查脑电设备配置变化
    eeg_fields = [
        ('eeg_port', '端口'),
        ('eeg_baud', '波特率'),
        ('eeg_sample_rate', '采样率'),
        ('eeg_channels', '通道数')
    ]
    
    for field, name in eeg_fields:
        if current.get(field) != original.get(field):
            changes.append(f"{name}: {original.get(field)} → {current.get(field)}")
    
    # 检查信号处理配置变化
    signal_fields = [
        ('highpass', '高通滤波', 'Hz'),
        ('lowpass', '低通滤波', 'Hz'),
        ('notch', '陷波滤波', 'Hz'),
        ('filter_order', '滤波器阶数', '')
    ]
    
    for field, name, unit in signal_fields:
        if current.get(field) != original.get(field):
            changes.append(f"{name}: {original.get(field)}{unit} → {current.get(field)}{unit}")
    
    # 检查UI配置变化
    if current.get('font_size') != original.get('font_size'):
        changes.append(f"字体大小: {original.get('font_size')} → {current.get('font_size')}")
    
    # 检查日志配置变化
    if current.get('log_level') != original.get('log_level'):
        changes.append(f"日志级别: {original.get('log_level')} → {current.get('log_level')}")
    
    if current.get('log_file_size') != original.get('log_file_size'):
        changes.append(f"日志文件大小: {original.get('log_file_size')}MB → {current.get('log_file_size')}MB")
    
    if current.get('console_output') != original.get('console_output'):
        changes.append(f"控制台输出: {'开启' if original.get('console_output') else '关闭'} → {'开启' if current.get('console_output') else '关闭'}")
    
    # 检查数据库配置变化
    if current.get('auto_backup') != original.get('auto_backup'):
        changes.append(f"自动备份: {'开启' if original.get('auto_backup') else '关闭'} → {'开启' if current.get('auto_backup') else '关闭'}")
    
    if current.get('backup_interval') != original.get('backup_interval'):
        changes.append(f"备份间隔: {original.get('backup_interval')}小时 → {current.get('backup_interval')}小时")
    
    return changes

def main():
    """主函数"""
    success = test_all_settings_detection()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
