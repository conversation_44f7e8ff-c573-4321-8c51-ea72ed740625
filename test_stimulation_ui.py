#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激界面测试脚本
Electrical Stimulation UI Test Script

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Qt环境变量
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(project_root / 'plugins' / 'platforms')

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    from PySide6.QtCore import Qt
    
    from ui.treatment_ui import TreatmentWidget
    from core.database_manager import DatabaseManager
    from utils.app_config import AppConfig
    
    class TestMainWindow(QMainWindow):
        """测试主窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("电刺激功能测试")
            self.setGeometry(100, 100, 1200, 800)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 创建治疗界面
            self.treatment_widget = TreatmentWidget()
            layout.addWidget(self.treatment_widget)
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            self.db_manager.initialize()
            
            # 设置数据库管理器
            self.treatment_widget.set_database_manager(self.db_manager)
            
            print("电刺激界面测试窗口创建成功")
            print("请在界面中测试以下功能：")
            print("1. 切换到'电刺激治疗'标签页")
            print("2. 点击'连接电刺激设备'按钮")
            print("3. 调整刺激参数")
            print("4. 测试开始/停止刺激功能")
            print("5. 查看刺激日志")
    
    def main():
        """主函数"""
        try:
            # 创建应用程序
            app = QApplication(sys.argv)
            app.setApplicationName("电刺激功能测试")
            
            # 创建主窗口
            window = TestMainWindow()
            window.show()
            
            # 运行应用程序
            return app.exec()
            
        except Exception as e:
            print(f"测试程序运行异常: {e}")
            return 1
    
    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装PySide6和相关依赖")
    sys.exit(1)
except Exception as e:
    print(f"程序异常: {e}")
    sys.exit(1)
