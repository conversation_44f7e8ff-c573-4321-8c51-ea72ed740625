# 脑电地形图显示优化说明

## 🎯 问题分析

### 原始问题
1. **美观性不足**: 地形图显示效果粗糙，缺乏专业感
2. **布局不稳定**: 区域大小不断变化，影响用户体验
3. **数据位数影响**: 颜色条标签长度变化导致整体布局变化

### 根本原因
- 颜色范围动态变化导致颜色条尺寸变化
- 插值精度不够，显示效果粗糙
- 头部轮廓和电极标注不够美观
- 布局参数未固定，随内容变化

## ✅ 优化方案

### 1. 固定颜色范围
```python
# 根据数据范围智能选择固定的颜色范围
data_range = max(abs(np.nanmax(channel_data)), abs(np.nanmin(channel_data)))
if data_range < 50:
    vmin, vmax = -100, 100
elif data_range < 200:
    vmin, vmax = -300, 300
else:
    vmin, vmax = -500, 500
```

**优势**:
- 避免颜色条范围频繁变化
- 保持布局稳定性
- 提供合理的数据显示范围

### 2. 提升视觉效果

#### 更密集的插值网格
```python
# 从100x100提升到150x150
xi = np.linspace(-1.2, 1.2, 150)
yi = np.linspace(-1.2, 1.2, 150)
```

#### 更平滑的等高线
```python
# 增加等高线级数从20到30
contour = ax.contourf(xi, yi, zi, levels=30, cmap='RdBu_r')
# 添加细线等高线
contour_lines = ax.contour(xi, yi, zi, levels=10, colors='black', alpha=0.3)
```

#### 美观的头部轮廓
```python
# 更精细的头部轮廓（200个点）
theta = np.linspace(0, 2*np.pi, 200)

# 更自然的鼻子形状
nose_theta = np.linspace(-np.pi/6, np.pi/6, 20)
nose_x = 0.1 * np.sin(nose_theta)
nose_y = np.cos(nose_theta) + 0.05

# 更立体的耳朵形状
ear_theta = np.linspace(-np.pi/3, np.pi/3, 20)
```

### 3. 优化电极显示

#### 更大更清晰的电极点
```python
ax.scatter(
    self.electrode_positions[:, 0], 
    self.electrode_positions[:, 1],
    c='black', s=80, edgecolors='white', linewidth=2, zorder=10,
    marker='o'
)
```

#### 智能标签定位
```python
# 根据电极位置智能调整标签位置
if x < -0.3:  # 左侧电极
    ha, xytext = 'right', (-8, 0)
elif x > 0.3:  # 右侧电极
    ha, xytext = 'left', (8, 0)
else:  # 中间电极
    ha, xytext = 'center', (0, 10)
```

#### 美观的标签样式
```python
ax.annotate(ch_name, (x, y),
           xytext=xytext, textcoords='offset points',
           fontsize=9, ha=ha, va='center',
           color='black', weight='bold',
           bbox=dict(boxstyle='round,pad=0.2', facecolor='white', 
                   edgecolor='black', alpha=0.8))
```

### 4. 固定布局参数

#### 固定图形尺寸
```python
fig = Figure(figsize=(5, 4.5), facecolor='white', dpi=100)
```

#### 固定颜色条格式
```python
cbar = fig.colorbar(contour, ax=ax, shrink=0.7, pad=0.05, aspect=20)
# 固定刻度标签，避免变化
cbar.set_ticks([vmin, vmin/2, 0, vmax/2, vmax])
cbar.set_ticklabels([f'{vmin:.0f}', f'{vmin/2:.0f}', '0', 
                   f'{vmax/2:.0f}', f'{vmax:.0f}'])
```

#### 固定子图布局
```python
fig.subplots_adjust(left=0.05, right=0.85, top=0.85, bottom=0.05)
```

## 🎨 视觉效果提升

### 1. 颜色映射优化
- 使用`RdBu_r`颜色映射，红色表示正值，蓝色表示负值
- 30级等高线提供平滑的颜色过渡
- 添加黑色细线等高线增强层次感

### 2. 头部轮廓美化
- 200个点的精细圆形轮廓
- 自然弧形的鼻子形状
- 立体感的耳朵轮廓
- 2.5像素线宽提供清晰边界

### 3. 电极标注优化
- 80像素大小的电极点，清晰可见
- 白色边框突出电极位置
- 智能标签定位避免重叠
- 圆角白色背景框提升可读性

### 4. 统计信息显示
- 简洁的标题格式
- 整数显示避免小数点混乱
- 粗体字体增强可读性

## 📊 技术参数

### 插值参数
- **网格密度**: 150x150（提升50%）
- **插值方法**: cubic（优先），linear（备选）
- **掩码范围**: 1.05倍头部半径

### 显示参数
- **等高线级数**: 30级（填充），10级（线条）
- **颜色映射**: RdBu_r
- **电极大小**: 80像素
- **线宽**: 2.5像素

### 布局参数
- **图形尺寸**: 5x4.5英寸
- **DPI**: 100
- **边距**: left=0.05, right=0.85, top=0.85, bottom=0.05
- **颜色条**: shrink=0.7, pad=0.05, aspect=20

## 🔧 稳定性保证

### 1. 颜色范围固定
- 小信号: ±100μV
- 中等信号: ±300μV  
- 大信号: ±500μV

### 2. 布局固定
- 固定图形尺寸和边距
- 固定颜色条位置和大小
- 固定标题和标签格式

### 3. 内存管理
- 及时关闭matplotlib图形
- 清理IO缓冲区
- 避免内存泄漏

## 🎯 效果对比

### 优化前
- ❌ 颜色范围动态变化
- ❌ 布局不稳定
- ❌ 插值精度低
- ❌ 电极标注简陋
- ❌ 头部轮廓粗糙

### 优化后
- ✅ 固定颜色范围，布局稳定
- ✅ 高精度插值，平滑显示
- ✅ 美观的电极标注
- ✅ 精细的头部轮廓
- ✅ 专业的医疗级显示效果

## 📈 用户体验提升

1. **视觉稳定性**: 地形图区域大小不再变化
2. **显示质量**: 更平滑、更专业的地形图效果
3. **信息清晰**: 电极位置和数值清晰可读
4. **专业感**: 符合医疗器械软件的显示标准

这些优化确保了脑电地形图不仅美观专业，而且布局稳定，为临床使用提供了可靠的视觉反馈。
