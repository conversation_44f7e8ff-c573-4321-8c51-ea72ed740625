#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练界面集成补丁
Training UI Integration Patch

将原始数据存储功能集成到现有的训练界面中

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.training_data_integration import TrainingDataIntegration
from core.database_manager import DatabaseManager


class TrainingUIEnhancement:
    """训练界面增强类"""
    
    def __init__(self, treatment_ui):
        """初始化增强功能"""
        self.treatment_ui = treatment_ui
        self.logger = treatment_ui.logger
        
        # 初始化训练数据集成
        self.training_data_integration = TrainingDataIntegration(treatment_ui.db_manager)
        
        # 保存原始方法
        self._original_start_training = treatment_ui.start_training
        self._original_stop_training = treatment_ui.stop_training
        self._original_on_eeg_data_received = treatment_ui.on_eeg_data_received
        
        # 替换方法
        treatment_ui.start_training = self._enhanced_start_training
        treatment_ui.stop_training = self._enhanced_stop_training
        treatment_ui.on_eeg_data_received = self._enhanced_on_eeg_data_received
        
        # 添加训练器回调
        if hasattr(treatment_ui, 'mi_trainer') and treatment_ui.mi_trainer:
            self._setup_trainer_callbacks()
        
        self.logger.info("训练界面原始数据存储增强已启用")
    
    def _enhanced_start_training(self):
        """增强的开始训练方法"""
        try:
            # 调用原始方法
            result = self._original_start_training()
            
            # 如果训练开始成功，启动数据记录
            if result and self.treatment_ui.is_training:
                patient_id = self._get_current_patient_id()
                if patient_id:
                    success = self.training_data_integration.start_training_session(patient_id)
                    if success:
                        self.logger.info("训练数据记录会话已启动")
                        self.treatment_ui.add_training_log("📊 原始数据记录已启动")
                    else:
                        self.logger.warning("训练数据记录会话启动失败")
                        self.treatment_ui.add_training_log("⚠️ 原始数据记录启动失败")
                
                # 设置训练器回调
                self._setup_trainer_callbacks()
            
            return result
            
        except Exception as e:
            self.logger.error(f"增强开始训练失败: {e}")
            return self._original_start_training()
    
    def _enhanced_stop_training(self):
        """增强的停止训练方法"""
        try:
            # 结束数据记录会话
            if hasattr(self.training_data_integration, 'current_session') and \
               self.training_data_integration.current_session:
                success = self.training_data_integration.end_training_session()
                if success:
                    self.logger.info("训练数据记录会话已结束")
                    self.treatment_ui.add_training_log("📊 原始数据记录已停止")
                else:
                    self.logger.warning("训练数据记录会话结束失败")
            
            # 调用原始方法
            return self._original_stop_training()
            
        except Exception as e:
            self.logger.error(f"增强停止训练失败: {e}")
            return self._original_stop_training()
    
    def _enhanced_on_eeg_data_received(self, data_packet):
        """增强的脑电数据接收方法"""
        try:
            # 调用原始方法
            self._original_on_eeg_data_received(data_packet)
            
            # 如果正在训练，将数据传递给数据集成管理器
            if self.treatment_ui.is_training:
                self.training_data_integration.process_eeg_data(data_packet)
            
        except Exception as e:
            self.logger.error(f"增强脑电数据处理失败: {e}")
            # 确保原始功能不受影响
            self._original_on_eeg_data_received(data_packet)
    
    def _setup_trainer_callbacks(self):
        """设置训练器回调"""
        try:
            if not hasattr(self.treatment_ui, 'mi_trainer') or not self.treatment_ui.mi_trainer:
                return
            
            trainer = self.treatment_ui.mi_trainer
            
            # 保存原始回调
            if not hasattr(self, '_original_state_changed'):
                self._original_state_changed = getattr(trainer, 'state_changed_callback', None)
            
            # 设置新的状态变化回调
            trainer.state_changed_callback = self._on_training_state_changed
            
        except Exception as e:
            self.logger.error(f"设置训练器回调失败: {e}")
    
    def _on_training_state_changed(self, old_state, new_state, round_number=0, trial_number=0):
        """训练状态变化回调"""
        try:
            # 调用原始回调
            if self._original_state_changed:
                self._original_state_changed(old_state, new_state, round_number, trial_number)
            
            # 处理数据记录
            from core.motor_imagery_trainer import TrainingState
            
            # 如果进入运动想象或休息状态，开始记录
            if new_state in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET]:
                success = self.training_data_integration.start_trial_recording(
                    training_state=new_state,
                    round_number=round_number,
                    trial_number=trial_number
                )
                if success:
                    state_name = "运动想象" if new_state == TrainingState.MOTOR_IMAGERY else "休息"
                    self.logger.debug(f"开始记录{state_name}数据")
            
            # 如果离开运动想象或休息状态，结束记录
            elif old_state in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET] and \
                 new_state not in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET]:
                success = self.training_data_integration.end_trial_recording()
                if success:
                    state_name = "运动想象" if old_state == TrainingState.MOTOR_IMAGERY else "休息"
                    self.logger.debug(f"结束记录{state_name}数据")
            
        except Exception as e:
            self.logger.error(f"处理训练状态变化失败: {e}")
    
    def _get_current_patient_id(self):
        """获取当前患者ID"""
        try:
            # 尝试从治疗界面获取患者信息
            if hasattr(self.treatment_ui, 'current_patient_info') and \
               self.treatment_ui.current_patient_info:
                return self.treatment_ui.current_patient_info.get('bianhao')
            
            # 尝试从患者选择获取
            if hasattr(self.treatment_ui, 'patient_combo') and \
               self.treatment_ui.patient_combo.currentData():
                return self.treatment_ui.patient_combo.currentData()
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取当前患者ID失败: {e}")
            return None
    
    def get_training_statistics(self):
        """获取训练统计信息"""
        try:
            stats = self.training_data_integration.get_session_statistics()
            return stats
        except Exception as e:
            self.logger.error(f"获取训练统计失败: {e}")
            return {}
    
    def get_patient_training_data(self, patient_id=None):
        """获取患者训练数据"""
        try:
            if not patient_id:
                patient_id = self._get_current_patient_id()
            
            if not patient_id:
                return None
            
            return self.training_data_integration.get_patient_training_data(patient_id)
        except Exception as e:
            self.logger.error(f"获取患者训练数据失败: {e}")
            return None
    
    def check_data_status(self):
        """检查数据状态"""
        try:
            return self.training_data_integration.check_data_directory()
        except Exception as e:
            self.logger.error(f"检查数据状态失败: {e}")
            return {'error': str(e)}


def apply_training_enhancement(treatment_ui):
    """应用训练增强功能"""
    try:
        enhancement = TrainingUIEnhancement(treatment_ui)
        
        # 将增强对象附加到治疗界面，以便后续访问
        treatment_ui.training_enhancement = enhancement
        
        print("✅ 训练界面原始数据存储增强已应用")
        return enhancement
        
    except Exception as e:
        print(f"❌ 应用训练增强功能失败: {e}")
        return None


def test_enhancement():
    """测试增强功能"""
    print("🧪 测试训练界面增强功能")
    
    try:
        # 模拟治疗界面
        class MockTreatmentUI:
            def __init__(self):
                self.db_manager = DatabaseManager()
                self.db_manager.initialize()
                self.is_training = False
                self.current_patient_info = {'bianhao': 123}
                self.logger = logging.getLogger(__name__)
                
            def start_training(self):
                print("原始开始训练方法")
                self.is_training = True
                return True
                
            def stop_training(self):
                print("原始停止训练方法")
                self.is_training = False
                return True
                
            def on_eeg_data_received(self, data_packet):
                print("原始脑电数据接收方法")
                
            def add_training_log(self, message):
                print(f"训练日志: {message}")
        
        # 创建模拟界面
        mock_ui = MockTreatmentUI()
        
        # 应用增强
        enhancement = apply_training_enhancement(mock_ui)
        
        if enhancement:
            # 测试增强功能
            print("\n📊 测试开始训练...")
            mock_ui.start_training()
            
            print("\n📊 测试数据状态...")
            status = enhancement.check_data_status()
            print(f"数据状态: {status}")
            
            print("\n📊 测试停止训练...")
            mock_ui.stop_training()
            
            print("\n✅ 增强功能测试完成")
            return True
        else:
            print("❌ 增强功能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    success = test_enhancement()
    sys.exit(0 if success else 1)
