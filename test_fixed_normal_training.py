#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的普通训练多轮训练行为
Test Fixed Normal Training Multi-Round Behavior

验证修复后普通训练能够累积学习成果

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.ml_model import MotorImageryModel
from core.eegnet_model import TrainingConfig


def test_fixed_normal_training():
    """测试修复后的普通训练多轮训练行为"""
    print("🧪 测试修复后的普通训练多轮训练")
    print("=" * 60)
    
    try:
        # 创建模型
        model = MotorImageryModel("Fixed_Normal_Training_Test")
        print("✅ 创建模型成功")
        
        # 禁用迁移学习
        model_info = model.get_model_info()
        model_info.transfer_learning = False
        print("🔧 禁用迁移学习（使用普通训练）")
        
        # 模拟多轮训练
        total_rounds = 3
        samples_per_round = 10
        model_ids = []
        
        for round_num in range(1, total_rounds + 1):
            print(f"\n📋 第{round_num}轮训练")
            print("-" * 40)
            
            # 记录训练前的模型状态
            before_model_id = id(model.eegnet_model.model) if model.eegnet_model.model else None
            before_is_trained = model.eegnet_model.is_trained
            
            # 添加本轮训练数据
            print(f"🔄 添加第{round_num}轮训练数据...")
            for i in range(samples_per_round):
                data = np.random.randn(8, 250) * 50
                label = i % 2
                model.add_training_data(data, label)
            
            print(f"✅ 添加训练数据: {samples_per_round} 个样本")
            print(f"📊 累积样本数: {len(model.eegnet_model.training_data)}")
            print(f"🔍 训练前状态: 模型ID={before_model_id}, 已训练={before_is_trained}")
            
            # 训练配置
            config = TrainingConfig(epochs=3, batch_size=8, learning_rate=0.001)
            
            # 训练模型
            print(f"🚀 开始第{round_num}轮训练...")
            
            def progress_callback(message, progress):
                if progress % 25 == 0:
                    print(f"  [{progress:3d}%] {message}")
            
            start_time = time.time()
            success = model.train_model(config=config, progress_callback=progress_callback)
            training_time = time.time() - start_time
            
            # 记录训练后的模型状态
            after_model_id = id(model.eegnet_model.model) if model.eegnet_model.model else None
            after_is_trained = model.eegnet_model.is_trained
            
            if success:
                print(f"✅ 第{round_num}轮训练完成，耗时: {training_time:.1f}秒")
                print(f"🔍 训练后状态: 模型ID={after_model_id}, 已训练={after_is_trained}")
                
                # 分析模型行为
                model_recreated = (before_model_id != after_model_id) if before_model_id else True
                if model_recreated:
                    if round_num == 1:
                        behavior = "🆕 首次创建（正常）"
                    else:
                        behavior = "🔄 重新创建（学习成果丢失）"
                else:
                    behavior = "♻️ 复用现有（学习成果保持）"
                
                print(f"📈 模型行为: {behavior}")
                
                model_ids.append({
                    'round': round_num,
                    'before_id': before_model_id,
                    'after_id': after_model_id,
                    'recreated': model_recreated,
                    'behavior': behavior
                })
                
                # 检查训练策略
                model_info = model.get_model_info()
                if hasattr(model_info, 'used_transfer_learning'):
                    if model_info.used_transfer_learning:
                        print(f"⚠️ 意外使用了迁移学习")
                    else:
                        print(f"🔧 确认使用了从头训练")
                
                # 显示性能
                if hasattr(model_info, 'performance') and model_info.performance:
                    perf = model_info.performance
                    print(f"📊 训练准确率: {perf.accuracy*100:.1f}%")
                    print(f"📊 验证准确率: {perf.val_accuracy*100:.1f}%")
                
            else:
                print(f"❌ 第{round_num}轮训练失败")
                return False
        
        # 分析结果
        print(f"\n📊 多轮训练行为分析:")
        print("-" * 40)
        
        recreated_count = sum(1 for info in model_ids if info['recreated'])
        continued_count = len(model_ids) - recreated_count
        
        for info in model_ids:
            print(f"   第{info['round']}轮: {info['behavior']}")
        
        print(f"\n🔍 总结:")
        if recreated_count == 1 and model_ids[0]['round'] == 1:
            print(f"   ✅ 理想行为：只有第一轮创建新模型，后续轮次复用")
            print(f"   ✅ 学习成果会累积保持")
            result = "perfect"
        elif recreated_count == len(model_ids):
            print(f"   ❌ 问题行为：每轮都重新创建模型")
            print(f"   ❌ 学习成果会丢失")
            result = "bad"
        else:
            print(f"   ⚠️ 混合行为：{recreated_count}/{len(model_ids)}轮重新创建")
            print(f"   ⚠️ 部分学习成果会丢失")
            result = "mixed"
        
        print(f"\n🎉 多轮训练完成！")
        print(f"📊 最终统计:")
        print(f"   - 总轮次: {total_rounds}")
        print(f"   - 总样本: {len(model.eegnet_model.training_data)}")
        print(f"   - 迁移学习: {'❌ 未使用' if not getattr(model_info, 'used_transfer_learning', True) else '✅ 已使用'}")
        print(f"   - 行为评估: {result}")
        
        return result == "perfect"
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comparison_with_transfer_learning():
    """对比普通训练和迁移学习的多轮训练行为"""
    print("\n📋 对比普通训练和迁移学习")
    print("=" * 60)
    
    results = {}
    
    for training_type in ["normal", "transfer"]:
        print(f"\n🔍 测试{training_type}训练")
        print("-" * 30)
        
        try:
            model = MotorImageryModel(f"Comparison_{training_type}_Test")
            model_info = model.get_model_info()
            
            if training_type == "normal":
                model_info.transfer_learning = False
                print("🔧 禁用迁移学习")
            else:
                model_info.transfer_learning = True
                model_info.finetune_layers = 3
                print("✅ 启用迁移学习")
            
            model_ids = []
            total_time = 0
            
            # 进行3轮训练
            for round_num in range(1, 4):
                # 添加数据
                for i in range(8):  # 每轮8个样本
                    data = np.random.randn(8, 250) * 50
                    label = i % 2
                    model.add_training_data(data, label)
                
                # 记录模型ID
                before_id = id(model.eegnet_model.model) if model.eegnet_model.model else None
                
                # 训练
                config = TrainingConfig(epochs=2, batch_size=4, learning_rate=0.001)
                start_time = time.time()
                success = model.train_model(config=config)
                round_time = time.time() - start_time
                total_time += round_time
                
                # 记录结果
                after_id = id(model.eegnet_model.model) if model.eegnet_model.model else None
                recreated = (before_id != after_id) if before_id else True
                
                model_ids.append(recreated)
                
                if success:
                    model_info = model.get_model_info()
                    if hasattr(model_info, 'performance') and model_info.performance:
                        final_accuracy = model_info.performance.accuracy
                    else:
                        final_accuracy = 0.0
                
                print(f"   第{round_num}轮: {round_time:.1f}秒, {'重新创建' if recreated else '复用模型'}")
            
            # 分析行为
            recreated_count = sum(model_ids)
            if recreated_count == 1:  # 只有第一轮重新创建
                behavior = "累积学习"
            elif recreated_count == len(model_ids):  # 每轮都重新创建
                behavior = "重新开始"
            else:
                behavior = "混合模式"
            
            results[training_type] = {
                'total_time': total_time,
                'final_accuracy': final_accuracy,
                'behavior': behavior,
                'recreated_count': recreated_count,
                'samples': len(model.eegnet_model.training_data)
            }
            
            print(f"✅ {training_type}训练完成: {behavior}, 总时间{total_time:.1f}秒")
            
        except Exception as e:
            print(f"❌ {training_type}训练失败: {e}")
            results[training_type] = {'error': str(e)}
    
    # 显示对比结果
    print(f"\n📊 对比结果:")
    print("-" * 60)
    for training_type, result in results.items():
        if 'error' in result:
            print(f"{training_type:12s}: ❌ 失败 - {result['error']}")
        else:
            print(f"{training_type:12s}: {result['behavior']:8s}, ⏱️ {result['total_time']:.1f}秒, 📊 {result['final_accuracy']*100:.1f}%, 📈 {result['samples']}样本")
    
    return True


def main():
    """主函数"""
    print("🧪 修复后普通训练多轮训练测试")
    print("=" * 70)
    print("测试目标:")
    print("1. 验证修复后普通训练的多轮训练行为")
    print("2. 确认学习成果能够累积保持")
    print("3. 对比普通训练和迁移学习的行为")
    print()
    
    # 运行测试
    test1_success = test_fixed_normal_training()
    test2_success = test_comparison_with_transfer_learning()
    
    print(f"\n📊 测试结果总结:")
    print(f"   修复验证测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   对比测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎊 所有测试通过！")
        print("\n📋 修复效果:")
        print("✅ 普通训练现在也能累积学习成果")
        print("✅ 不再每轮重新创建模型")
        print("✅ 与迁移学习行为一致")
        print("\n🎯 用户体验:")
        print("✅ 无论是否勾选迁移学习，都能累积学习")
        print("✅ 多轮训练效果更好")
        print("✅ 个性化程度逐步提升")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
