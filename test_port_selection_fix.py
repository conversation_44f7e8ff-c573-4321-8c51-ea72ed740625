#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试端口选择修复功能
Test Port Selection Fix

作者: AI Assistant
版本: 1.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_port_selection_logic():
    """测试端口选择逻辑"""
    print("=" * 60)
    print("端口选择逻辑测试")
    print("=" * 60)
    
    # 模拟下拉框数据结构
    class MockComboBox:
        def __init__(self):
            self.items = []
            self.current_index = -1
        
        def addItem(self, text, data=None):
            self.items.append({'text': text, 'data': data})
        
        def count(self):
            return len(self.items)
        
        def itemText(self, index):
            if 0 <= index < len(self.items):
                return self.items[index]['text']
            return ""
        
        def itemData(self, index):
            if 0 <= index < len(self.items):
                return self.items[index]['data']
            return None
        
        def setCurrentIndex(self, index):
            if 0 <= index < len(self.items):
                self.current_index = index
                return True
            return False
        
        def currentIndex(self):
            return self.current_index
        
        def currentText(self):
            if 0 <= self.current_index < len(self.items):
                return self.items[self.current_index]['text']
            return ""
        
        def currentData(self):
            if 0 <= self.current_index < len(self.items):
                return self.items[self.current_index]['data']
            return None
        
        def findText(self, text):
            for i, item in enumerate(self.items):
                if item['text'] == text:
                    return i
            return -1
    
    # 模拟端口设置函数
    def set_current_port(combo_box, port_num: int):
        """设置当前选中的端口号"""
        target_port = f"COM{port_num}"
        
        # 方法1: 先尝试通过 itemData 查找
        for i in range(combo_box.count()):
            item_data = combo_box.itemData(i)
            if item_data == target_port:
                combo_box.setCurrentIndex(i)
                print(f"  ✅ 通过itemData设置端口: {target_port} (索引: {i})")
                return True
        
        # 方法2: 通过文本精确匹配
        exact_index = combo_box.findText(target_port)
        if exact_index >= 0:
            combo_box.setCurrentIndex(exact_index)
            print(f"  ✅ 通过精确匹配设置端口: {target_port} (索引: {exact_index})")
            return True
        
        # 方法3: 通过文本部分匹配（处理 "COM7 (可用)" 格式）
        for i in range(combo_box.count()):
            item_text = combo_box.itemText(i)
            if item_text.startswith(target_port):
                combo_box.setCurrentIndex(i)
                print(f"  ✅ 通过部分匹配设置端口: {target_port} -> {item_text} (索引: {i})")
                return True
        
        # 如果都找不到，设置为第一个选项
        print(f"  ❌ 未找到端口 {target_port}，设置为第一个选项")
        if combo_box.count() > 0:
            combo_box.setCurrentIndex(0)
            return False
        
        return False
    
    # 测试场景1: 标准端口列表
    print("\n测试场景1: 标准端口列表")
    combo1 = MockComboBox()
    for i in range(1, 11):
        combo1.addItem(f"COM{i}", f"COM{i}")
    
    test_ports = [1, 5, 7, 10]
    for port in test_ports:
        print(f"设置端口 {port}:")
        set_current_port(combo1, port)
        print(f"  当前选择: {combo1.currentText()} (数据: {combo1.currentData()})")
    
    # 测试场景2: 带可用标识的端口列表
    print("\n测试场景2: 带可用标识的端口列表")
    combo2 = MockComboBox()
    # 模拟系统检测到的可用端口
    available_ports = ["COM3", "COM7", "COM8"]
    for port in available_ports:
        combo2.addItem(f"{port} (可用)", port)
    # 添加其他常用端口
    for i in range(1, 11):
        port = f"COM{i}"
        if port not in available_ports:
            combo2.addItem(port, port)
    
    test_ports = [1, 3, 7, 8, 15]
    for port in test_ports:
        print(f"设置端口 {port}:")
        set_current_port(combo2, port)
        current_text = combo2.currentText()
        current_data = combo2.currentData()
        print(f"  当前选择: {current_text} (数据: {current_data})")
        
        # 验证端口号提取
        if current_data and current_data.startswith('COM'):
            extracted_port = int(current_data[3:])
            print(f"  提取的端口号: {extracted_port}")
    
    # 测试场景3: 混合格式
    print("\n测试场景3: 混合格式")
    combo3 = MockComboBox()
    combo3.addItem("COM3 (可用)", "COM3")
    combo3.addItem("COM7 (可用)", "COM7")
    combo3.addItem("COM1", "COM1")
    combo3.addItem("COM2", "COM2")
    combo3.addItem("COM8", "COM8")
    
    test_ports = [1, 3, 7, 8, 99]  # 99是不存在的端口
    for port in test_ports:
        print(f"设置端口 {port}:")
        success = set_current_port(combo3, port)
        current_text = combo3.currentText()
        current_data = combo3.currentData()
        status = "✅" if success else "⚠️"
        print(f"  {status} 当前选择: {current_text} (数据: {current_data})")
    
    return True

def test_config_persistence():
    """测试配置持久化"""
    print("\n" + "=" * 60)
    print("配置持久化测试")
    print("=" * 60)
    
    try:
        from utils.app_config import AppConfig
        
        # 获取当前配置
        current_port = AppConfig.STIMULATION_CONFIG.get('port_num', 1)
        print(f"当前配置的端口号: {current_port}")
        
        # 模拟保存不同端口号
        test_ports = [1, 7, 10]
        for port in test_ports:
            # 模拟保存
            AppConfig.STIMULATION_CONFIG['port_num'] = port
            saved_port = AppConfig.STIMULATION_CONFIG.get('port_num')
            print(f"保存端口 {port}: 读取结果 {saved_port} {'✅' if saved_port == port else '❌'}")
        
        # 恢复原始配置
        AppConfig.STIMULATION_CONFIG['port_num'] = current_port
        print(f"恢复原始配置: {current_port}")
        
    except Exception as e:
        print(f"❌ 配置持久化测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始端口选择修复测试...")
    
    success = True
    success &= test_port_selection_logic()
    success &= test_config_persistence()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试通过！")
        print("端口选择修复功能正常工作。")
        print("\n修复要点:")
        print("1. 使用多种匹配方式确保端口正确设置")
        print("2. 支持带标识的端口名格式")
        print("3. 提供备选方案防止设置失败")
        print("4. 详细的日志记录便于调试")
    else:
        print("❌ 部分测试失败！")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
