#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激治疗页面迁移测试脚本
Test script for electrical stimulation treatment page migration
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ui_import():
    """测试UI模块导入"""
    try:
        from ui.treatment_ui import TreatmentWidget
        print("✅ TreatmentWidget 导入成功")
        return True
    except ImportError as e:
        print(f"❌ TreatmentWidget 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ TreatmentWidget 导入异常: {e}")
        return False

def test_ui_creation():
    """测试UI创建"""
    try:
        # 检查PySide6是否可用
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import Qt
        except ImportError:
            print("⚠️ PySide6未安装，跳过UI创建测试")
            return True
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入并创建UI
        from ui.treatment_ui import TreatmentWidget

        # 创建治疗界面实例
        treatment_ui = TreatmentWidget()
        print("✅ TreatmentWidget 实例创建成功")
        
        # 检查关键属性是否存在
        required_attributes = [
            # 脑电设备相关
            'eeg_connect_button',
            'eeg_status_label',
            
            # 电刺激设备相关
            'stimulation_connect_button',
            'stimulation_status_label',
            'stimulation_port_combo',
            
            # 通道控制相关
            'channel_a_checkbox',
            'channel_a_current',
            'channel_b_checkbox', 
            'channel_b_current',
            'channel_a_status_label',
            'channel_b_status_label',
            
            # 控制按钮相关
            'start_stimulation_button',
            'stop_stimulation_button',
            'start_training_button',
            'stop_training_button',
            
            # 日志相关
            'training_log',
            'stimulation_log'
        ]
        
        missing_attributes = []
        for attr in required_attributes:
            if not hasattr(treatment_ui, attr):
                missing_attributes.append(attr)
        
        if missing_attributes:
            print(f"❌ 缺少必需的属性: {missing_attributes}")
            return False
        else:
            print("✅ 所有必需的UI属性都存在")
        
        # 检查关键方法是否存在
        required_methods = [
            'add_training_log',
            'add_stimulation_log',
            'toggle_stimulation_connection',
            'start_stimulation',
            'stop_stimulation',
            'on_channel_a_current_changed',
            'on_channel_b_current_changed'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(treatment_ui, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少必需的方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的方法都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ UI创建测试失败: {e}")
        return False

def test_stimulation_functionality():
    """测试电刺激功能完整性"""
    try:
        from core.stimulation_device import StimulationDevice
        print("✅ StimulationDevice 导入成功")
        
        # 检查设备类是否有必需的方法
        required_methods = [
            'connect',
            'disconnect',
            'start_stimulation',
            'stop_stimulation',
            'set_current',
            'is_connected'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(StimulationDevice, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ StimulationDevice 缺少必需的方法: {missing_methods}")
            return False
        else:
            print("✅ StimulationDevice 所有必需的方法都存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ StimulationDevice 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 电刺激功能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("电刺激治疗页面迁移测试")
    print("=" * 60)
    
    tests = [
        ("UI模块导入测试", test_ui_import),
        ("UI创建测试", test_ui_creation),
        ("电刺激功能测试", test_stimulation_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！电刺激功能迁移成功！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查迁移结果")
        return 1

if __name__ == "__main__":
    sys.exit(main())
