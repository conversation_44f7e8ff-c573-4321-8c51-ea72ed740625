import sys
import random
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QListWidget
)
from PySide6.QtCore import Qt, QTimer
import pyqtgraph as pg
import numpy as np

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("脑机接口医疗器械界面 - PySide6")
        self.resize(900, 600)

        # 主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)

        # 侧边导航
        self.nav_list = QListWidget()
        self.nav_list.addItems(["首页", "脑波监测", "设备设置", "用户管理"])
        self.nav_list.setFixedWidth(150)
        main_layout.addWidget(self.nav_list)

        # 内容区
        self.content_area = QWidget()
        content_layout = QVBoxLayout()
        self.content_area.setLayout(content_layout)
        main_layout.addWidget(self.content_area)

        # 脑波数据显示区 - 用Label显示数值
        self.data_label = QLabel("脑波数据：-- μV")
        self.data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.data_label.setStyleSheet("font-size: 24px;")
        content_layout.addWidget(self.data_label)

        # 操作按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始采集")
        self.stop_btn = QPushButton("停止采集")
        btn_layout.addWidget(self.start_btn)
        btn_layout.addWidget(self.stop_btn)
        content_layout.addLayout(btn_layout)

        # pyqtgraph实时波形图（脑波信号模拟）
        self.graph_widget = pg.PlotWidget()
        self.graph_widget.setYRange(-3, 3)
        content_layout.addWidget(self.graph_widget)

        self.x = np.arange(100)
        self.y = np.zeros(100)
        self.curve = self.graph_widget.plot(self.x, self.y, pen=pg.mkPen('c', width=2))

        self.ptr = 0

        # 定时器更新时间和刷新图表
        self.timer = QTimer()
        self.timer.setInterval(100)  # 100ms刷新一次
        self.timer.timeout.connect(self.update_data)

        self.start_btn.clicked.connect(self.timer.start)
        self.stop_btn.clicked.connect(self.timer.stop)

    def update_data(self):
        # 模拟脑波数值（随机）
        value = round(random.uniform(10.0, 100.0), 2)
        self.data_label.setText(f"脑波数据：{value} μV")

        # 模拟脑波信号变化，用正弦+随机噪声
        self.y[:-1] = self.y[1:]
        self.y[-1] = np.sin(self.ptr * 0.1) + np.random.normal(scale=0.2)
        self.curve.setData(self.x, self.y)
        self.ptr += 1

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
