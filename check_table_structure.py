#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def check_table_structure():
    """检查表结构"""
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 检查bingren表结构
        print("📋 bingren表结构:")
        bingren_info = db.execute_query("PRAGMA table_info(bingren)")
        for column in bingren_info:
            print(f"   {column['name']} ({column['type']}) - {'NOT NULL' if column['notnull'] else 'NULL'}")
        
        # 检查yiyuan表结构
        print("\n📋 yiyuan表结构:")
        yiyuan_info = db.execute_query("PRAGMA table_info(yiyuan)")
        for column in yiyuan_info:
            print(f"   {column['name']} ({column['type']}) - {'NOT NULL' if column['notnull'] else 'NULL'}")
        
        # 检查外键约束
        print("\n📋 bingren表外键:")
        fk_info = db.execute_query("PRAGMA foreign_key_list(bingren)")
        for fk in fk_info:
            print(f"   {fk['from']} -> {fk['table']}.{fk['to']}")
        
        # 检查当前数据
        print("\n📋 bingren表数据:")
        bingren_data = db.execute_query("SELECT * FROM bingren LIMIT 5")
        if bingren_data:
            for row in bingren_data:
                print(f"   {dict(row)}")
        else:
            print("   (无数据)")
        
        print("\n📋 yiyuan表数据:")
        yiyuan_data = db.execute_query("SELECT * FROM yiyuan")
        if yiyuan_data:
            for row in yiyuan_data:
                print(f"   {dict(row)}")
        else:
            print("   (无数据)")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False

if __name__ == "__main__":
    check_table_structure()
