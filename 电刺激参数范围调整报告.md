# 电刺激参数范围调整报告

## 调整需求

根据用户提供的电刺激设备参数规范，需要调整以下参数的取值范围：

### 原始参数范围要求
- **ActFreq (频率)**: 2~160Hz
- **PulseWidth (脉宽)**: 10~500μs  
- **RelaxTime (休息时间)**: 0~16S
- **ClimbTime (上升时间)**: 0~5S
- **WorkTime (工作时间)**: 0~30S
- **FallTime (下降时间)**: 0~5S
- **最大电流**: 1-100mA
- **最小电流**: 1-10mA
- **电流步长**: 固定为1
- **所有数值只接受整数**

## 实施的修改

### 1. UI组件调整 (`ui/settings_ui.py`)

#### 电流参数调整
```python
# 最大电流 - 改为QSpinBox，范围1-100
self.max_current_spin = QSpinBox()
self.max_current_spin.setRange(1, 100)
self.max_current_spin.setValue(int(stimulation_config.get('max_current', 50)))

# 最小电流 - 改为QSpinBox，范围1-10
self.min_current_spin = QSpinBox()
self.min_current_spin.setRange(1, 10)
self.min_current_spin.setValue(int(stimulation_config.get('min_current', 1)))

# 电流步长 - 固定为1，禁用编辑
self.current_step_spin = QSpinBox()
self.current_step_spin.setRange(1, 1)
self.current_step_spin.setValue(1)
self.current_step_spin.setEnabled(False)
```

#### 频率参数调整
```python
# 频率 - 改为QSpinBox，范围2-160Hz
self.default_frequency_spin = QSpinBox()
self.default_frequency_spin.setRange(2, 160)
self.default_frequency_spin.setValue(int(stimulation_config.get('default_frequency', 20)))
```

#### 脉宽参数调整
```python
# 脉宽 - 改为QSpinBox，范围10-500μs
self.default_pulse_width_spin = QSpinBox()
self.default_pulse_width_spin.setRange(10, 500)
self.default_pulse_width_spin.setValue(int(stimulation_config.get('default_pulse_width', 200)))
```

#### 时间参数调整
```python
# 休息时间 - 改为QSpinBox，范围0-16s
self.default_relax_time_spin = QSpinBox()
self.default_relax_time_spin.setRange(0, 16)
self.default_relax_time_spin.setValue(int(stimulation_config.get('default_relax_time', 5)))

# 工作时间 - 改为QSpinBox，范围0-30s
self.default_work_time_spin = QSpinBox()
self.default_work_time_spin.setRange(0, 30)
self.default_work_time_spin.setValue(int(stimulation_config.get('default_work_time', 10)))

# 上升时间 - 改为QSpinBox，范围0-5s
self.default_climb_time_spin = QSpinBox()
self.default_climb_time_spin.setRange(0, 5)
self.default_climb_time_spin.setValue(int(stimulation_config.get('default_climb_time', 2)))

# 下降时间 - 改为QSpinBox，范围0-5s
self.default_fall_time_spin = QSpinBox()
self.default_fall_time_spin.setRange(0, 5)
self.default_fall_time_spin.setValue(int(stimulation_config.get('default_fall_time', 2)))
```

#### 连接超时调整
```python
# 连接超时 - 改为QSpinBox，范围1-30s
self.connection_timeout_spin = QSpinBox()
self.connection_timeout_spin.setRange(1, 30)
self.connection_timeout_spin.setValue(int(stimulation_config.get('connection_timeout', 5)))
```

### 2. 配置文件调整 (`utils/app_config.py`)

```python
# 电刺激配置 - 所有值改为整数
STIMULATION_CONFIG = {
    'dll_path': PROJECT_ROOT / 'libs' / 'RecoveryDLL.dll',
    'max_current': 50,    # mA (1-100)
    'min_current': 1,     # mA (1-10)
    'current_step': 1,    # mA (固定为1)
    'default_frequency': 20,      # Hz (2-160)
    'default_pulse_width': 200,   # μs (10-500)
    'default_relax_time': 5,      # s (0-16)
    'default_climb_time': 2,      # s (0-5)
    'default_work_time': 10,      # s (0-30)
    'default_fall_time': 2,       # s (0-5)
    'default_wave_type': 0,       # 0: 双相波, 1: 单相波
    'max_channels': 2,            # 最大通道数
    'port_num': 1,                # 默认端口号
    'connection_timeout': 5,      # 连接超时时间(s) (1-30)
    'current_steps': [1],         # 电流调节步长选项（固定为1）
}
```

### 3. 重置功能调整

更新重置设置中的默认值，确保使用整数：

```python
# 重置电刺激设备配置
self._set_current_port(1)
self.max_current_spin.setValue(50)
self.min_current_spin.setValue(1)
self.current_step_spin.setValue(1)
self.default_frequency_spin.setValue(20)
self.default_pulse_width_spin.setValue(200)
self.default_relax_time_spin.setValue(5)
self.default_work_time_spin.setValue(10)
self.default_climb_time_spin.setValue(2)
self.default_fall_time_spin.setValue(2)
self.default_wave_type_combo.setCurrentIndex(0)
self.connection_timeout_spin.setValue(5)
```

## 调整效果

### 参数范围对比

| 参数 | 原范围 | 新范围 | 数据类型 |
|------|--------|--------|----------|
| 频率 | 1.0-100.0 Hz | 2-160 Hz | float → int |
| 脉宽 | 50.0-500.0 μs | 10-500 μs | float → int |
| 休息时间 | 1.0-60.0 s | 0-16 s | float → int |
| 上升时间 | 1.0-10.0 s | 0-5 s | float → int |
| 工作时间 | 1.0-60.0 s | 0-30 s | float → int |
| 下降时间 | 1.0-10.0 s | 0-5 s | float → int |
| 最大电流 | 1.0-100.0 mA | 1-100 mA | float → int |
| 最小电流 | 0.1-10.0 mA | 1-10 mA | float → int |
| 电流步长 | 0.1-5.0 mA | 1 mA (固定) | float → int |
| 连接超时 | 1.0-30.0 s | 1-30 s | float → int |

### 用户界面改进

1. **整数输入**: 所有参数现在只接受整数输入，避免小数点输入错误
2. **精确范围**: 参数范围严格按照设备规范设置
3. **固定步长**: 电流步长固定为1，简化操作
4. **禁用编辑**: 电流步长控件被禁用，防止误操作

### 兼容性保证

1. **向后兼容**: 现有配置文件会自动转换为整数值
2. **类型安全**: 使用 `int()` 转换确保数据类型正确
3. **边界检查**: UI控件自动限制输入范围

## 验证测试

### 边界值测试
- ✅ 最小值边界正确设置
- ✅ 最大值边界正确设置  
- ✅ 超出范围值被自动限制
- ✅ 整数类型强制执行

### 功能测试
- ✅ 参数保存功能正常
- ✅ 参数加载功能正常
- ✅ 重置功能使用新范围
- ✅ 下传参数功能兼容

## 使用说明

### 用户操作
1. 进入"系统设置" → "设备配置"
2. 在电刺激设备配置中调整参数
3. 所有输入框现在只接受整数
4. 电流步长已固定为1，无需调整
5. 参数范围已按设备规范限制

### 注意事项
- 电流步长控件已禁用，固定为1mA
- 所有时间参数单位为秒，只接受整数
- 频率和脉宽参数范围已按设备规范调整
- 保存设置后立即生效

## 总结

本次调整完全按照用户提供的设备参数规范实施，确保：

1. **参数范围准确**: 严格按照设备规范设置
2. **数据类型正确**: 所有参数改为整数类型
3. **用户体验优化**: 简化操作，防止错误输入
4. **系统稳定性**: 保持向后兼容，确保功能正常

所有修改已完成并经过验证，系统现在完全符合电刺激设备的参数要求。
