#!/usr/bin/env python3
"""
测试身份证号验证功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cardid_validation():
    """测试身份证号验证逻辑"""
    
    def validate_cardid(cardid_text):
        """模拟身份证号验证逻辑"""
        if cardid_text:  # 身份证号不是必填项，但如果填写了就需要验证
            # 检查是否为纯数字
            if not cardid_text.isdigit():
                return False, "身份证号必须为数字形式！"
            
            # 检查长度是否合理（一般身份证号为15位或18位，但允许不完整）
            if len(cardid_text) > 18:
                return False, "身份证号长度不能超过18位！"
        
        return True, "验证通过"
    
    # 测试用例
    test_cases = [
        ("", True, "空身份证号应该通过验证"),  # 空值应该通过
        ("123456789012345678", True, "18位数字身份证号应该通过"),  # 18位数字
        ("123456789012345", True, "15位数字身份证号应该通过"),  # 15位数字
        ("12345", True, "不完整的数字身份证号应该通过"),  # 不完整但是数字
        ("12345678901234567890", False, "超过18位的身份证号应该失败"),  # 超过18位
        ("12345678901234567a", False, "包含字母的身份证号应该失败"),  # 包含字母
        ("123-456-789", False, "包含特殊字符的身份证号应该失败"),  # 包含特殊字符
        ("123 456 789", False, "包含空格的身份证号应该失败"),  # 包含空格
    ]
    
    print("身份证号验证测试:")
    print("=" * 50)
    
    all_passed = True
    for cardid, expected, description in test_cases:
        result, message = validate_cardid(cardid)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status} | 输入: '{cardid}' | 期望: {'通过' if expected else '失败'} | 实际: {'通过' if result else '失败'}")
        print(f"      | 描述: {description}")
        if not passed:
            print(f"      | 错误: 期望 {'通过' if expected else '失败'}，但实际 {'通过' if result else '失败'}")
        print()
    
    print("=" * 50)
    if all_passed:
        print("✓ 所有测试用例都通过了！")
    else:
        print("✗ 有测试用例失败！")
    
    return all_passed

def test_input_validators():
    """测试输入验证器的正则表达式"""
    import re
    
    print("\n输入验证器正则表达式测试:")
    print("=" * 50)
    
    # 测试身份证号验证器
    cardid_pattern = r'^\d{0,18}$'
    cardid_regex = re.compile(cardid_pattern)
    
    cardid_tests = [
        ("", True, "空字符串"),
        ("123456789012345678", True, "18位数字"),
        ("12345", True, "5位数字"),
        ("123456789012345678901", False, "超过18位"),
        ("12345a", False, "包含字母"),
    ]
    
    print("身份证号验证器测试:")
    for test_input, expected, desc in cardid_tests:
        result = bool(cardid_regex.match(test_input))
        status = "✓" if result == expected else "✗"
        print(f"{status} '{test_input}' -> {result} ({desc})")
    
    # 测试患者编号验证器
    bianhao_pattern = r'^[1-9]\d{0,5}$'
    bianhao_regex = re.compile(bianhao_pattern)
    
    bianhao_tests = [
        ("1", True, "单位数"),
        ("123", True, "三位数"),
        ("999999", True, "六位数"),
        ("0", False, "不能以0开头"),
        ("01", False, "不能以0开头"),
        ("1234567", False, "超过6位"),
        ("abc", False, "非数字"),
    ]
    
    print("\n患者编号验证器测试:")
    for test_input, expected, desc in bianhao_tests:
        result = bool(bianhao_regex.match(test_input))
        status = "✓" if result == expected else "✗"
        print(f"{status} '{test_input}' -> {result} ({desc})")

if __name__ == "__main__":
    test_cardid_validation()
    test_input_validators()
