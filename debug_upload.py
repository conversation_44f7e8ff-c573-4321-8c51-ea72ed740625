#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试上传功能脚本
Debug Upload Functionality

用于调试患者数据上传返回-2错误的问题

作者: AI Assistant
版本: 1.0.0
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader, UploadStatus
from core.database_manager import DatabaseManager
import requests
import json

def setup_logging():
    """设置详细日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def debug_upload_issue():
    """调试上传问题"""
    print("🔍 调试患者数据上传返回-2错误")
    print("=" * 60)
    
    # 1. 初始化组件
    print("\n1. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 2. 获取医院信息
    print("\n2. 获取医院信息...")
    hospital_info = db_manager.get_hospital_info()
    print(f"医院信息: {hospital_info}")
    
    # 3. 创建测试患者数据（模拟用户输入的数据）
    print("\n3. 创建测试患者数据...")
    test_patient = {
        'bianhao': 77777,  # 使用新的编号避免重复
        'name': '调试患者',
        'age': 25,
        'xingbie': '男',
        'cardid': '110101199501011234',
        'zhenduan': '调试诊断',
        'bingshi': '调试既往史',
        'brhc': '左侧',
        'zhuzhi': '调试医生',
        'czy': '调试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1)
    }
    
    print(f"患者数据: {test_patient}")
    
    # 4. 详细检查JSON数据格式
    print("\n4. 检查JSON数据格式...")
    json_data = uploader._prepare_json_data(test_patient, hospital_info)
    print(f"JSON字符串: {json_data}")
    
    # 解析JSON验证格式
    try:
        parsed_json = json.loads(json_data)
        print(f"解析后的JSON: {json.dumps(parsed_json, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ JSON格式错误: {e}")
        return
    
    # 5. 构建完整的上传URL
    print("\n5. 构建上传URL...")
    base_url = uploader.base_url
    upload_url = f"{base_url}AppPatientServlet?act=sendPatients&data={json_data}"
    print(f"完整URL: {upload_url}")
    print(f"URL长度: {len(upload_url)}")
    
    # 6. 手动发送请求查看详细响应
    print("\n6. 手动发送请求...")
    try:
        response = requests.get(
            upload_url,
            timeout=30,
            headers={
                'User-Agent': 'NK-BCI-System/1.0',
                'Accept': 'application/json, text/plain, */*'
            }
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应文本: {response.text}")
        
        # 尝试解析JSON响应
        try:
            response_json = response.json()
            print(f"响应JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except:
            print("无法解析为JSON格式")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 7. 对比之前成功的数据格式
    print("\n7. 对比分析...")
    print("当前数据特点:")
    print(f"  - 患者编号: {test_patient['bianhao']} (类型: {type(test_patient['bianhao'])})")
    print(f"  - 患者姓名: '{test_patient['name']}'")
    print(f"  - 年龄: {test_patient['age']} (类型: {type(test_patient['age'])})")
    print(f"  - 医院ID: {hospital_info.get('id')} (类型: {type(hospital_info.get('id'))})")
    
    # 8. 检查可能的问题
    print("\n8. 可能的问题分析:")
    
    # 检查是否有空字段
    empty_fields = []
    for key, value in parsed_json.items():
        if value == "" or value is None:
            empty_fields.append(key)
    
    if empty_fields:
        print(f"⚠️ 发现空字段: {empty_fields}")
    else:
        print("✅ 没有空字段")
    
    # 检查字段类型
    print("字段类型检查:")
    for key, value in parsed_json.items():
        print(f"  - {key}: {value} (类型: {type(value).__name__})")
    
    # 9. 尝试最小化数据测试
    print("\n9. 尝试最小化数据测试...")
    minimal_patient = {
        'bianhao': 88888,
        'name': '最小测试',
        'age': 30,
        'xingbie': '男',
        'cardid': '',
        'zhenduan': '',
        'bingshi': '',
        'brhc': '左侧',
        'zhuzhi': '',
        'czy': '',
        'keshi': '康复科',
        'shebeiid': 'TEST',
        'yiyuanid': hospital_info.get('id', 1)
    }
    
    minimal_json = uploader._prepare_json_data(minimal_patient, hospital_info)
    minimal_url = f"{base_url}AppPatientServlet?act=sendPatients&data={minimal_json}"
    
    print(f"最小化JSON: {minimal_json}")
    
    try:
        minimal_response = requests.get(minimal_url, timeout=30)
        print(f"最小化测试响应: {minimal_response.text}")
    except Exception as e:
        print(f"最小化测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("调试完成！请查看上述信息分析问题原因。")

def main():
    """主函数"""
    setup_logging()
    
    try:
        debug_upload_issue()
    except Exception as e:
        print(f"\n💥 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
