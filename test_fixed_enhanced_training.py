#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的增强训练功能
Test Fixed Enhanced Training Functionality

作者: AI Assistant
版本: 1.0.0
"""

import numpy as np
import logging
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_training_fixed():
    """测试修复后的增强训练功能"""
    logger.info("🧪 测试修复后的增强训练功能")
    
    try:
        from core.ml_model import MotorImageryModel
        
        # 创建模型
        model = MotorImageryModel("test_fixed_model")
        
        # 生成测试数据 - 确保数据形状一致
        logger.info("生成测试数据...")
        for i in range(20):
            # 生成固定长度的脑电数据 (8通道, 250采样点)
            eeg_data = np.random.randn(8, 250) * 1000
            
            # 添加一些运动想象相关的模式
            if i % 2 == 1:  # 运动想象试验
                # 在C3, C4通道添加mu节律抑制
                t = np.linspace(0, 2, 250)
                mu_pattern = -200 * np.sin(2 * np.pi * 10 * t)
                eeg_data[2, :] += mu_pattern  # C3
                eeg_data[3, :] += mu_pattern  # C4
            
            # 添加训练数据
            label = 1 if i % 2 == 1 else 0
            success = model.add_training_data(eeg_data, label)
            if not success:
                logger.error(f"添加样本 {i} 失败")
                return False
        
        logger.info(f"成功添加 {model.model_info.total_samples} 个训练样本")
        
        # 使用增强特征训练
        logger.info("开始增强特征训练...")
        success = model.train_model(algorithm="lda", use_enhanced_features=True)
        
        if success:
            logger.info("✅ 增强特征训练成功！")
            logger.info(f"   训练轮次: {model.model_info.training_rounds}")
            logger.info(f"   总样本数: {model.model_info.total_samples}")
            if model.model_info.performance:
                logger.info(f"   准确率: {model.model_info.performance.accuracy:.3f}")
                logger.info(f"   精确率: {model.model_info.performance.precision:.3f}")
                logger.info(f"   召回率: {model.model_info.performance.recall:.3f}")
                logger.info(f"   F1分数: {model.model_info.performance.f1_score:.3f}")
            
            # 测试预测
            logger.info("测试预测功能...")
            test_data = np.random.randn(8, 250) * 1000
            # 添加运动想象模式
            t = np.linspace(0, 2, 250)
            mu_pattern = -200 * np.sin(2 * np.pi * 10 * t)
            test_data[2, :] += mu_pattern
            test_data[3, :] += mu_pattern
            
            prediction, confidence = model.predict(test_data)
            logger.info(f"   预测结果: {prediction}, 置信度: {confidence:.3f}")
            
            # 测试批量预测
            logger.info("测试批量预测...")
            batch_data = [np.random.randn(8, 250) * 1000 for _ in range(5)]
            batch_results = model.predict_batch(batch_data)
            logger.info(f"   批量预测结果: {batch_results}")
            
            return True
        else:
            logger.error("❌ 增强特征训练失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_traditional_training():
    """测试传统训练功能作为对比"""
    logger.info("🔄 测试传统训练功能")
    
    try:
        from core.ml_model import MotorImageryModel
        
        # 创建模型
        model = MotorImageryModel("test_traditional_model")
        
        # 生成测试数据
        logger.info("生成测试数据...")
        for i in range(20):
            eeg_data = np.random.randn(8, 250) * 1000
            
            if i % 2 == 1:
                t = np.linspace(0, 2, 250)
                mu_pattern = -200 * np.sin(2 * np.pi * 10 * t)
                eeg_data[2, :] += mu_pattern
                eeg_data[3, :] += mu_pattern
            
            label = 1 if i % 2 == 1 else 0
            model.add_training_data(eeg_data, label)
        
        # 使用传统特征训练
        logger.info("开始传统特征训练...")
        success = model.train_model(algorithm="lda", use_enhanced_features=False)
        
        if success:
            logger.info("✅ 传统特征训练成功！")
            if model.model_info.performance:
                logger.info(f"   准确率: {model.model_info.performance.accuracy:.3f}")
            
            # 测试预测
            test_data = np.random.randn(8, 250) * 1000
            prediction, confidence = model.predict(test_data)
            logger.info(f"   预测结果: {prediction}, 置信度: {confidence:.3f}")
            
            return True
        else:
            logger.error("❌ 传统特征训练失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 传统训练测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始测试修复后的增强训练功能")
    logger.info("=" * 60)
    
    tests = [
        ("增强特征训练", test_enhanced_training_fixed),
        ("传统特征训练", test_traditional_training),
    ]
    
    results = []
    
    for name, test_func in tests:
        logger.info(f"\n{'='*20} {name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
            if result:
                logger.info(f"✅ {name}测试成功")
            else:
                logger.warning(f"⚠️ {name}测试失败")
        except Exception as e:
            logger.error(f"❌ {name}测试异常: {e}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 测试结果: {success_count}/{total_count} 个测试成功")
    
    if success_count == total_count:
        logger.info("🎉 所有测试都通过！修复成功！")
        logger.info("\n✨ 修复内容:")
        logger.info("  • 修复了特征维度不匹配问题")
        logger.info("  • 改进了增强特征训练和评估流程")
        logger.info("  • 修复了自适应频带选择的数组形状问题")
        logger.info("  • 确保预测时使用正确的特征提取方法")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
