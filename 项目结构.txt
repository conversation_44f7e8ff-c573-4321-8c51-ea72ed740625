Python_NK_System/
├── main.py                     # 主程序入口
├── requirements.txt            # Python依赖包
├── README.md                   # 详细使用说明
├── 启动系统.bat               # Windows启动脚本
├── install_dependencies.py    # 依赖安装脚本
├── test_system.py             # 系统测试脚本
├── quick_start.py             # 快速功能演示
├── core/                      # 核心模块
│   ├── main_window.py         # 主窗口
│   ├── database_manager.py    # 数据库管理
│   └── logger_system.py       # 日志系统
├── ui/                        # 用户界面模块
│   ├── patient_management_ui.py # 患者管理界面
│   ├── treatment_ui.py        # 治疗系统界面
│   ├── report_ui.py           # 报告分析界面
│   └── settings_ui.py         # 系统设置界面
├── utils/                     # 工具模块
│   ├── app_config.py          # 应用配置
│   └── single_instance.py     # 单实例检查
└── resources/                 # 资源文件
    ├── styles/                # 样式文件
    ├── images/                # 图片资源
    └── templates/             # 报告模板