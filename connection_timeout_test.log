2025-06-03 15:32:37,610 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 15:32:37,611 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 15:32:37,611 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:32:37,612 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:32:37,613 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:37,613 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:37,614 - core.stimulation_device - DEBUG - 使用连接超时: 2秒
2025-06-03 15:32:37,614 - core.stimulation_device - DEBUG - DLL连接调用失败，错误码: -1
2025-06-03 15:32:37,715 - core.stimulation_device - DEBUG - 连接失败
2025-06-03 15:32:37,715 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 99
2025-06-03 15:32:37,715 - core.stimulation_device - DEBUG - 执行连接失败后清理，端口: 99
2025-06-03 15:32:37,715 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 15:32:37,715 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 15:32:38,217 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 15:32:38,217 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 15:32:38,719 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:32:38,720 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:32:38,821 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:32:38,922 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:32:39,023 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:32:39,124 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:32:40,228 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:40,229 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:40,230 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:32:40,731 - core.stimulation_device - DEBUG - 连接失败后清理完成
2025-06-03 15:32:42,734 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:32:42,734 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:32:42,735 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:32:42,735 - core.stimulation_device - DEBUG - 使用连接超时: 2秒
2025-06-03 15:32:43,435 - core.stimulation_device - DEBUG - DLL连接调用成功
2025-06-03 15:32:43,440 - core.stimulation_device - DEBUG - 连接成功完成
2025-06-03 15:32:43,440 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:32:43,561 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:43,561 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,562 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,562 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,562 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,577 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,577 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,592 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,592 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,592 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,593 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,607 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,607 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,608 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,608 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,623 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,623 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:32:43,623 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,811 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:32:43,812 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:32:43,813 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:32:43,813 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:32:43,827 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,827 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,843 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:43,843 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,844 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,844 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,844 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,845 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,845 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,874 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,874 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,875 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,875 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,905 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,905 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,905 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:43,921 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,921 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,922 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,922 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,936 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,936 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,952 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,952 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,952 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,953 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,968 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,968 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:43,984 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:43,984 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:44,000 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:44,000 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:44,000 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:44,001 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:44,016 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:44,016 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:44,126 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:32:44,126 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:32:44,126 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:32:44,228 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:32:44,328 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:32:44,429 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:32:44,530 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:32:45,635 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:45,635 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:45,635 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:32:45,635 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:32:45,636 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 15:32:45,636 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 15:32:45,636 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:32:45,637 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:32:45,637 - core.stimulation_device - DEBUG - 使用连接超时: 2秒
2025-06-03 15:32:45,637 - core.stimulation_device - DEBUG - DLL连接调用失败，错误码: -1
2025-06-03 15:32:45,637 - core.stimulation_device - DEBUG - 连接失败
2025-06-03 15:32:45,638 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 99
2025-06-03 15:32:45,638 - core.stimulation_device - DEBUG - 执行连接失败后清理，端口: 99
2025-06-03 15:32:45,638 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 15:32:45,638 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 15:32:46,139 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 15:32:46,139 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 15:32:46,640 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:32:46,640 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:32:46,742 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:32:46,843 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:32:46,943 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:32:47,045 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:32:48,150 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:48,150 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:48,151 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:32:48,652 - core.stimulation_device - DEBUG - 连接失败后清理完成
2025-06-03 15:32:50,653 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:32:50,653 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:32:50,653 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:32:50,653 - core.stimulation_device - DEBUG - 使用连接超时: 2秒
2025-06-03 15:32:51,368 - core.stimulation_device - DEBUG - DLL连接调用成功
2025-06-03 15:32:51,458 - core.stimulation_device - DEBUG - 连接成功完成
2025-06-03 15:32:51,458 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:32:51,586 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:51,586 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,586 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,587 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,587 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,602 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,602 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,617 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,618 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,618 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,619 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,632 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:32:51,822 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:32:51,822 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:32:51,823 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:32:51,824 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:32:51,838 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:51,838 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,838 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,854 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,854 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,854 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,855 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,885 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,885 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,900 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,900 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,915 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,915 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,916 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:32:51,931 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,931 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,932 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,932 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,946 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,946 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,946 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,947 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,962 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,962 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:51,991 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:51,991 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:52,007 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:52,007 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:52,007 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:52,007 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:52,023 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:52,023 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:52,024 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:32:52,024 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:32:52,132 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:32:52,133 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:32:52,134 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:32:52,236 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:32:52,337 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:32:52,438 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:32:52,539 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:32:53,643 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:53,643 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:53,644 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:32:53,644 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:32:53,644 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 15:32:53,647 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:32:53,647 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:32:53,647 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:32:53,648 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:32:53,749 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:32:53,851 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:32:53,952 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:32:54,054 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:32:55,157 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:32:55,157 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:32:55,159 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:32:55,159 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:32:55,159 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 15:33:26,084 - core.stimulation_device - INFO - 电刺激设备控制器初始化完成
2025-06-03 15:33:26,085 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 15:33:26,085 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:33:26,085 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:33:26,087 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:26,087 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:26,087 - core.stimulation_device - DEBUG - 使用连接超时: 5秒
2025-06-03 15:33:26,088 - core.stimulation_device - DEBUG - DLL连接调用失败，错误码: -1
2025-06-03 15:33:26,088 - core.stimulation_device - DEBUG - 连接失败
2025-06-03 15:33:26,089 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 99
2025-06-03 15:33:26,089 - core.stimulation_device - DEBUG - 执行连接失败后清理，端口: 99
2025-06-03 15:33:26,089 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 15:33:26,089 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 15:33:26,591 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 15:33:26,591 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 15:33:27,091 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:33:27,091 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:33:27,192 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:33:27,294 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:33:27,394 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:33:27,496 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:33:28,611 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:28,611 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:28,611 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:33:29,113 - core.stimulation_device - DEBUG - 连接失败后清理完成
2025-06-03 15:33:31,116 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:33:31,117 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:33:31,118 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:33:31,118 - core.stimulation_device - DEBUG - 使用连接超时: 5秒
2025-06-03 15:33:31,843 - core.stimulation_device - DEBUG - DLL连接调用成功
2025-06-03 15:33:31,924 - core.stimulation_device - DEBUG - 连接成功完成
2025-06-03 15:33:31,924 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:33:32,062 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:32,063 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,063 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,064 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,064 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,064 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,064 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,065 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,065 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,093 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:33:32,279 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:33:32,280 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:33:32,281 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:33:32,282 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:33:32,295 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,295 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,295 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,295 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,295 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:32,310 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,310 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,326 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,326 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,327 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,328 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,342 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,342 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,342 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,342 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,357 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,358 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,373 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,374 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,374 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:32,374 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,375 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,389 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,389 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,404 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,404 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,405 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,405 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,419 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,419 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,419 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,420 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,451 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,451 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,452 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,452 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,466 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,467 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,482 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,482 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,483 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:32,483 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:32,605 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:33:32,605 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:33:32,605 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:33:32,706 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:33:32,807 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:33:32,908 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:33:33,010 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:33:34,116 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:34,117 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:34,117 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:33:34,118 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:33:34,118 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 15:33:34,119 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 99
2025-06-03 15:33:34,119 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:33:34,119 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:33:34,120 - core.stimulation_device - DEBUG - 使用连接超时: 5秒
2025-06-03 15:33:34,120 - core.stimulation_device - DEBUG - DLL连接调用失败，错误码: -1
2025-06-03 15:33:34,221 - core.stimulation_device - DEBUG - 连接失败
2025-06-03 15:33:34,221 - core.stimulation_device - WARNING - 连接电刺激设备超时，端口: 99
2025-06-03 15:33:34,221 - core.stimulation_device - DEBUG - 执行连接失败后清理，端口: 99
2025-06-03 15:33:34,221 - core.stimulation_device - DEBUG - 强制释放端口 99 的资源
2025-06-03 15:33:34,222 - core.stimulation_device - DEBUG - 端口状态检查: 关闭
2025-06-03 15:33:34,723 - core.stimulation_device - DEBUG - 端口已成功关闭
2025-06-03 15:33:34,723 - core.stimulation_device - DEBUG - 端口 99 资源强制释放完成
2025-06-03 15:33:35,225 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:33:35,225 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:33:35,327 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:33:35,427 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:33:35,529 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:33:35,629 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:33:36,732 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:36,733 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:36,733 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:33:37,235 - core.stimulation_device - DEBUG - 连接失败后清理完成
2025-06-03 15:33:39,238 - core.stimulation_device - INFO - 正在连接电刺激设备，端口: 7
2025-06-03 15:33:39,238 - core.stimulation_device - DEBUG - 执行连接前预清理
2025-06-03 15:33:39,238 - core.stimulation_device - DEBUG - 连接前预清理完成
2025-06-03 15:33:39,239 - core.stimulation_device - DEBUG - 使用连接超时: 5秒
2025-06-03 15:33:39,957 - core.stimulation_device - DEBUG - DLL连接调用成功
2025-06-03 15:33:40,044 - core.stimulation_device - DEBUG - 连接成功完成
2025-06-03 15:33:40,044 - core.stimulation_device - INFO - 电刺激设备连接成功
2025-06-03 15:33:40,176 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:40,177 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,177 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,192 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,193 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,193 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,194 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,207 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,208 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,208 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,209 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,223 - core.stimulation_device - INFO - 设备状态切换成功
2025-06-03 15:33:40,411 - core.stimulation_device - INFO - 设备信息读取成功: DeviceInfo(device_id='4e43432d', firmware_version='50454d47', hardware_version='00000000', serial_number='0000000002000302')
2025-06-03 15:33:40,412 - core.stimulation_device - INFO - 状态监控线程启动
2025-06-03 15:33:40,412 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:33:40,413 - core.stimulation_device - INFO - 状态监控线程停止
2025-06-03 15:33:40,427 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,427 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,427 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:40,427 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,428 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,442 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,442 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,457 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,457 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,458 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,458 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,472 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,472 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,472 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,473 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,504 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,504 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,504 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=0
2025-06-03 15:33:40,505 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,505 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,520 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,520 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,535 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,535 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,536 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,536 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,582 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,582 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,583 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,583 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,583 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,584 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,612 - core.stimulation_device - DEBUG - 回调函数收到数据: nSize=6
2025-06-03 15:33:40,612 - core.stimulation_device - DEBUG - 设备状态数据(6个short): [21930, 1, 0, 0, 0, 0], A通道=0, B通道=0
2025-06-03 15:33:40,738 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:33:40,739 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:33:40,740 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:33:40,841 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:33:40,943 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:33:41,043 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:33:41,144 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:33:42,250 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:42,251 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:42,251 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:33:42,251 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:33:42,252 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
2025-06-03 15:33:42,255 - core.stimulation_device - INFO - 正在断开电刺激设备连接
2025-06-03 15:33:42,255 - core.stimulation_device - INFO - 电刺激设备断开成功
2025-06-03 15:33:42,255 - core.stimulation_device - DEBUG - 开始完整DLL重置
2025-06-03 15:33:42,255 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 1)
2025-06-03 15:33:42,357 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 2)
2025-06-03 15:33:42,458 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 3)
2025-06-03 15:33:42,559 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 4)
2025-06-03 15:33:42,660 - core.stimulation_device - DEBUG - DLL端口关闭成功 (尝试 5)
2025-06-03 15:33:43,763 - core.stimulation_device - INFO - DLL函数原型定义完成
2025-06-03 15:33:43,763 - core.stimulation_device - INFO - 成功加载DLL: D:\NK_QT\QT6\NK\NK\Python_NK_System\libs\RecoveryDLL.dll
2025-06-03 15:33:43,763 - core.stimulation_device - DEBUG - 完整DLL重置成功
2025-06-03 15:33:43,764 - core.stimulation_device - DEBUG - 增强DLL重置成功
2025-06-03 15:33:43,764 - core.stimulation_device - DEBUG - 设备状态已完全重置，包括增强的DLL重置
