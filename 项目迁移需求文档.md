# 医疗级脑机接口系统Python迁移需求文档

## 项目概述
将现有的QT医疗级脑机接口系统迁移到Python平台，保持医疗级别的可靠性、准确性和稳定性。

## 迁移目标
1. **功能完整性**：保持原系统所有功能
2. **医疗级标准**：符合医疗器械软件标准
3. **模块化设计**：便于后续单独升级
4. **性能优化**：提高运动想象二分类识别率
5. **安全性增强**：新增日志系统和权限管理

## 系统架构分析

### 原QT系统主要模块
1. **主界面控制** (NK类) - 用户界面和业务逻辑
2. **数据库管理** (Data_connect) - SQLite数据库操作
3. **脑电信号处理**：
   - 串口通信 (SerialPort) - 脑电设备通信
   - 数据预处理 (Preprocessor) - 滤波、去噪、阻抗检测
   - 特征提取 (FeatureExtractor) - 频带功率、ERD/ERS特征
4. **电刺激控制** - RecoveryModuleDLL接口
5. **网络通信**：
   - HTTP通信 - 平台数据上传
   - UDP通信 - VR动画系统
6. **用户管理** - 患者、医生、操作员管理
7. **治疗系统** - 训练、反馈、记录
8. **报告系统** - 治疗报告生成
9. **语音系统** - 训练语音指导

### 数据库表结构
- `bingren` - 患者信息 (编号、姓名、年龄、性别、身份证、诊断等)
- `zhiliao` - 治疗记录 (治疗编号、患者编号、日期、得分、触发次数等)
- `Edata` - 脑电数据 (患者编号、时间戳、各频段功率、状态等)
- `yiyuan` - 医院信息 (ID、名称、科室、设备编号)
- `doctor` - 医生信息
- `operator` - 操作员信息

## Python迁移方案

### 1. 技术栈选择
- **界面框架**: PySide6 (保持QT界面风格)
- **数据库**: SQLite3 (Python标准库)
- **信号处理**: scipy, numpy, mne
- **机器学习**: scikit-learn, tensorflow
- **串口通信**: pyserial
- **网络通信**: requests (HTTP), socket (UDP)
- **图表绘制**: matplotlib, pyqtgraph
- **语音合成**: pyttsx3

### 2. 模块化设计

#### 核心模块
1. **main_window.py** - 主界面控制器
2. **database_manager.py** - 数据库管理
3. **eeg_processor.py** - 脑电信号处理
4. **stimulation_controller.py** - 电刺激控制
5. **network_manager.py** - 网络通信管理
6. **user_manager.py** - 用户权限管理
7. **treatment_system.py** - 治疗系统
8. **report_generator.py** - 报告生成
9. **audio_manager.py** - 语音管理
10. **logger_system.py** - 日志系统

#### 信号处理模块
1. **serial_communication.py** - 串口通信
2. **data_preprocessor.py** - 数据预处理
3. **feature_extractor.py** - 特征提取
4. **classifier.py** - 分类器
5. **impedance_monitor.py** - 阻抗监测

#### UI模块
1. **main_ui.py** - 主界面
2. **patient_management_ui.py** - 患者管理界面
3. **treatment_ui.py** - 治疗界面
4. **report_ui.py** - 报告界面
5. **settings_ui.py** - 设置界面

### 3. 新增功能

#### 日志系统
- 操作日志记录
- 错误日志追踪
- 系统状态监控
- 日志查询和导出

#### 权限管理系统
- 用户角色定义 (管理员、医生、技师、操作员)
- 功能权限控制
- 操作审计
- 密码策略

#### 算法优化
- 改进的滤波算法
- 自适应特征提取
- 集成学习分类器
- 实时性能优化

## 迁移实施计划

### 阶段1: 基础架构搭建 ✅
- [x] 创建项目结构
- [x] 设置开发环境
- [x] 基础模块框架

### 阶段2: 数据库和用户管理
- [ ] 数据库管理模块
- [ ] 用户管理系统
- [ ] 权限控制系统
- [ ] 日志系统

### 阶段3: 脑电信号处理
- [ ] 串口通信模块
- [ ] 数据预处理算法
- [ ] 特征提取优化
- [ ] 分类器实现

### 阶段4: 治疗系统
- [ ] 训练流程控制
- [ ] 实时反馈系统
- [ ] 电刺激控制接口
- [ ] 数据记录和分析

### 阶段5: 界面和交互
- [ ] 主界面实现
- [ ] 患者管理界面
- [ ] 治疗界面
- [ ] 报告生成界面

### 阶段6: 网络和外部接口
- [ ] HTTP通信模块
- [ ] UDP通信模块
- [ ] VR系统接口
- [ ] 语音提示系统

### 阶段7: 测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试
- [ ] 医疗标准验证

## 关键技术要求

### 医疗级标准
- 数据完整性保证
- 错误处理和恢复
- 操作可追溯性
- 系统稳定性

### 性能要求
- 实时数据处理 (<100ms延迟)
- 高识别准确率 (>85%)
- 系统响应时间 (<1s)
- 内存使用优化

### 兼容性要求
- 保持原有数据格式
- 兼容现有硬件设备
- 保持网络通信协议
- 支持原有文件格式

## 风险评估和应对

### 技术风险
- **风险**: Python性能可能不如C++
- **应对**: 关键算法使用Cython优化

### 兼容性风险
- **风险**: 硬件接口兼容问题
- **应对**: 保持原有DLL接口调用

### 数据安全风险
- **风险**: 患者数据泄露
- **应对**: 加强权限控制和数据加密

## 验收标准

### 功能验收
- [ ] 所有原有功能正常工作
- [ ] 新增功能符合需求
- [ ] 界面操作流畅

### 性能验收
- [ ] 识别准确率达标
- [ ] 系统响应时间达标
- [ ] 内存使用合理

### 安全验收
- [ ] 权限控制有效
- [ ] 日志记录完整
- [ ] 数据安全保护

## 项目时间表
- **总工期**: 预计4-6周
- **每阶段**: 3-5个工作日
- **测试时间**: 1周
- **部署时间**: 2-3天

---
*文档版本: v1.0*
*创建日期: 2024年*
*最后更新: 2024年*
