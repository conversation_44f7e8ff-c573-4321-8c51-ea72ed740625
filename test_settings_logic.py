#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设置保存逻辑（不依赖GUI）
验证配置变化检测逻辑
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_change_detection_logic():
    """测试变化检测逻辑"""
    print("=" * 60)
    print("设置变化检测逻辑测试")
    print("=" * 60)
    
    # 模拟原始配置
    original_config = {
        'hospital_info': {
            'hname': '原医院',
            'keshi': '原科室', 
            'shebeiid': 'ORIG001'
        },
        'stimulation_port': 1,
        'eeg_port': 'COM8',
        'font_size': 10,
        'log_level': 'INFO'
    }
    
    # 测试1: 无变化
    print("\n1. 测试无变化检测...")
    current_config = original_config.copy()
    current_config['hospital_info'] = original_config['hospital_info'].copy()
    
    changes = detect_changes(original_config, current_config)
    if not changes:
        print("✓ 无变化检测正确")
    else:
        print(f"✗ 无变化检测失败，检测到变化: {changes}")
    
    # 测试2: 医院信息变化
    print("\n2. 测试医院信息变化检测...")
    current_config = original_config.copy()
    current_config['hospital_info'] = {
        'hname': '新医院',
        'keshi': '新科室',
        'shebeiid': 'NEW001'
    }
    
    changes = detect_changes(original_config, current_config)
    expected_changes = [
        "医院名称: 原医院 → 新医院",
        "科室名称: 原科室 → 新科室", 
        "设备编号: ORIG001 → NEW001"
    ]
    
    if all(change in changes for change in expected_changes):
        print("✓ 医院信息变化检测正确")
        print(f"  检测到的变化: {changes}")
    else:
        print(f"✗ 医院信息变化检测失败")
        print(f"  期望: {expected_changes}")
        print(f"  实际: {changes}")
    
    # 测试3: 配置参数变化
    print("\n3. 测试配置参数变化检测...")
    current_config = original_config.copy()
    current_config['hospital_info'] = original_config['hospital_info'].copy()
    current_config['stimulation_port'] = 7
    current_config['font_size'] = 12
    current_config['log_level'] = 'DEBUG'
    
    changes = detect_changes(original_config, current_config)
    expected_changes = [
        "端口号: COM1 → COM7",
        "字体大小: 10 → 12",
        "日志级别: INFO → DEBUG"
    ]
    
    if any("端口号" in change for change in changes) and \
       any("字体大小" in change for change in changes) and \
       any("日志级别" in change for change in changes):
        print("✓ 配置参数变化检测正确")
        print(f"  检测到的变化: {changes}")
    else:
        print(f"✗ 配置参数变化检测失败")
        print(f"  期望包含: {expected_changes}")
        print(f"  实际: {changes}")
    
    # 测试4: 部分变化
    print("\n4. 测试部分变化检测...")
    current_config = original_config.copy()
    current_config['hospital_info'] = original_config['hospital_info'].copy()
    current_config['hospital_info']['hname'] = '部分修改医院'
    current_config['font_size'] = 14
    
    changes = detect_changes(original_config, current_config)
    
    if (len(changes) == 2 and 
        any("医院名称" in change for change in changes) and
        any("字体大小" in change for change in changes)):
        print("✓ 部分变化检测正确")
        print(f"  检测到的变化: {changes}")
    else:
        print(f"✗ 部分变化检测失败")
        print(f"  实际: {changes}")
    
    print("\n" + "=" * 60)
    print("逻辑测试完成")
    print("=" * 60)

def detect_changes(original, current):
    """检测配置变化（模拟设置界面的逻辑）"""
    changes = []
    
    # 检查医院信息变化
    orig_hospital = original.get('hospital_info', {})
    curr_hospital = current.get('hospital_info', {})
    
    if curr_hospital.get('hname') != orig_hospital.get('hname'):
        changes.append(f"医院名称: {orig_hospital.get('hname', '')} → {curr_hospital.get('hname', '')}")
    
    if curr_hospital.get('keshi') != orig_hospital.get('keshi'):
        changes.append(f"科室名称: {orig_hospital.get('keshi', '')} → {curr_hospital.get('keshi', '')}")
    
    if curr_hospital.get('shebeiid') != orig_hospital.get('shebeiid'):
        changes.append(f"设备编号: {orig_hospital.get('shebeiid', '')} → {curr_hospital.get('shebeiid', '')}")
    
    # 检查电刺激端口变化
    if current.get('stimulation_port') != original.get('stimulation_port'):
        changes.append(f"端口号: COM{original.get('stimulation_port')} → COM{current.get('stimulation_port')}")
    
    # 检查脑电端口变化
    if current.get('eeg_port') != original.get('eeg_port'):
        changes.append(f"脑电端口: {original.get('eeg_port')} → {current.get('eeg_port')}")
    
    # 检查字体大小变化
    if current.get('font_size') != original.get('font_size'):
        changes.append(f"字体大小: {original.get('font_size')} → {current.get('font_size')}")
    
    # 检查日志级别变化
    if current.get('log_level') != original.get('log_level'):
        changes.append(f"日志级别: {original.get('log_level')} → {current.get('log_level')}")
    
    return changes

def test_message_generation():
    """测试消息生成逻辑"""
    print("\n" + "=" * 60)
    print("消息生成逻辑测试")
    print("=" * 60)
    
    # 测试有变化时的消息
    changes = [
        "医院名称: 原医院 → 新医院",
        "字体大小: 10 → 12",
        "日志级别: INFO → DEBUG"
    ]
    
    message = generate_save_message(changes)
    print("有变化时的消息:")
    print(message)
    print()
    
    # 测试无变化时的消息
    message = generate_save_message([])
    print("无变化时的消息:")
    print(message)

def generate_save_message(changes):
    """生成保存消息（模拟设置界面的逻辑）"""
    if changes:
        return "设置保存成功！\n\n已更新的配置项：\n\n" + "\n".join(f"• {item}" for item in changes) + "\n\n配置将立即生效。"
    else:
        return "未检测到任何配置变化。\n\n当前设置已是最新状态，无需保存。"

def main():
    """主函数"""
    test_change_detection_logic()
    test_message_generation()
    print("\n🎉 逻辑测试完成！")

if __name__ == "__main__":
    main()
