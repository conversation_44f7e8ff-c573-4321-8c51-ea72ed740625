#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库修复脚本
用于修复用户管理相关的问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.logger_system import LoggerSystem
from core.auth_manager import AuthManager

def fix_database():
    """修复数据库问题"""
    print("=== NK脑机接口系统数据库修复工具 ===")
    
    try:
        # 初始化日志系统
        logger_system = LoggerSystem()
        logger_system.setup_logging()
        print("✓ 日志系统初始化成功")
        
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("✗ 数据库初始化失败")
            return False
        print("✓ 数据库初始化成功")
        
        # 检查当前用户状态
        print("\n--- 检查用户状态 ---")
        all_users = db_manager.execute_query("SELECT * FROM operator ORDER BY id")
        
        print(f"数据库中共有 {len(all_users)} 个用户:")
        for user in all_users:
            status = "活跃" if user['is_active'] else "停用"
            print(f"  ID: {user['id']}, 用户名: {user['name']}, 角色: {user['role']}, 状态: {status}")
        
        # 检查活跃的管理员
        active_admins = db_manager.execute_query(
            "SELECT * FROM operator WHERE role = 'admin' AND is_active = 1"
        )
        
        print(f"\n活跃的管理员用户: {len(active_admins)} 个")
        
        if len(active_admins) == 0:
            print("⚠️  警告：没有活跃的管理员用户！")
            
            # 查找被停用的管理员
            inactive_admins = db_manager.execute_query(
                "SELECT * FROM operator WHERE role = 'admin' AND is_active = 0"
            )
            
            if len(inactive_admins) > 0:
                print(f"发现 {len(inactive_admins)} 个被停用的管理员用户")
                
                # 重新激活第一个管理员
                admin_to_activate = inactive_admins[0]
                success = db_manager.execute_non_query(
                    "UPDATE operator SET is_active = 1 WHERE id = ?",
                    (admin_to_activate['id'],)
                )
                
                if success:
                    print(f"✓ 已重新激活管理员用户: {admin_to_activate['name']} (ID: {admin_to_activate['id']})")
                else:
                    print("✗ 重新激活管理员用户失败")
            else:
                # 创建新的默认管理员
                print("正在创建新的默认管理员账户...")
                
                import hashlib
                salt = "NK_BCI_SYSTEM_2024"
                password_hash = hashlib.sha256(("admin123" + salt).encode()).hexdigest()
                
                success = db_manager.execute_non_query(
                    """INSERT INTO operator (name, password, role, permissions, is_active)
                       VALUES (?, ?, ?, ?, 1)""",
                    ("admin", password_hash, "admin", "all")
                )
                
                if success:
                    print("✓ 已创建新的默认管理员账户: admin / admin123")
                else:
                    print("✗ 创建默认管理员账户失败")
        
        # 清理重复的用户
        print("\n--- 检查重复用户 ---")
        duplicate_check = db_manager.execute_query(
            """SELECT name, COUNT(*) as count 
               FROM operator 
               GROUP BY name 
               HAVING COUNT(*) > 1"""
        )
        
        if duplicate_check:
            print(f"发现 {len(duplicate_check)} 个重复的用户名:")
            for dup in duplicate_check:
                print(f"  用户名: {dup['name']}, 重复次数: {dup['count']}")
                
                # 获取该用户名的所有记录
                user_records = db_manager.execute_query(
                    "SELECT * FROM operator WHERE name = ? ORDER BY id",
                    (dup['name'],)
                )
                
                # 保留第一个记录，删除其他的
                for i, record in enumerate(user_records):
                    if i == 0:
                        # 确保第一个记录是活跃的
                        db_manager.execute_non_query(
                            "UPDATE operator SET is_active = 1 WHERE id = ?",
                            (record['id'],)
                        )
                        print(f"    保留记录 ID: {record['id']} (已激活)")
                    else:
                        # 删除重复记录
                        db_manager.execute_non_query(
                            "DELETE FROM operator WHERE id = ?",
                            (record['id'],)
                        )
                        print(f"    删除重复记录 ID: {record['id']}")
        else:
            print("✓ 没有发现重复用户")
        
        # 最终检查
        print("\n--- 修复后状态 ---")
        final_users = db_manager.execute_query("SELECT * FROM operator ORDER BY id")
        final_active_admins = db_manager.execute_query(
            "SELECT * FROM operator WHERE role = 'admin' AND is_active = 1"
        )
        
        print(f"总用户数: {len(final_users)}")
        print(f"活跃管理员数: {len(final_active_admins)}")
        
        for admin in final_active_admins:
            print(f"  管理员: {admin['name']} (ID: {admin['id']})")
        
        print("\n=== 数据库修复完成 ===")
        print("现在可以使用以下账户登录:")
        print("用户名: admin")
        print("密码: admin123")
        
        return True
        
    except Exception as e:
        print(f"✗ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db_manager' in locals():
            db_manager.close()

if __name__ == "__main__":
    fix_database()
    input("\n按回车键退出...")
