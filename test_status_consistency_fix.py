#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电刺激设备状态一致性修复
Test Stimulation Device Status Consistency Fix

这个脚本用于验证界面显示状态与设备实际状态的一致性
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device_qt import StimulationDeviceQt, DeviceStatus, ChannelState


def test_status_consistency():
    """测试状态一致性"""
    print("=" * 60)
    print("测试电刺激设备状态一致性修复")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    device = None
    try:
        # 1. 创建设备实例
        print("\n1. 创建电刺激设备实例...")
        device = StimulationDeviceQt()
        
        # 2. 尝试连接设备
        print("\n2. 尝试连接设备...")
        port_num = 7  # 默认端口
        if device.connect(port_num):
            print(f"   ✅ 设备连接成功 (端口: COM{port_num})")
            
            # 3. 设置电流并开始刺激
            print("\n3. 设置电流并开始刺激...")
            if device.set_current(1, 5.0):  # A通道设置5mA
                print("   ✅ A通道电流设置成功: 5.0mA")
                
                if device.start_stimulation(1):
                    print("   ✅ A通道刺激启动成功")
                    
                    # 4. 监控状态变化
                    print("\n4. 监控状态变化（15秒）...")
                    print("   时间 | 通道状态 | 状态说明")
                    print("   -----|----------|----------")
                    
                    for i in range(15):
                        time.sleep(1)
                        
                        # 获取实际通道状态
                        a_status = device.get_channel_status(1)
                        status_text = device.get_channel_status_text(a_status)
                        
                        # 模拟界面状态更新逻辑
                        if a_status == 0:  # 停止
                            ui_display = "关闭"
                            ui_style = "默认"
                        elif a_status == 1:  # 暂停
                            ui_display = "暂停"
                            ui_style = "橙色"
                        elif a_status == 2:  # 电流调节
                            ui_display = "电流调节"
                            ui_style = "蓝色"
                        elif a_status == 3:  # 正常工作
                            ui_display = "刺激中"
                            ui_style = "绿色"
                        else:
                            ui_display = f"未知({a_status})"
                            ui_style = "红色"
                        
                        print(f"   {i+1:2d}s  | {a_status:8d} | {status_text} -> 界面显示: {ui_display} ({ui_style})")
                        
                        # 检测到正常工作状态时的特殊标记
                        if a_status == 3:
                            print("   ✅ 检测到正常工作状态！设备正在刺激")
                    
                    # 5. 停止刺激
                    print("\n5. 停止刺激...")
                    if device.stop_all_stimulation():
                        print("   ✅ 刺激停止成功")
                        
                        # 等待状态稳定
                        time.sleep(1)
                        final_status = device.get_channel_status(1)
                        final_text = device.get_channel_status_text(final_status)
                        print(f"   最终状态: {final_status} ({final_text})")
                    else:
                        print("   ❌ 刺激停止失败")
                else:
                    print("   ❌ A通道刺激启动失败")
            else:
                print("   ❌ A通道电流设置失败")
                
        else:
            print(f"   ❌ 设备连接失败 (端口: COM{port_num})")
            print("   这可能是因为:")
            print("   - 设备未连接到指定端口")
            print("   - 设备驱动未安装")
            print("   - 端口被其他程序占用")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 确保设备被正确清理
        print("\n6. 清理设备...")
        if device:
            try:
                device.disconnect()
                print("   ✅ 设备清理完成")
            except Exception as e:
                print(f"   ⚠️  设备清理时发生错误: {e}")


def test_ui_status_mapping():
    """测试界面状态映射逻辑"""
    print("\n" + "=" * 60)
    print("测试界面状态映射逻辑")
    print("=" * 60)
    
    # 模拟_get_channel_display_info方法
    def get_channel_display_info(status: int) -> tuple:
        """根据通道状态获取显示信息"""
        if status == 0:  # 停止
            return "关闭", ""
        elif status == 1:  # 暂停
            return "暂停", "color: orange; font-weight: bold;"
        elif status == 2:  # 电流调节
            return "电流调节", "color: blue; font-weight: bold;"
        elif status == 3:  # 正常工作
            return "刺激中", "color: green; font-weight: bold;"
        else:
            return f"未知({status})", "color: red; font-weight: bold;"
    
    print("\n状态映射测试:")
    print("设备状态 | 界面显示 | 样式")
    print("---------|----------|----------")
    
    for status in range(5):
        display_text, style = get_channel_display_info(status)
        print(f"{status:8d} | {display_text:8s} | {style}")
    
    print("\n✅ 状态映射逻辑正确")


if __name__ == "__main__":
    print("电刺激设备状态一致性修复测试")
    print("这个测试验证界面显示状态与设备实际状态的一致性")
    
    # 测试状态一致性
    test_status_consistency()
    
    # 测试界面状态映射
    test_ui_status_mapping()
    
    print("\n🔧 修复总结:")
    print("1. ✅ 添加了update_channel_status_display()方法，基于设备实际状态更新界面")
    print("2. ✅ 添加了_get_channel_display_info()方法，提供状态到界面显示的映射")
    print("3. ✅ 修改了start_stimulation()和stop_stimulation()方法，不再直接设置界面状态")
    print("4. ✅ 添加了状态更新定时器，定期同步设备状态到界面显示")
    print("5. ✅ 在cleanup()方法中添加了定时器清理逻辑")
    print("\n📋 状态映射规则:")
    print("   0 (停止)     -> 关闭     (默认样式)")
    print("   1 (暂停)     -> 暂停     (橙色)")
    print("   2 (电流调节) -> 电流调节 (蓝色)")
    print("   3 (正常工作) -> 刺激中   (绿色)")
    print("   其他         -> 未知     (红色)")
    
    input("\n按回车键退出...")
