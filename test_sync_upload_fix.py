#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的同步上传功能
Test Fixed Sync Upload Functionality

验证改回同步上传后的功能是否正常

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader
from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_sync_upload_fix():
    """测试修复后的同步上传功能"""
    print("🔧 测试修复后的同步上传功能")
    print("=" * 50)
    
    # 1. 检查配置
    print("\n1. 检查配置...")
    retry_count = AppConfig.NETWORK_CONFIG['http']['retry_count']
    print(f"✅ 重试次数: {retry_count} 次")
    
    # 2. 初始化组件
    print("\n2. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 3. 获取医院信息
    hospital_info = db_manager.get_hospital_info()
    
    # 4. 创建测试患者数据
    print("\n3. 创建测试患者数据...")
    test_patient = {
        'bianhao': int(time.time()) % 1000000,
        'name': '同步上传测试患者',
        'age': 40,
        'xingbie': '男',
        'cardid': '110101198501011234',
        'zhenduan': '同步上传测试诊断',
        'bingshi': '同步上传测试既往史',
        'brhc': '左侧',
        'zhuzhi': '同步上传测试医生',
        'czy': '同步上传测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1),
        'status': 'pending'
    }
    
    print(f"患者编号: {test_patient['bianhao']}")
    print(f"患者姓名: {test_patient['name']}")
    
    # 5. 模拟同步上传流程
    print("\n4. 模拟同步上传流程...")
    print("📝 显示进度指示器: '正在保存中...'")
    
    start_time = time.time()
    upload_status = "pending"
    
    try:
        print("🌐 开始同步上传到平台...")
        
        # 执行同步上传
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        if upload_result.success:
            upload_status = "success"
            print(f"✅ 上传成功: {upload_result.message}")
        else:
            upload_status = "failed"
            if "重复或已存在" in upload_result.message:
                print(f"ℹ️ 数据重复: {upload_result.message}")
            else:
                print(f"⚠️ 上传失败: {upload_result.message}")
                
    except Exception as e:
        upload_status = "failed"
        print(f"❌ 上传异常: {e}")
    
    end_time = time.time()
    upload_duration = end_time - start_time
    
    # 6. 设置上传状态并保存到数据库
    print("\n5. 保存到本地数据库...")
    test_patient['status'] = upload_status
    
    success = db_manager.add_patient(test_patient)
    if success:
        print("✅ 本地保存成功！")
        print("🔄 隐藏进度指示器")
        print("🎉 显示成功消息: '添加患者成功！'")
    else:
        print("❌ 本地保存失败")
    
    # 7. 验证结果
    print("\n6. 验证结果...")
    print(f"⏱️ 总耗时: {upload_duration:.2f} 秒")
    print(f"📊 最终状态: {upload_status}")
    
    # 验证时间是否合理
    if upload_duration <= 2.0:  # 应该在2秒内完成
        print("✅ 响应时间符合预期！")
    else:
        print(f"⚠️ 响应时间过长: {upload_duration:.2f}秒")
    
    # 8. 测试网络失败情况
    print("\n7. 测试网络失败情况...")
    
    # 临时修改URL为无效地址
    original_base_url = uploader.base_url
    uploader.base_url = "http://invalid-url-for-testing.com/"
    
    test_patient_fail = test_patient.copy()
    test_patient_fail['bianhao'] = test_patient['bianhao'] + 1
    test_patient_fail['name'] = '网络失败测试患者'
    
    print("📝 显示进度指示器: '正在保存中...'")
    
    start_time = time.time()
    upload_status = "pending"
    
    try:
        print("🌐 开始同步上传到平台（无效URL）...")
        
        # 执行同步上传
        upload_result = uploader.upload_patient_data(test_patient_fail, hospital_info)
        upload_status = "failed"
        print(f"⚠️ 上传失败: {upload_result.message}")
        
    except Exception as e:
        upload_status = "failed"
        print(f"❌ 上传异常: {e}")
    
    end_time = time.time()
    failure_duration = end_time - start_time
    
    print(f"⏱️ 失败耗时: {failure_duration:.2f} 秒")
    
    # 验证失败时间是否合理
    if failure_duration <= 3.0:  # 应该在3秒内失败
        print("✅ 失败响应时间符合预期！")
    else:
        print(f"⚠️ 失败响应时间过长: {failure_duration:.2f}秒")
    
    # 恢复原始URL
    uploader.base_url = original_base_url
    
    # 9. 清理测试数据
    print("\n8. 清理测试数据...")
    try:
        db_manager.execute_non_query(
            "DELETE FROM bingren WHERE bianhao IN (?, ?)",
            (test_patient['bianhao'], test_patient_fail['bianhao'])
        )
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️ 测试数据清理失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 同步上传修复测试完成！")
    
    # 10. 总结修复效果
    print("\n📋 修复效果总结:")
    print("✅ 改回同步上传，确保数据立即上传")
    print("✅ 保持重试次数为1次，减少等待时间")
    print("✅ 显示'正在保存中...'提供用户反馈")
    print("✅ 上传完成后立即显示结果")
    print("✅ 消除了异步上传的定时器问题")
    
    print("\n🎯 用户体验:")
    print("- 点击保存 → 立即显示'正在保存中...'")
    print("- 同步执行上传 → 最多2秒内完成")
    print("- 隐藏进度指示器 → 显示'保存成功'")
    print("- 整个过程流畅，用户有明确反馈")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_sync_upload_fix()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
