#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QT风格电刺激设备测试
严格按照QT程序的电刺激控制逻辑进行测试
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.stimulation_device_qt import StimulationDevice, StimulationParameters

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('qt_stimulation_test.log', encoding='utf-8')
        ]
    )

def test_qt_stimulation_workflow():
    """测试QT风格的电刺激工作流程"""
    print("=" * 60)
    print("🔧 QT风格电刺激设备测试")
    print("严格按照原QT程序的控制逻辑")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        # 步骤1：Button_dianchiji_open - 连接设备
        print("\n📡 步骤1: 连接设备 (Button_dianchiji_open)")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功，已切换到循环刺激状态")
        
        # 显示初始状态
        status_info = device.get_status_info()
        print(f"   设备状态: {status_info['device_status']}")
        print(f"   A通道状态: {status_info['channel_a_status']}")
        print(f"   B通道状态: {status_info['channel_b_status']}")
        
        # 步骤2：设置刺激参数
        print("\n⚙️ 步骤2: 设置刺激参数")
        params = StimulationParameters(
            channel_num=1,
            frequency=25.0,
            pulse_width=250.0,
            relax_time=5.0,
            climb_time=2.0,
            work_time=10.0,
            fall_time=2.0,
            wave_type=0
        )
        
        if not device.set_stimulation_parameters(params):
            print("❌ 刺激参数设置失败")
            return False
        print("✅ 刺激参数设置成功")
        
        # 步骤3：spinBox_dianchiji_A - 调节A通道电流（自动3秒刺激）
        print("\n🎛️ 步骤3: 调节A通道电流 (spinBox_dianchiji_A)")
        print("   模拟用户调节spinBox，每次调节会自动触发3秒刺激")
        
        test_currents = [3.0, 5.0, 4.0]
        
        for i, current_ma in enumerate(test_currents, 1):
            print(f"\n   调节{i}: 设置A通道电流为{current_ma}mA")
            
            if device.adjust_current_with_feedback(1, current_ma):
                print(f"   ✅ 电流调节成功，自动刺激3秒")
                
                # 监控状态变化
                for j in range(5):
                    time.sleep(1)
                    a_status = device.get_channel_status(1)
                    status_text = device.get_channel_status_text(a_status)
                    print(f"   [{j+1}s] A通道状态: {status_text}")
                    
                    if a_status == 3:  # 正常工作状态
                        print("   ✅ 检测到正在刺激")
                    elif a_status == 0:  # 停止状态
                        print("   ✅ 刺激已自动停止")
                        break
            else:
                print(f"   ❌ 电流调节失败")
            
            print("   等待2秒后进行下次调节...")
            time.sleep(2)
        
        # 步骤4：spinBox_dianchiji_B - 调节B通道电流
        print("\n🎛️ 步骤4: 调节B通道电流 (spinBox_dianchiji_B)")
        
        if device.adjust_current_with_feedback(2, 6.0):
            print("   ✅ B通道电流调节成功，自动刺激3秒")
            
            # 监控B通道状态
            for i in range(5):
                time.sleep(1)
                b_status = device.get_channel_status(2)
                status_text = device.get_channel_status_text(b_status)
                print(f"   [{i+1}s] B通道状态: {status_text}")
                
                if b_status == 3:
                    print("   ✅ B通道正在刺激")
                elif b_status == 0:
                    print("   ✅ B通道刺激已自动停止")
                    break
        else:
            print("   ❌ B通道电流调节失败")
        
        # 步骤5：Button_dianchiji_shart - 持续刺激
        print("\n🚀 步骤5: 持续刺激 (Button_dianchiji_shart)")
        print("   按照当前spinBox中的电流值进行持续刺激")
        
        current_values = device.get_current_values()
        print(f"   当前电流值: A通道={current_values[0]}mA, B通道={current_values[1]}mA")
        
        # 启动A通道持续刺激
        if device.start_continuous_stimulation(1):
            print("   ✅ A通道持续刺激启动成功")
            
            # 监控持续刺激状态
            print("   监控持续刺激状态10秒...")
            stimulation_detected = False
            
            for i in range(10):
                time.sleep(1)
                a_status = device.get_channel_status(1)
                status_text = device.get_channel_status_text(a_status)
                print(f"   [{i+1:2d}s] A通道状态: {status_text}")
                
                if a_status == 3:  # 正常工作状态
                    stimulation_detected = True
            
            # 手动停止刺激
            print("   手动停止持续刺激...")
            if device.stop_stimulation(1):
                print("   ✅ A通道刺激停止成功")
            else:
                print("   ❌ A通道刺激停止失败")
            
            # 验证结果
            if stimulation_detected:
                print("\n🎉 QT风格测试成功！")
                print("   ✅ 设备连接正常")
                print("   ✅ 电流调节功能正常")
                print("   ✅ 自动3秒刺激功能正常")
                print("   ✅ 持续刺激功能正常")
                print("   ✅ 状态监控功能正常")
                return True
            else:
                print("\n⚠️ 测试部分成功")
                print("   ✅ 命令执行成功")
                print("   ❌ 未检测到正常工作状态")
                return False
        else:
            print("   ❌ 持续刺激启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        device.stop_all_stimulation()
        device.disconnect()
        print("✅ 资源清理完成")

def test_status_monitoring():
    """测试状态监控功能"""
    print("\n" + "=" * 60)
    print("🔍 状态监控功能测试")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        
        print("✅ 设备连接成功，开始监控状态变化...")
        
        # 设置参数
        params = StimulationParameters(channel_num=1, frequency=25.0)
        device.set_stimulation_parameters(params)
        
        # 测试状态变化监控
        print("\n监控状态变化30秒...")
        for i in range(30):
            time.sleep(1)
            
            status_info = device.get_status_info()
            a_status = status_info['channel_a_status']
            b_status = status_info['channel_b_status']
            
            print(f"[{i+1:2d}s] A通道: {a_status}, B通道: {b_status}")
            
            # 在第10秒时调节电流
            if i == 9:
                print("   >>> 调节A通道电流到5mA")
                device.adjust_current_with_feedback(1, 5.0)
            
            # 在第20秒时启动持续刺激
            if i == 19:
                print("   >>> 启动A通道持续刺激")
                device.start_continuous_stimulation(1)
        
        return True
        
    except Exception as e:
        print(f"❌ 状态监控测试失败: {e}")
        return False
        
    finally:
        device.stop_all_stimulation()
        device.disconnect()

def main():
    """主函数"""
    setup_logging()
    
    print("🔧 NK电刺激设备QT风格测试")
    print("基于原QT程序的完整控制逻辑")
    
    # 运行主要测试
    success1 = test_qt_stimulation_workflow()
    
    # 运行状态监控测试
    success2 = test_status_monitoring()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ 所有测试成功！")
        print("🎯 QT风格电刺激控制逻辑验证通过")
        print("📋 主要功能:")
        print("   - Button_dianchiji_open: 设备连接 ✅")
        print("   - spinBox_dianchiji_A/B: 电流调节+自动3秒刺激 ✅")
        print("   - Button_dianchiji_shart: 持续刺激 ✅")
        print("   - NK::callf: 状态监控 ✅")
    else:
        print("❌ 部分测试失败")
        print("🔍 需要进一步调试的问题:")
        if not success1:
            print("   - 主要刺激功能需要优化")
        if not success2:
            print("   - 状态监控功能需要优化")
    
    print("📄 详细日志: qt_stimulation_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
