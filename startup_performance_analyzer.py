#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动性能分析工具
分析系统启动过程中的性能瓶颈
"""

import time
import sys
import os
import psutil
import threading
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

class StartupProfiler:
    """启动性能分析器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.checkpoints = []
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
    def checkpoint(self, name, description=""):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        memory_usage = self.process.memory_info().rss / 1024 / 1024  # MB
        cpu_percent = self.process.cpu_percent()
        
        checkpoint = {
            'name': name,
            'description': description,
            'elapsed_time': elapsed,
            'memory_usage': memory_usage,
            'memory_delta': memory_usage - self.initial_memory,
            'cpu_percent': cpu_percent,
            'timestamp': datetime.now().isoformat()
        }
        
        self.checkpoints.append(checkpoint)
        print(f"⏱️ {name}: {elapsed:.2f}s (+{memory_usage-self.initial_memory:.1f}MB)")
        
    def get_report(self):
        """生成性能报告"""
        total_time = self.checkpoints[-1]['elapsed_time'] if self.checkpoints else 0
        max_memory = max([cp['memory_usage'] for cp in self.checkpoints]) if self.checkpoints else 0
        
        report = {
            'total_startup_time': total_time,
            'max_memory_usage': max_memory,
            'memory_increase': max_memory - self.initial_memory,
            'checkpoints': self.checkpoints,
            'analysis': self._analyze_bottlenecks()
        }
        
        return report
    
    def _analyze_bottlenecks(self):
        """分析性能瓶颈"""
        if len(self.checkpoints) < 2:
            return []
        
        bottlenecks = []
        
        for i in range(1, len(self.checkpoints)):
            prev = self.checkpoints[i-1]
            curr = self.checkpoints[i]
            
            time_diff = curr['elapsed_time'] - prev['elapsed_time']
            memory_diff = curr['memory_usage'] - prev['memory_usage']
            
            # 识别耗时较长的步骤（>1秒）
            if time_diff > 1.0:
                bottlenecks.append({
                    'type': 'time',
                    'step': curr['name'],
                    'duration': time_diff,
                    'severity': 'high' if time_diff > 3.0 else 'medium'
                })
            
            # 识别内存消耗较大的步骤（>50MB）
            if memory_diff > 50:
                bottlenecks.append({
                    'type': 'memory',
                    'step': curr['name'],
                    'memory_increase': memory_diff,
                    'severity': 'high' if memory_diff > 100 else 'medium'
                })
        
        return bottlenecks

# 全局性能分析器
profiler = StartupProfiler()

def analyze_startup():
    """分析启动性能"""
    print("🚀 启动性能分析工具")
    print("=" * 60)
    
    try:
        profiler.checkpoint("开始分析", "启动性能分析开始")
        
        # 1. 导入分析
        print("\n📦 分析模块导入性能...")
        import_start = time.time()
        
        profiler.checkpoint("导入开始", "开始导入核心模块")
        
        # 分析各个模块的导入时间
        modules_to_test = [
            ('PySide6.QtWidgets', 'Qt界面框架'),
            ('PySide6.QtCore', 'Qt核心'),
            ('numpy', '数值计算'),
            ('matplotlib', '图形绘制'),
            ('serial', '串口通信'),
            ('sqlite3', '数据库'),
            ('core.database_manager', '数据库管理器'),
            ('core.main_window', '主窗口'),
            ('utils.app_config', '应用配置')
        ]
        
        import_times = {}
        for module_name, description in modules_to_test:
            module_start = time.time()
            try:
                __import__(module_name)
                import_time = time.time() - module_start
                import_times[module_name] = import_time
                print(f"   📄 {description}: {import_time:.3f}s")
                if import_time > 0.5:
                    print(f"      ⚠️ 导入较慢: {module_name}")
            except ImportError as e:
                print(f"   ❌ {description}: 导入失败 - {e}")
                import_times[module_name] = -1
        
        profiler.checkpoint("导入完成", "核心模块导入完成")
        
        # 2. 数据库分析
        print("\n🗄️ 分析数据库性能...")
        db_start = time.time()
        
        try:
            from core.database_manager import DatabaseManager
            profiler.checkpoint("数据库管理器导入", "DatabaseManager导入完成")
            
            db = DatabaseManager()
            profiler.checkpoint("数据库管理器创建", "DatabaseManager实例创建")
            
            init_start = time.time()
            db.initialize()
            init_time = time.time() - init_start
            print(f"   🗄️ 数据库初始化: {init_time:.3f}s")
            
            profiler.checkpoint("数据库初始化", f"数据库初始化完成，耗时{init_time:.3f}s")
            
            # 测试数据库查询性能
            query_start = time.time()
            patients = db.execute_query("SELECT COUNT(*) FROM bingren")
            query_time = time.time() - query_start
            print(f"   📊 数据库查询: {query_time:.3f}s")
            
            if patients:
                patient_count = patients[0]['COUNT(*)'] if isinstance(patients[0], dict) else patients[0][0]
                print(f"   👥 患者数量: {patient_count}")
                
                if patient_count > 1000:
                    print(f"      ⚠️ 患者数据较多，可能影响启动速度")
            
        except Exception as e:
            print(f"   ❌ 数据库分析失败: {e}")
        
        profiler.checkpoint("数据库分析完成", "数据库性能分析完成")
        
        # 3. 界面创建分析
        print("\n🖥️ 分析界面创建性能...")
        
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import Qt
            
            app_start = time.time()
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            app_time = time.time() - app_start
            print(f"   🎨 QApplication创建: {app_time:.3f}s")
            
            profiler.checkpoint("QApplication创建", f"Qt应用创建，耗时{app_time:.3f}s")
            
            # 测试主窗口创建（不显示）
            window_start = time.time()
            from core.main_window import MainWindow
            window = MainWindow()
            window_time = time.time() - window_start
            print(f"   🏠 主窗口创建: {window_time:.3f}s")
            
            profiler.checkpoint("主窗口创建", f"主窗口创建，耗时{window_time:.3f}s")
            
            # 清理
            window.close()
            
        except Exception as e:
            print(f"   ❌ 界面分析失败: {e}")
        
        profiler.checkpoint("界面分析完成", "界面创建性能分析完成")
        
        # 4. 文件系统分析
        print("\n📁 分析文件系统性能...")
        
        # 检查数据目录大小
        data_path = Path('data')
        if data_path.exists():
            total_size = sum(f.stat().st_size for f in data_path.rglob('*') if f.is_file())
            total_size_mb = total_size / 1024 / 1024
            print(f"   📁 data目录大小: {total_size_mb:.1f} MB")
            
            if total_size_mb > 1000:  # 1GB
                print(f"      ⚠️ data目录较大，可能影响启动速度")
        
        # 检查日志文件
        logs_path = Path('logs')
        if logs_path.exists():
            log_files = list(logs_path.glob('*.log'))
            total_log_size = sum(f.stat().st_size for f in log_files)
            total_log_size_mb = total_log_size / 1024 / 1024
            print(f"   📝 日志文件大小: {total_log_size_mb:.1f} MB ({len(log_files)} 个文件)")
            
            if total_log_size_mb > 100:
                print(f"      ⚠️ 日志文件较大，建议清理")
        
        profiler.checkpoint("文件系统分析完成", "文件系统性能分析完成")
        
        # 5. 生成报告
        print("\n📊 生成性能报告...")
        report = profiler.get_report()
        
        print(f"\n📋 性能分析结果:")
        print("=" * 60)
        print(f"🕐 总启动时间: {report['total_startup_time']:.2f}s")
        print(f"💾 最大内存使用: {report['max_memory_usage']:.1f} MB")
        print(f"📈 内存增长: {report['memory_increase']:.1f} MB")
        
        # 显示瓶颈
        if report['analysis']:
            print(f"\n⚠️ 发现的性能瓶颈:")
            for bottleneck in report['analysis']:
                if bottleneck['type'] == 'time':
                    print(f"   🐌 {bottleneck['step']}: 耗时 {bottleneck['duration']:.2f}s")
                elif bottleneck['type'] == 'memory':
                    print(f"   🧠 {bottleneck['step']}: 内存增长 {bottleneck['memory_increase']:.1f}MB")
        else:
            print(f"\n✅ 未发现明显的性能瓶颈")
        
        # 保存详细报告
        report_file = f"startup_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        # 生成优化建议
        generate_optimization_suggestions(report, import_times)
        
    except Exception as e:
        print(f"❌ 性能分析失败: {e}")
        import traceback
        traceback.print_exc()

def generate_optimization_suggestions(report, import_times):
    """生成优化建议"""
    print(f"\n💡 优化建议:")
    print("=" * 60)
    
    suggestions = []
    
    # 基于总启动时间的建议
    if report['total_startup_time'] > 10:
        suggestions.append("🚀 启动时间较长，建议进行优化")
    
    # 基于内存使用的建议
    if report['memory_increase'] > 200:
        suggestions.append("💾 内存使用较高，建议优化内存管理")
    
    # 基于导入时间的建议
    slow_imports = [(name, time_val) for name, time_val in import_times.items() if time_val > 1.0]
    if slow_imports:
        suggestions.append("📦 以下模块导入较慢，建议延迟导入:")
        for name, time_val in slow_imports:
            suggestions.append(f"   - {name}: {time_val:.2f}s")
    
    # 基于瓶颈的建议
    for bottleneck in report['analysis']:
        if bottleneck['type'] == 'time' and bottleneck['severity'] == 'high':
            suggestions.append(f"⏱️ 优化 {bottleneck['step']} 步骤的执行时间")
        elif bottleneck['type'] == 'memory' and bottleneck['severity'] == 'high':
            suggestions.append(f"🧠 优化 {bottleneck['step']} 步骤的内存使用")
    
    # 通用优化建议
    suggestions.extend([
        "",
        "🔧 通用优化建议:",
        "1. 使用延迟导入 - 只在需要时导入大型模块",
        "2. 优化数据库查询 - 添加索引，减少启动时的查询",
        "3. 减少启动时的文件I/O操作",
        "4. 使用多线程加载非关键组件",
        "5. 清理不必要的日志和临时文件",
        "6. 考虑使用启动缓存机制"
    ])
    
    for suggestion in suggestions:
        print(suggestion)

if __name__ == "__main__":
    analyze_startup()
