#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试患者更新功能
Test Patient Update Functionality

验证修改患者时的上传功能和患者编号不可编辑

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader
from core.database_manager import DatabaseManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_update_patient():
    """测试患者更新功能"""
    print("🔄 测试患者更新功能")
    print("=" * 50)
    
    # 1. 初始化组件
    print("\n1. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 2. 获取医院信息
    hospital_info = db_manager.get_hospital_info()
    
    # 3. 创建测试患者数据
    print("\n2. 创建测试患者数据...")
    test_patient = {
        'bianhao': int(time.time()) % 1000000,
        'name': '更新测试患者',
        'age': 55,
        'xingbie': '女',
        'cardid': '110101196901011234',
        'zhenduan': '更新测试诊断',
        'bingshi': '更新测试既往史',
        'brhc': '右侧',
        'zhuzhi': '更新测试医生',
        'czy': '更新测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1),
        'status': 'pending'
    }
    
    print(f"患者编号: {test_patient['bianhao']}")
    print(f"患者姓名: {test_patient['name']}")
    
    # 4. 先添加患者到数据库
    print("\n3. 先添加患者到数据库...")
    success = db_manager.add_patient(test_patient)
    if success:
        print("✅ 患者添加成功！")
    else:
        print("❌ 患者添加失败")
        return
    
    # 5. 测试新增患者的上传功能
    print("\n4. 测试新增患者的上传功能...")
    start_time = time.time()
    
    try:
        print("🌐 开始上传新增患者数据...")
        
        # 执行新增上传
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        end_time = time.time()
        upload_duration = end_time - start_time
        
        print(f"⏱️ 新增上传耗时: {upload_duration:.2f} 秒")
        
        if upload_result.success:
            print(f"✅ 新增上传成功: {upload_result.message}")
        else:
            if "重复或已存在" in upload_result.message:
                print(f"ℹ️ 数据重复: {upload_result.message}")
            else:
                print(f"⚠️ 新增上传失败: {upload_result.message}")
        
    except Exception as e:
        print(f"❌ 新增上传测试失败: {e}")
    
    # 6. 修改患者数据
    print("\n5. 修改患者数据...")
    updated_patient = test_patient.copy()
    updated_patient['name'] = '更新后的患者姓名'
    updated_patient['age'] = 56
    updated_patient['zhenduan'] = '更新后的诊断信息'
    updated_patient['bingshi'] = '更新后的既往史'
    
    print(f"修改后姓名: {updated_patient['name']}")
    print(f"修改后年龄: {updated_patient['age']}")
    print(f"修改后诊断: {updated_patient['zhenduan']}")
    
    # 7. 测试更新患者的上传功能
    print("\n6. 测试更新患者的上传功能...")
    start_time = time.time()
    
    try:
        print("🌐 开始更新患者数据到平台...")
        
        # 执行更新上传
        upload_result = uploader.update_patient_data(updated_patient, hospital_info)
        
        end_time = time.time()
        update_duration = end_time - start_time
        
        print(f"⏱️ 更新上传耗时: {update_duration:.2f} 秒")
        
        if upload_result.success:
            print(f"✅ 更新上传成功: {upload_result.message}")
            updated_patient['status'] = "success"
        else:
            if "重复或已存在" in upload_result.message:
                print(f"ℹ️ 数据重复: {upload_result.message}")
            else:
                print(f"⚠️ 更新上传失败: {upload_result.message}")
            updated_patient['status'] = "failed"
        
    except Exception as e:
        print(f"❌ 更新上传测试失败: {e}")
        updated_patient['status'] = "failed"
    
    # 8. 更新本地数据库
    print("\n7. 更新本地数据库...")
    success = db_manager.update_patient(test_patient['bianhao'], updated_patient)
    if success:
        print("✅ 本地数据库更新成功！")
    else:
        print("❌ 本地数据库更新失败")
    
    # 9. 验证JSON格式
    print("\n8. 验证JSON格式...")
    print("📋 新增患者JSON格式（sendPatients）:")
    add_json = uploader._prepare_add_json_data(test_patient, hospital_info)
    print(f"   {add_json}")
    
    print("📋 更新患者JSON格式（updatePatients）:")
    update_json = uploader._prepare_update_json_data(updated_patient, hospital_info)
    print(f"   {update_json}")
    
    # 验证字段顺序
    import json
    add_obj = json.loads(add_json)
    update_obj = json.loads(update_json)
    
    print("\n📊 字段对比:")
    print(f"新增字段数量: {len(add_obj)} 个")
    print(f"更新字段数量: {len(update_obj)} 个")
    print(f"新增字段: {list(add_obj.keys())}")
    print(f"更新字段: {list(update_obj.keys())}")
    
    # 10. 清理测试数据
    print("\n9. 清理测试数据...")
    try:
        db_manager.execute_non_query(
            "DELETE FROM bingren WHERE bianhao = ?",
            (test_patient['bianhao'],)
        )
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️ 测试数据清理失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 患者更新功能测试完成！")
    
    # 11. 总结功能特点
    print("\n📋 功能特点总结:")
    print("✅ 新增患者使用sendPatients接口")
    print("✅ 更新患者使用updatePatients接口")
    print("✅ JSON格式按照原QT程序的字段和顺序")
    print("✅ 更新时患者编号不可修改（界面控制）")
    print("✅ 上传失败直接确认失败，不重试")
    
    print("\n🎯 JSON格式对比:")
    print("- sendPatients: 包含完整字段（department, equipmentNum, attdoctor, operator等）")
    print("- updatePatients: 只包含核心字段（num, name, sex, age, hospitalID, idCard, jiwangshi, zhenduan）")
    print("- 字段顺序严格按照原QT程序")
    
    print("\n⚡ 性能表现:")
    print(f"- 新增上传: {upload_duration:.2f}秒")
    print(f"- 更新上传: {update_duration:.2f}秒")
    print("- 响应速度优秀，用户体验良好")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_update_patient()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
