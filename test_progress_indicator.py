#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度指示器修复
Test Progress Indicator Fix

验证进度指示器是否能正确显示和隐藏

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.http_client import PatientDataUploader
from core.database_manager import DatabaseManager
from utils.app_config import AppConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,  # 使用DEBUG级别查看进度指示器日志
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_progress_indicator():
    """测试进度指示器修复"""
    print("🔧 测试进度指示器修复")
    print("=" * 50)
    
    # 1. 检查配置
    print("\n1. 检查配置...")
    retry_count = AppConfig.NETWORK_CONFIG['http']['retry_count']
    print(f"✅ 重试次数: {retry_count} 次")
    
    # 2. 初始化组件
    print("\n2. 初始化组件...")
    db_manager = DatabaseManager()
    if not db_manager.initialize():
        print("❌ 数据库初始化失败")
        return
    
    uploader = PatientDataUploader()
    
    # 3. 获取医院信息
    hospital_info = db_manager.get_hospital_info()
    
    # 4. 创建测试患者数据
    print("\n3. 创建测试患者数据...")
    test_patient = {
        'bianhao': int(time.time()) % 1000000,
        'name': '进度指示器测试患者',
        'age': 45,
        'xingbie': '女',
        'cardid': '110101197901011234',
        'zhenduan': '进度指示器测试诊断',
        'bingshi': '进度指示器测试既往史',
        'brhc': '右侧',
        'zhuzhi': '进度指示器测试医生',
        'czy': '进度指示器测试操作员',
        'keshi': hospital_info.get('keshi', '康复科'),
        'shebeiid': hospital_info.get('shebeiid', 'NK001'),
        'yiyuanid': hospital_info.get('id', 1),
        'status': 'pending'
    }
    
    print(f"患者编号: {test_patient['bianhao']}")
    print(f"患者姓名: {test_patient['name']}")
    
    # 5. 模拟完整的保存流程
    print("\n4. 模拟完整的保存流程...")
    
    # 模拟显示进度指示器
    print("📝 [模拟] 显示进度指示器: '正在保存中...'")
    print("📝 [模拟] 禁用保存按钮")
    print("📝 [模拟] 强制刷新界面")
    
    start_time = time.time()
    upload_status = "pending"
    
    try:
        print("🌐 开始同步上传到平台...")
        
        # 执行同步上传
        upload_result = uploader.upload_patient_data(test_patient, hospital_info)
        
        if upload_result.success:
            upload_status = "success"
            print(f"✅ 上传成功: {upload_result.message}")
        else:
            upload_status = "failed"
            if "重复或已存在" in upload_result.message:
                print(f"ℹ️ 数据重复: {upload_result.message}")
            else:
                print(f"⚠️ 上传失败: {upload_result.message}")
                
    except Exception as e:
        upload_status = "failed"
        print(f"❌ 上传异常: {e}")
    
    end_time = time.time()
    upload_duration = end_time - start_time
    
    # 6. 模拟进度指示器隐藏逻辑
    print("\n5. 模拟进度指示器隐藏逻辑...")
    print(f"⏱️ 实际上传耗时: {upload_duration:.2f} 秒")
    
    min_display_time = 1.5  # 最小显示时间1.5秒
    
    if upload_duration < min_display_time:
        remaining_time = min_display_time - upload_duration
        print(f"📝 [模拟] 上传太快，需要延迟隐藏 {remaining_time:.2f} 秒")
        print(f"📝 [模拟] 用户将看到进度指示器总共 {min_display_time:.1f} 秒")
        
        # 模拟延迟
        print("⏳ 模拟延迟等待...")
        time.sleep(remaining_time)
        print("📝 [模拟] 现在隐藏进度指示器")
    else:
        print(f"📝 [模拟] 上传时间足够长，立即隐藏进度指示器")
        print(f"📝 [模拟] 用户看到进度指示器 {upload_duration:.2f} 秒")
    
    print("📝 [模拟] 重新启用保存按钮")
    print("🎉 [模拟] 显示成功消息: '添加患者成功！'")
    
    # 7. 保存到本地数据库
    print("\n6. 保存到本地数据库...")
    test_patient['status'] = upload_status
    
    success = db_manager.add_patient(test_patient)
    if success:
        print("✅ 本地保存成功！")
    else:
        print("❌ 本地保存失败")
    
    # 8. 测试网络失败的情况
    print("\n7. 测试网络失败情况...")
    
    # 临时修改URL为无效地址
    original_base_url = uploader.base_url
    uploader.base_url = "http://invalid-url-for-testing.com/"
    
    test_patient_fail = test_patient.copy()
    test_patient_fail['bianhao'] = test_patient['bianhao'] + 1
    test_patient_fail['name'] = '网络失败进度测试患者'
    
    print("📝 [模拟] 显示进度指示器: '正在保存中...'")
    
    start_time = time.time()
    
    try:
        print("🌐 开始同步上传到平台（无效URL）...")
        
        # 执行同步上传
        upload_result = uploader.upload_patient_data(test_patient_fail, hospital_info)
        print(f"⚠️ 上传失败: {upload_result.message}")
        
    except Exception as e:
        print(f"❌ 上传异常: {e}")
    
    end_time = time.time()
    failure_duration = end_time - start_time
    
    print(f"⏱️ 失败耗时: {failure_duration:.2f} 秒")
    
    # 模拟失败情况的进度指示器隐藏
    if failure_duration < min_display_time:
        remaining_time = min_display_time - failure_duration
        print(f"📝 [模拟] 失败也太快，需要延迟隐藏 {remaining_time:.2f} 秒")
        print(f"📝 [模拟] 用户将看到进度指示器总共 {min_display_time:.1f} 秒")
        time.sleep(remaining_time)
    
    print("📝 [模拟] 隐藏进度指示器")
    print("📝 [模拟] 重新启用保存按钮")
    
    # 恢复原始URL
    uploader.base_url = original_base_url
    
    # 9. 清理测试数据
    print("\n8. 清理测试数据...")
    try:
        db_manager.execute_non_query(
            "DELETE FROM bingren WHERE bianhao IN (?, ?)",
            (test_patient['bianhao'], test_patient_fail['bianhao'])
        )
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️ 测试数据清理失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 进度指示器修复测试完成！")
    
    # 10. 总结修复内容
    print("\n📋 修复内容总结:")
    print("✅ 添加了QApplication.processEvents()强制刷新界面")
    print("✅ 设置了最小显示时间1.5秒")
    print("✅ 使用QTimer延迟隐藏进度指示器")
    print("✅ 防止QTimer被垃圾回收")
    print("✅ 添加了详细的调试日志")
    
    print("\n🎯 用户体验改进:")
    print("- 点击保存 → 立即显示'正在保存中...'")
    print("- 强制界面刷新 → 确保用户立即看到")
    print("- 最小显示1.5秒 → 即使上传很快也能看到")
    print("- 延迟隐藏机制 → 避免闪烁效果")
    print("- 完成后显示成功 → 清晰的操作反馈")
    
    print("\n✅ 现在用户应该能够清楚地看到:")
    print("1. 点击保存按钮后立即出现'正在保存中...'")
    print("2. 保存按钮被禁用，防止重复点击")
    print("3. 至少显示1.5秒的进度指示")
    print("4. 完成后显示'保存成功'消息")

def main():
    """主函数"""
    setup_logging()
    
    try:
        test_progress_indicator()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
