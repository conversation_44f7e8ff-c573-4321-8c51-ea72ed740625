#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验测试 - 模拟真实的用户操作场景
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.WARNING,  # 只显示警告和错误
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_user_experience():
    """用户体验测试"""
    print("👤 用户体验测试 - 模拟真实操作场景")
    print("=" * 60)
    
    try:
        from core.stimulation_device import StimulationDevice
        
        device = StimulationDevice()
        
        print("\n📍 场景1: 用户点击连接按钮（错误端口）")
        print("用户期望：点击后立即看到反馈，不会卡住界面")
        
        start_time = time.time()
        result = device.connect(26)  # 错误端口
        response_time = time.time() - start_time
        
        print(f"✅ 按钮响应时间: {response_time:.1f}秒")
        print(f"连接结果: {'成功' if result else '失败'}")
        
        if response_time <= 8:
            print("🎉 响应时间优秀！用户不会感到卡顿")
            scenario1_pass = True
        else:
            print("⚠️ 响应时间较慢，用户可能感到不耐烦")
            scenario1_pass = False
        
        # 短暂等待，模拟用户看到错误后的反应时间
        time.sleep(1)
        
        print("\n📍 场景2: 用户立即切换到正确端口")
        print("用户期望：能够立即重新连接，不需要等待")
        
        start_time = time.time()
        result = device.connect(7)  # 正确端口
        response_time = time.time() - start_time
        
        print(f"✅ 重新连接时间: {response_time:.1f}秒")
        print(f"连接结果: {'成功' if result else '失败'}")
        
        if result and response_time <= 2:
            print("🎉 重新连接快速成功！")
            scenario2_pass = True
        else:
            print("⚠️ 重新连接有问题")
            scenario2_pass = False
        
        if result:
            print("\n📍 场景3: 用户断开连接")
            start_time = time.time()
            device.disconnect()
            disconnect_time = time.time() - start_time
            
            print(f"✅ 断开连接时间: {disconnect_time:.1f}秒")
            
            if disconnect_time <= 1:
                print("🎉 断开连接快速完成！")
                scenario3_pass = True
            else:
                print("⚠️ 断开连接较慢")
                scenario3_pass = False
        else:
            scenario3_pass = False
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 用户体验评估:")
        
        scenarios = [
            ("错误端口响应", scenario1_pass),
            ("快速重新连接", scenario2_pass),
            ("快速断开连接", scenario3_pass)
        ]
        
        passed = sum(1 for _, passed in scenarios if passed)
        total = len(scenarios)
        
        for name, passed in scenarios:
            status = "✅ 优秀" if passed else "❌ 需改进"
            print(f"   {name}: {status}")
        
        success_rate = (passed / total) * 100
        print(f"\n用户体验评分: {passed}/{total} ({success_rate:.0f}%)")
        
        if success_rate >= 80:
            print("🎉 用户体验优秀！")
            print("✅ 用户可以流畅地操作设备连接")
            return True
        else:
            print("⚠️ 用户体验需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_user_experience()
    sys.exit(0 if success else 1)
