#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备测试脚本
Electrical Stimulation Device Test Script

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.stimulation_device import StimulationDevice, StimulationParameters
from utils.app_config import AppConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_dll_loading():
    """测试DLL加载"""
    print("=" * 50)
    print("测试DLL加载")
    print("=" * 50)

    try:
        device = StimulationDevice()
        if device.load_dll():
            print("✓ DLL加载成功")
            return True
        else:
            print("✗ DLL加载失败")
            return False
    except Exception as e:
        print(f"✗ DLL加载异常: {e}")
        return False


def test_device_connection():
    """测试设备连接"""
    print("\n" + "=" * 50)
    print("测试设备连接")
    print("=" * 50)

    try:
        device = StimulationDevice()

        # 首先进行连接诊断
        print("正在进行连接诊断...")
        diagnosis = device.diagnose_connection_issues()

        print(f"DLL加载状态: {'✓' if diagnosis['dll_loaded'] else '✗'}")
        print(f"DLL文件存在: {'✓' if diagnosis['dll_exists'] else '✗'}")
        print(f"可用串口: {diagnosis['available_ports']}")
        print(f"检测到设备端口: {diagnosis['device_detected_ports']}")

        print("\n诊断建议:")
        for recommendation in diagnosis['recommendations']:
            print(f"  • {recommendation}")

        # 如果检测到设备端口，优先测试这些端口
        test_ports = diagnosis['device_detected_ports'] if diagnosis['device_detected_ports'] else [1, 7, 2, 3]

        print(f"\n正在测试端口: {test_ports}")
        for port_num in test_ports:
            print(f"正在测试端口 {port_num}...")
            if device.connect(port_num=port_num):
                print(f"✓ 端口 {port_num} 连接成功")

                # 读取设备信息
                device_info = device.get_device_info()
                print(f"设备ID: {device_info.device_id}")
                print(f"固件版本: {device_info.firmware_version}")
                print(f"硬件版本: {device_info.hardware_version}")
                print(f"序列号: {device_info.serial_number}")

                # 获取统计信息
                stats = device.get_statistics()
                print(f"连接次数: {stats['connection_count']}")
                print(f"错误次数: {stats['error_count']}")

                # 断开连接
                device.disconnect()
                print("✓ 设备断开成功")
                return True
            else:
                print(f"✗ 端口 {port_num} 连接失败")

        print("✗ 所有端口连接失败")
        return False

    except Exception as e:
        print(f"✗ 设备连接异常: {e}")
        return False


def test_parameter_setting():
    """测试参数设置"""
    print("\n" + "=" * 50)
    print("测试参数设置")
    print("=" * 50)

    try:
        device = StimulationDevice()

        # 尝试连接端口7（已知可用）
        if not device.connect(port_num=7):
            print("✗ 无法连接设备，跳过参数测试")
            return False

        # 创建测试参数
        params = StimulationParameters()
        params.channel_num = 1
        params.frequency = 25.0
        params.pulse_width = 250.0
        params.relax_time = 3.0
        params.climb_time = 1.5
        params.work_time = 8.0
        params.fall_time = 1.5
        params.wave_type = 0

        print(f"设置参数: 频率={params.frequency}Hz, 脉宽={params.pulse_width}μs")

        if device.set_stimulation_parameters(params):
            print("✓ 参数设置成功")

            # 获取当前参数
            current_params = device.get_stimulation_parameters()
            print(f"当前参数: 频率={current_params.frequency}Hz, 脉宽={current_params.pulse_width}μs")

            device.disconnect()
            return True
        else:
            print("✗ 参数设置失败")
            device.disconnect()
            return False

    except Exception as e:
        print(f"✗ 参数设置异常: {e}")
        return False


def test_current_control():
    """测试电流控制"""
    print("\n" + "=" * 50)
    print("测试电流控制")
    print("=" * 50)

    try:
        device = StimulationDevice()

        # 尝试连接端口7（已知可用）
        if not device.connect(port_num=7):
            print("✗ 无法连接设备，跳过电流测试")
            return False

        # 设置电流
        test_current = 5.0  # 5.0mA
        print(f"设置通道1电流为: {test_current}mA")

        if device.set_current(1, test_current):
            print("✓ 电流设置成功")

            # 测试电流调节
            print("测试电流增加...")
            if device.adjust_current(1, 1, increase=True):
                print("✓ 电流增加成功")

            print("测试电流减少...")
            if device.adjust_current(1, 1, increase=False):
                print("✓ 电流减少成功")

            device.disconnect()
            return True
        else:
            print("✗ 电流设置失败")
            device.disconnect()
            return False

    except Exception as e:
        print(f"✗ 电流控制异常: {e}")
        return False


def test_stimulation_control():
    """测试刺激控制"""
    print("\n" + "=" * 50)
    print("测试刺激控制")
    print("=" * 50)

    try:
        device = StimulationDevice()

        # 尝试连接端口7（已知可用）
        if not device.connect(port_num=7):
            print("✗ 无法连接设备，跳过刺激测试")
            return False

        # 设置基本参数
        params = StimulationParameters()
        device.set_stimulation_parameters(params)

        print("开始验证式刺激测试...")
        test_current = 3.0  # 设置较低的测试电流
        if device.start_stimulation_verified(1, test_current):
            print("✓ 刺激开始成功")

            # 等待2秒
            print("刺激进行中... (2秒)")
            time.sleep(2)

            # 暂停刺激
            if device.pause_stimulation(1):
                print("✓ 刺激暂停成功")

            time.sleep(1)

            # 停止刺激
            if device.stop_stimulation(1):
                print("✓ 刺激停止成功")

            device.disconnect()
            return True
        else:
            print("✗ 刺激开始失败")
            device.disconnect()
            return False

    except Exception as e:
        print(f"✗ 刺激控制异常: {e}")
        return False


def test_enhanced_features():
    """测试增强功能"""
    print("\n" + "=" * 50)
    print("测试增强功能")
    print("=" * 50)

    try:
        device = StimulationDevice()

        # 测试连接诊断
        print("1. 测试连接诊断功能...")
        diagnosis = device.diagnose_connection_issues()

        print(f"   DLL状态: {'正常' if diagnosis['dll_loaded'] else '异常'}")
        print(f"   检测到设备端口: {len(diagnosis['device_detected_ports'])} 个")
        print(f"   诊断建议数量: {len(diagnosis['recommendations'])} 条")

        # 如果有可用端口，测试验证式刺激
        if diagnosis['device_detected_ports']:
            port = diagnosis['device_detected_ports'][0]
            print(f"\n2. 测试验证式刺激功能（端口{port}）...")

            if device.connect(port):
                print("   ✓ 设备连接成功")

                # 设置参数
                params = StimulationParameters()
                params.frequency = 20.0
                params.pulse_width = 200.0
                device.set_stimulation_parameters(params)

                # 测试验证式刺激启动
                if device.start_stimulation_verified(1, 2.0):
                    print("   ✓ 验证式刺激启动成功")

                    # 检查通道状态
                    channel_status = device.get_channel_status(1)
                    print(f"   通道1状态: {channel_status}")

                    # 停止刺激
                    time.sleep(1)
                    device.stop_stimulation(1)
                    print("   ✓ 刺激停止成功")
                else:
                    print("   ✗ 验证式刺激启动失败")

                device.disconnect()
                return True
            else:
                print("   ✗ 设备连接失败")
        else:
            print("\n2. 跳过验证式刺激测试（无可用设备）")

        return True

    except Exception as e:
        print(f"✗ 增强功能测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("电刺激设备功能测试")
    print("=" * 50)

    # 检查配置
    config = AppConfig.STIMULATION_CONFIG
    dll_path = config['dll_path']
    print(f"DLL路径: {dll_path}")
    print(f"DLL存在: {dll_path.exists()}")

    if not dll_path.exists():
        print("✗ DLL文件不存在，请检查libs目录")
        return

    # 运行测试
    tests = [
        ("DLL加载", test_dll_loading),
        ("设备连接", test_device_connection),
        ("参数设置", test_parameter_setting),
        ("电流控制", test_current_control),
        ("刺激控制", test_stimulation_control),
        ("增强功能", test_enhanced_features),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{len(results)} 项测试通过")

    if passed == len(results):
        print("🎉 所有测试通过！电刺激设备功能正常。")
    else:
        print("⚠️  部分测试失败，请检查设备连接和配置。")


if __name__ == "__main__":
    main()
