# 电刺激功能使用说明

## 概述

NK脑机接口系统已成功集成电刺激仪功能，支持双通道电刺激治疗。本文档详细说明如何使用电刺激功能。

## 系统要求

### 硬件要求
- 电刺激设备（支持RecoveryDLL接口）
- USB或串口连接线
- Windows操作系统

### 软件要求
- NK脑机接口系统 v1.0.0+
- RecoveryDLL.dll库文件（已包含在libs目录）
- Python 3.8+
- PySide6

## 功能特性

### 支持的功能
- ✅ 双通道独立控制（A/B通道）
- ✅ 实时参数调节
- ✅ 多种波形类型（双相波/单相波）
- ✅ 精确电流控制（0.1-50.0 mA）
- ✅ 安全保护机制
- ✅ 详细操作日志

### 刺激参数
- **频率范围**: 1-100 Hz
- **脉宽范围**: 50-500 μs
- **电流范围**: 0.1-50.0 mA
- **时间参数**: 休息、上升、工作、下降时间可调
- **波形类型**: 双相波、单相波

## 使用步骤

### 1. 设备连接

#### 1.1 硬件连接
1. 将电刺激设备通过USB线连接到计算机
2. 确保设备驱动程序已正确安装
3. 记录设备的端口号

#### 1.2 软件配置
1. 启动NK脑机接口系统
2. 进入"系统设置"界面
3. 选择"设备配置"标签页
4. 在"电刺激设备配置"部分：
   - 设置正确的端口号
   - 配置电流限制和默认参数
   - 点击"测试连接"验证设备

### 2. 治疗操作

#### 2.1 进入治疗界面
1. 在主界面选择"治疗系统"
2. 切换到"电刺激治疗"标签页

#### 2.2 连接设备
1. 点击"连接电刺激设备"按钮
2. 等待连接成功提示
3. 确认设备状态显示为"已连接"

#### 2.3 参数设置
1. **通道选择**：
   - 勾选要使用的通道（A通道/B通道）
   - 设置各通道的电流值（mA）

2. **刺激参数**：
   - 频率：设置刺激频率（Hz）
   - 脉宽：设置脉冲宽度（μs）
   - 休息时间：设置休息阶段时长（s）
   - 上升时间：设置电流上升时长（s）
   - 工作时间：设置刺激工作时长（s）
   - 下降时间：设置电流下降时长（s）
   - 波形类型：选择双相波或单相波

#### 2.4 开始治疗
1. 确认所有参数设置正确
2. 点击"开始刺激"按钮
3. 监控通道状态和刺激日志
4. 根据需要调整参数

#### 2.5 停止治疗
1. 点击"停止刺激"按钮
2. 确认所有通道状态显示为"关闭"
3. 查看治疗日志记录

### 3. 安全操作

#### 3.1 电流安全
- ⚠️ **严格遵守电流限制**：不超过设备和患者安全范围
- ⚠️ **渐进调节**：使用小步长逐步调节电流
- ⚠️ **持续监护**：治疗过程中持续观察患者反应

#### 3.2 设备安全
- 🔧 **定期检查**：检查设备连接和工作状态
- 🔧 **紧急停止**：遇到异常立即停止刺激
- 🔧 **参数验证**：确保所有参数在安全范围内

#### 3.3 操作安全
- 👨‍⚕️ **专业操作**：仅限专业医疗人员操作
- 📋 **记录完整**：详细记录治疗参数和过程
- 🚨 **应急准备**：准备应急处理措施

## 常见问题

### Q1: 设备连接失败怎么办？
**A1**: 检查以下项目：
1. 设备是否正确连接到计算机
2. 设备驱动是否正确安装
3. 端口号设置是否正确
4. 设备是否被其他程序占用
5. 尝试重新插拔设备

### Q2: 参数设置无效怎么办？
**A2**: 确认以下事项：
1. 设备是否已正确连接
2. 参数是否在有效范围内
3. 是否有权限修改参数
4. 重新连接设备后再试

### Q3: 刺激无法开始怎么办？
**A3**: 检查以下条件：
1. 设备连接状态是否正常
2. 是否选择了至少一个通道
3. 电流值是否大于0
4. 参数设置是否完整
5. 设备是否处于可用状态

### Q4: 如何调节电流强度？
**A4**: 电流调节方法：
1. 在通道设置中直接输入电流值
2. 使用电流调节按钮进行微调
3. 建议从低电流开始，逐步增加
4. 密切观察患者反应

### Q5: 治疗过程中设备断开怎么办？
**A5**: 应急处理步骤：
1. 立即停止所有刺激
2. 检查设备连接
3. 重新连接设备
4. 确认患者安全
5. 记录异常情况

## 技术支持

### 日志查看
- 治疗过程中的所有操作都会记录在刺激日志中
- 系统日志文件位于 `logs/nk_system.log`
- 错误日志文件位于 `logs/error.log`

### 配置文件
- 电刺激配置位于 `utils/app_config.py`
- 可以修改默认参数和安全限制
- 修改后需要重启系统

### 测试工具
- 使用 `test_stimulation.py` 测试设备功能
- 使用 `test_stimulation_ui.py` 测试界面功能
- 在设置界面使用"测试连接"功能

## 注意事项

### 医疗安全
- 🏥 **医疗器械**：本系统为医疗器械，需按医疗器械标准使用
- 👨‍⚕️ **专业人员**：仅限经过培训的专业医疗人员操作
- 📋 **治疗记录**：详细记录每次治疗的参数和效果
- 🚨 **应急预案**：制定完善的应急处理预案

### 设备维护
- 🔧 **定期检查**：定期检查设备连接和功能
- 🧹 **清洁保养**：按照设备说明书进行清洁保养
- 📊 **性能监控**：监控设备性能和稳定性
- 🔄 **软件更新**：及时更新系统软件

### 数据管理
- 💾 **数据备份**：定期备份治疗数据和配置
- 🔒 **数据安全**：保护患者隐私和数据安全
- 📈 **数据分析**：分析治疗效果和参数优化
- 📋 **记录完整**：保持完整的治疗记录

## 联系方式

如有技术问题或需要支持，请联系：
- 技术支持：[技术支持邮箱]
- 用户手册：查看完整的用户手册文档
- 在线帮助：系统内置帮助功能

---

**免责声明**：本系统为医疗器械软件，使用前请仔细阅读相关法规和安全要求。操作人员需具备相应的专业资质和培训。
