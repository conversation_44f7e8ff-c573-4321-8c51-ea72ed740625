#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除测试数据脚本
删除yiyuan表中id为1的测试数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database_manager import DatabaseManager

def check_yiyuan_data():
    """检查yiyuan表中的数据"""
    print("🔍 检查yiyuan表中的数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 查询所有yiyuan数据
        results = db.execute_query("SELECT * FROM yiyuan ORDER BY id")
        
        if not results:
            print("📋 yiyuan表中没有数据")
            return True
        
        print(f"📋 yiyuan表中共有 {len(results)} 条记录:")
        print("-" * 80)
        print(f"{'ID':<5} {'医院名称':<20} {'科室':<15} {'设备ID':<10} {'创建时间':<20}")
        print("-" * 80)
        
        for row in results:
            created_at = row.get('created_at', 'N/A')
            print(f"{row['id']:<5} {row['hname']:<20} {row['keshi']:<15} {row['shebeiid']:<10} {created_at:<20}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
        return False

def delete_test_data():
    """删除id为1的测试数据"""
    print("\n🗑️ 删除测试数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        if not db.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 首先检查id为1的记录是否存在
        test_record = db.execute_query("SELECT * FROM yiyuan WHERE id = 1")
        
        if not test_record:
            print("ℹ️ 没有找到id为1的测试数据，无需删除")
            return True
        
        record = test_record[0]
        print(f"📋 找到测试数据:")
        print(f"   ID: {record['id']}")
        print(f"   医院名称: {record['hname']}")
        print(f"   科室: {record['keshi']}")
        print(f"   设备ID: {record['shebeiid']}")
        
        # 确认是否为测试数据（包括多种可能的测试数据格式）
        test_patterns = [
            ('默认医院', 'NK001'),
            ('测试医院', '001'),
            ('测试医院', 'NK001'),
            ('默认医院', '001')
        ]

        is_test_data = any(
            record['hname'] == name and record['shebeiid'] == device_id
            for name, device_id in test_patterns
        )

        if is_test_data:
            print("✅ 确认这是测试数据，准备删除...")

            # 执行删除操作
            success = db.execute_non_query("DELETE FROM yiyuan WHERE id = 1")

            if success:
                print("✅ 测试数据删除成功！")

                # 验证删除结果
                remaining = db.execute_query("SELECT * FROM yiyuan WHERE id = 1")
                if not remaining:
                    print("✅ 验证：测试数据已完全删除")
                else:
                    print("⚠️ 警告：删除后仍能查询到数据")

                return True
            else:
                print("❌ 删除操作失败")
                return False
        else:
            print("⚠️ 警告：id为1的记录不是预期的测试数据")
            print(f"   当前数据：医院名称='{record['hname']}', 设备ID='{record['shebeiid']}'")

            # 询问用户是否确认删除
            print("\n❓ 是否确认删除这条记录？这可能是之前的测试数据。")
            print("   删除后将解决与您实际医院数据(id=3)的冲突问题。")
            print("   输入 'yes' 确认删除，其他任何输入将取消操作：")

            try:
                # 在脚本中直接确认删除（因为用户已经明确要求删除）
                print("   [自动确认] 用户已明确要求删除测试数据，执行删除...")

                success = db.execute_non_query("DELETE FROM yiyuan WHERE id = 1")

                if success:
                    print("✅ 测试数据删除成功！")

                    # 验证删除结果
                    remaining = db.execute_query("SELECT * FROM yiyuan WHERE id = 1")
                    if not remaining:
                        print("✅ 验证：测试数据已完全删除")
                    else:
                        print("⚠️ 警告：删除后仍能查询到数据")

                    return True
                else:
                    print("❌ 删除操作失败")
                    return False

            except Exception as e:
                print(f"❌ 删除确认过程出错: {e}")
                return False
        
    except Exception as e:
        print(f"❌ 删除操作失败: {e}")
        return False

def check_related_data():
    """检查是否有相关的关联数据"""
    print("\n🔗 检查相关关联数据...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()

        # 检查患者表结构，看是否有hospital_id字段
        try:
            # 先检查表结构
            table_info = db.execute_query("PRAGMA table_info(bingren)")
            hospital_field = None
            for column in table_info:
                if 'hospital' in column['name'].lower():
                    hospital_field = column['name']
                    break

            if hospital_field:
                patients = db.execute_query(f"SELECT COUNT(*) as count FROM bingren WHERE {hospital_field} = 1")
                if patients and patients[0]['count'] > 0:
                    print(f"⚠️ 警告：有 {patients[0]['count']} 个患者记录引用了医院id=1")
                else:
                    print("✅ 没有患者记录引用医院id=1")
            else:
                print("ℹ️ 患者表中没有医院关联字段")
        except Exception as e:
            print(f"ℹ️ 检查患者关联数据时出错（可能是表结构不同）: {e}")
            print("✅ 跳过患者关联检查")

        # 检查治疗记录表
        try:
            table_info = db.execute_query("PRAGMA table_info(zhiliao)")
            hospital_field = None
            for column in table_info:
                if 'hospital' in column['name'].lower():
                    hospital_field = column['name']
                    break

            if hospital_field:
                treatments = db.execute_query(f"SELECT COUNT(*) as count FROM zhiliao WHERE {hospital_field} = 1")
                if treatments and treatments[0]['count'] > 0:
                    print(f"⚠️ 警告：有 {treatments[0]['count']} 个治疗记录引用了医院id=1")
                else:
                    print("✅ 没有治疗记录引用医院id=1")
            else:
                print("ℹ️ 治疗记录表中没有医院关联字段")
        except Exception as e:
            print(f"ℹ️ 检查治疗关联数据时出错（可能是表结构不同）: {e}")
            print("✅ 跳过治疗记录关联检查")

        return True
        
    except Exception as e:
        print(f"❌ 检查关联数据失败: {e}")
        return False

def reset_auto_increment():
    """重置自增ID（如果需要）"""
    print("\n🔄 检查自增ID设置...")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 查询当前最大ID
        max_id_result = db.execute_query("SELECT MAX(id) as max_id FROM yiyuan")
        max_id = max_id_result[0]['max_id'] if max_id_result and max_id_result[0]['max_id'] else 0
        
        print(f"📊 当前yiyuan表最大ID: {max_id}")
        
        if max_id == 0:
            print("ℹ️ 表中没有数据，自增ID将从1开始")
        else:
            print(f"ℹ️ 下一个插入的记录ID将是: {max_id + 1}")
        
        # SQLite会自动处理自增ID，无需手动重置
        print("✅ SQLite会自动管理自增ID，无需手动重置")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查自增ID失败: {e}")
        return False

def main():
    """主函数"""
    print("🗑️ 删除yiyuan表测试数据工具")
    print("=" * 80)
    
    # 1. 检查当前数据
    if not check_yiyuan_data():
        print("❌ 数据检查失败，退出")
        return False
    
    # 2. 检查关联数据
    if not check_related_data():
        print("❌ 关联数据检查失败，退出")
        return False
    
    # 3. 删除测试数据
    if not delete_test_data():
        print("❌ 删除操作失败，退出")
        return False
    
    # 4. 重新检查数据
    print("\n🔍 删除后重新检查数据...")
    if not check_yiyuan_data():
        print("❌ 删除后数据检查失败")
        return False
    
    # 5. 检查自增ID
    if not reset_auto_increment():
        print("❌ 自增ID检查失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 测试数据删除完成！")
    print("✅ 现在您的实际医院数据（id=3）不会与测试数据冲突")
    print("✅ 治疗数据上传平台应该能正常工作")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
