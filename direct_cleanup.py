#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接清理脚本 - 使用SQL直接删除孤立记录
"""

import sys
import os
import sqlite3

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.app_config import AppConfig

def direct_cleanup():
    """直接清理数据库"""
    print("🧹 直接清理数据库...")
    print("=" * 50)
    
    try:
        # 获取数据库路径
        db_config = AppConfig.get_config('database')
        db_path = db_config['path']
        
        print(f"📋 数据库路径: {db_path}")
        
        # 直接连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 禁用外键约束
        cursor.execute("PRAGMA foreign_keys = OFF")
        print("✅ 已禁用外键约束")
        
        # 1. 删除eeg_sessions中的孤立记录
        print("\n📋 清理eeg_sessions表...")
        
        # 查看违规记录
        cursor.execute("PRAGMA foreign_key_check(eeg_sessions)")
        violations = cursor.fetchall()
        
        if violations:
            print(f"   发现 {len(violations)} 个违规记录")
            
            # 直接删除这些记录
            for violation in violations:
                rowid = violation[1]
                cursor.execute("DELETE FROM eeg_sessions WHERE rowid = ?", (rowid,))
                print(f"   ✅ 删除eeg_sessions记录 rowid: {rowid}")
        else:
            print("   ✅ 没有发现违规记录")
        
        # 2. 再次检查所有外键约束
        print("\n📋 检查所有外键约束...")
        cursor.execute("PRAGMA foreign_key_check")
        all_violations = cursor.fetchall()
        
        if all_violations:
            print(f"   仍有 {len(all_violations)} 个违规:")
            for violation in all_violations:
                print(f"   {list(violation)}")
                
                # 尝试删除这些违规记录
                table = violation[0]
                rowid = violation[1]
                try:
                    cursor.execute(f"DELETE FROM {table} WHERE rowid = ?", (rowid,))
                    print(f"   ✅ 删除 {table} 记录 rowid: {rowid}")
                except Exception as e:
                    print(f"   ❌ 删除 {table} 记录失败: {e}")
        else:
            print("   ✅ 没有外键约束违规")
        
        # 重新启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        print("\n✅ 已重新启用外键约束")
        
        # 最终检查
        cursor.execute("PRAGMA foreign_key_check")
        final_violations = cursor.fetchall()
        
        if final_violations:
            print(f"❌ 最终检查仍有 {len(final_violations)} 个违规")
            for violation in final_violations:
                print(f"   {list(violation)}")
        else:
            print("✅ 最终外键约束检查通过")
        
        # 提交事务
        conn.commit()
        print("✅ 事务已提交")
        
        # 关闭连接
        conn.close()
        print("✅ 数据库连接已关闭")
        
        return len(final_violations) == 0
        
    except Exception as e:
        print(f"❌ 直接清理失败: {e}")
        return False

def verify_final_state():
    """验证最终状态"""
    print("\n🔍 验证最终状态...")
    print("=" * 50)
    
    try:
        # 重新连接验证
        db_config = AppConfig.get_config('database')
        db_path = db_config['path']
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查外键约束
        cursor.execute("PRAGMA foreign_key_check")
        violations = cursor.fetchall()
        
        if violations:
            print(f"❌ 仍有 {len(violations)} 个外键约束违规")
            return False
        else:
            print("✅ 外键约束检查通过")
        
        # 检查数据完整性
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()
        print(f"📋 数据完整性检查: {integrity_result[0]}")
        
        # 统计重要表的记录数
        print("\n📊 数据库统计:")
        print("-" * 30)
        
        tables = ['yiyuan', 'bingren', 'eeg_sessions', 'eeg_raw_data']
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table:<15} {count:>10}")
            except Exception as e:
                print(f"{table:<15} {'错误':>10}")
        
        # 检查医院数据
        print("\n📋 医院数据:")
        cursor.execute("SELECT id, hname FROM yiyuan")
        hospitals = cursor.fetchall()
        for hospital in hospitals:
            print(f"   ID: {hospital[0]}, 名称: {hospital[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证最终状态失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 数据库直接清理工具")
    print("=" * 80)
    
    # 1. 直接清理
    if not direct_cleanup():
        print("❌ 直接清理失败，退出")
        return False
    
    # 2. 验证最终状态
    if not verify_final_state():
        print("❌ 最终验证失败")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 数据库清理完成！")
    print("✅ 所有测试数据已清理")
    print("✅ 外键约束检查通过")
    print("✅ 数据库完整性恢复")
    print("✅ 治疗数据上传平台冲突问题彻底解决")
    print("\n📋 清理总结:")
    print("   - 删除了测试医院记录 (id=1)")
    print("   - 删除了测试患者记录 (编号8888, 9999)")
    print("   - 清理了相关的脑电数据记录")
    print("   - 清理了孤立的会话记录")
    print("   - 所有患者现在都关联到您的实际医院 (id=3)")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
