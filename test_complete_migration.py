#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整迁移测试脚本
Complete migration test script
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_complete_migration():
    """测试完整的迁移结果"""
    print("=" * 70)
    print("电刺激治疗页面完整迁移测试")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：基本导入
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 基本模块导入...")
    try:
        from ui.treatment_ui import TreatmentWidget
        from core.stimulation_device import StimulationDevice
        print("✅ 所有核心模块导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return 1
    
    # 测试2：电刺激功能完整性
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 电刺激功能完整性...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查电刺激相关功能
        stimulation_features = [
            'stimulation_connect_button',
            'stimulation_status_label',
            'stimulation_port_combo',
            'channel_a_current',
            'channel_b_current',
            'start_stimulation_button',
            'stop_stimulation_button',
            'channel_a_status_label',
            'channel_b_status_label',
            'add_stimulation_log',
            'toggle_stimulation_connection',
            'on_channel_a_current_changed',
            'on_channel_b_current_changed'
        ]
        
        missing_features = []
        for feature in stimulation_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ 缺少电刺激功能: {missing_features}")
        else:
            print("✅ 所有电刺激功能都已迁移")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查电刺激功能失败: {e}")
    
    # 测试3：深度学习参数完整性
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 深度学习参数完整性...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查深度学习参数
        dl_parameters = [
            'temperature_spinbox',
            'activation_threshold_spin',
            'class_weight_spinbox',
            'smoothing_slider',
            'smoothing_label',
            'adaptive_learning_checkbox',
            'transfer_learning_checkbox',
            'finetune_layers_spinbox',
            'neural_calibrate_btn',
            'EEGNet深度学习参数'
        ]
        
        missing_params = []
        for param in dl_parameters:
            if param not in content:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 缺少深度学习参数: {missing_params}")
        else:
            print("✅ 所有深度学习参数都已添加")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查深度学习参数失败: {e}")
    
    # 测试4：界面布局优化
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 界面布局优化...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局优化
        layout_features = [
            'QScrollArea',
            'scroll_area.setWidgetResizable(True)',
            'setFixedWidth(320)',
            'setVerticalScrollBarPolicy',
            'scroll_area.setWidget(panel)'
        ]
        
        missing_layouts = []
        for layout in layout_features:
            if layout not in content:
                missing_layouts.append(layout)
        
        if missing_layouts:
            print(f"❌ 缺少布局优化: {missing_layouts}")
        else:
            print("✅ 界面布局已优化（滚动区域）")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查界面布局失败: {e}")
    
    # 测试5：日志系统整合
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 日志系统整合...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查日志系统
        log_features = [
            'log_tab_widget = QTabWidget()',
            'addTab(self.training_log, "训练日志")',
            'addTab(self.stimulation_log, "刺激日志")',
            'def add_stimulation_log(self, message: str):'
        ]
        
        missing_logs = []
        for log in log_features:
            if log not in content:
                missing_logs.append(log)
        
        if missing_logs:
            print(f"❌ 缺少日志系统功能: {missing_logs}")
        else:
            print("✅ 日志系统已整合（标签页分离）")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查日志系统失败: {e}")
    
    # 测试6：清理工作验证
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 清理工作验证...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已清理
        should_not_exist = [
            'def create_stimulation_tab(',
            'tab_widget.addTab(stimulation_tab, "电刺激治疗")'
        ]
        
        found_old_code = []
        for old_code in should_not_exist:
            if old_code in content:
                found_old_code.append(old_code)
        
        if found_old_code:
            print(f"❌ 仍存在旧代码: {found_old_code}")
        else:
            print("✅ 旧代码已完全清理")
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ 检查清理工作失败: {e}")
    
    # 测试7：控件数量统计
    total_tests += 1
    print(f"\n🔍 测试 {total_tests}: 控件数量统计...")
    try:
        treatment_ui_path = project_root / "ui" / "treatment_ui.py"
        with open(treatment_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计各类控件数量
        control_groups = content.count('QGroupBox(')
        buttons = content.count('QPushButton(')
        spinboxes = content.count('QSpinBox(') + content.count('QDoubleSpinBox(')
        checkboxes = content.count('QCheckBox(')
        sliders = content.count('QSlider(')
        
        print(f"   📊 控件统计:")
        print(f"      - 控件组: {control_groups} 个")
        print(f"      - 按钮: {buttons} 个")
        print(f"      - 数值框: {spinboxes} 个")
        print(f"      - 复选框: {checkboxes} 个")
        print(f"      - 滑块: {sliders} 个")
        
        # 基本的数量检查
        if control_groups >= 10 and buttons >= 15 and spinboxes >= 8:
            print("✅ 控件数量符合预期")
            tests_passed += 1
        else:
            print("❌ 控件数量不足")
            
    except Exception as e:
        print(f"❌ 控件统计失败: {e}")
    
    # 输出结果
    print("\n" + "=" * 70)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 完整迁移测试全部通过！")
        print("\n✅ 迁移成功总结:")
        print("   🔧 电刺激功能: 完全迁移到脑电训练页面")
        print("   🧠 深度学习参数: 完整添加EEGNet参数控件")
        print("   📱 界面布局: 优化为滚动区域，支持更多控件")
        print("   📝 日志系统: 整合为标签页分离的系统日志")
        print("   🧹 代码清理: 完全删除旧的电刺激标签页")
        print("   ⚙️ 信号连接: 所有功能的信号连接完整")
        print("   🎛️ 控件完整: 包含所有原有功能和新增参数")
        print("\n🚀 系统现在具备:")
        print("   - 完整的电刺激设备控制功能")
        print("   - 完整的EEGNet深度学习参数调节")
        print("   - 优化的用户界面布局")
        print("   - 整合的治疗工作流程")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查迁移结果")
        return 1

if __name__ == "__main__":
    sys.exit(test_complete_migration())
