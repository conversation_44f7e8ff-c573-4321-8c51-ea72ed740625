#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始脑电数据分析工具
Raw EEG Data Analysis Tool

分析指定患者目录下的原始脑电数据

作者: AI Assistant
版本: 1.0.0
"""

import sys
import h5py
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import json

def analyze_hdf5_file(file_path: Path):
    """分析HDF5文件内容"""
    print(f"\n📁 分析文件: {file_path.name}")
    print("=" * 60)
    
    try:
        with h5py.File(file_path, 'r') as f:
            # 1. 文件基本信息
            file_size = file_path.stat().st_size
            print(f"文件大小: {file_size / 1024:.2f} KB ({file_size} bytes)")
            print(f"创建时间: {datetime.fromtimestamp(file_path.stat().st_ctime)}")
            print(f"修改时间: {datetime.fromtimestamp(file_path.stat().st_mtime)}")
            
            # 2. HDF5结构分析
            print(f"\n📊 HDF5文件结构:")
            print(f"根组数量: {len(f.keys())}")
            print(f"根组列表: {list(f.keys())}")
            
            # 3. 会话元数据分析
            if 'session_metadata' in f:
                print(f"\n🔍 会话元数据:")
                session_meta = f['session_metadata']
                for key in session_meta.attrs.keys():
                    value = session_meta.attrs[key]
                    if isinstance(value, bytes):
                        value = value.decode('utf-8')
                    print(f"  {key}: {value}")
            
            # 4. 试验数据分析
            if 'trials' in f:
                trials_group = f['trials']
                trial_count = len(trials_group.keys())
                print(f"\n🧠 试验数据分析:")
                print(f"试验总数: {trial_count}")
                
                if trial_count > 0:
                    trial_names = sorted(trials_group.keys())
                    print(f"试验列表: {trial_names}")
                    
                    # 分析每个试验
                    motor_imagery_count = 0
                    rest_count = 0
                    total_duration = 0
                    quality_scores = []
                    data_shapes = []
                    
                    for trial_name in trial_names:
                        trial_group = trials_group[trial_name]
                        
                        # 获取试验元数据
                        trial_id = trial_group.attrs.get('trial_id', 'N/A')
                        label = trial_group.attrs.get('label', 'N/A')
                        duration = trial_group.attrs.get('duration_seconds', 0)
                        quality = trial_group.attrs.get('data_quality', 0)
                        start_time = trial_group.attrs.get('start_time', 'N/A')
                        
                        # 获取数据形状
                        if 'eeg_data' in trial_group:
                            data_shape = trial_group['eeg_data'].shape
                            data_shapes.append(data_shape)
                        else:
                            data_shape = "无数据"
                        
                        # 统计标签
                        if label == 1:
                            motor_imagery_count += 1
                        elif label == 0:
                            rest_count += 1
                        
                        total_duration += duration
                        quality_scores.append(quality)
                        
                        print(f"\n  试验 {trial_name}:")
                        print(f"    ID: {trial_id}")
                        print(f"    标签: {'运动想象' if label == 1 else '休息' if label == 0 else '未知'} ({label})")
                        print(f"    时长: {duration:.2f} 秒")
                        print(f"    质量: {quality:.3f}")
                        print(f"    数据形状: {data_shape}")
                        print(f"    开始时间: {start_time}")
                    
                    # 5. 数据统计摘要
                    print(f"\n📈 数据统计摘要:")
                    print(f"运动想象试验: {motor_imagery_count} 个")
                    print(f"休息状态试验: {rest_count} 个")
                    print(f"标签平衡性: {motor_imagery_count}/{rest_count} = {motor_imagery_count/(rest_count+1e-6):.2f}")
                    print(f"总时长: {total_duration:.2f} 秒")
                    print(f"平均时长: {total_duration/trial_count:.2f} 秒/试验")
                    print(f"平均质量: {np.mean(quality_scores):.3f}")
                    print(f"质量范围: {np.min(quality_scores):.3f} - {np.max(quality_scores):.3f}")
                    
                    if data_shapes:
                        unique_shapes = list(set(data_shapes))
                        print(f"数据形状: {unique_shapes}")
                        if len(unique_shapes) == 1:
                            shape = unique_shapes[0]
                            print(f"  通道数: {shape[0]}")
                            print(f"  样本数: {shape[1]}")
                            print(f"  采样率估算: {shape[1]/np.mean([d for d in [total_duration/trial_count] if d > 0]):.1f} Hz")
                    
                    # 6. 数据质量评估
                    print(f"\n✅ 数据质量评估:")
                    
                    # 检查数据完整性
                    if trial_count >= 2:
                        print("✅ 数据完整性: 良好 (包含多个试验)")
                    else:
                        print("⚠️ 数据完整性: 试验数量较少")
                    
                    # 检查标签平衡性
                    if abs(motor_imagery_count - rest_count) <= 1:
                        print("✅ 标签平衡性: 良好")
                    else:
                        print("⚠️ 标签平衡性: 不平衡")
                    
                    # 检查数据质量
                    avg_quality = np.mean(quality_scores)
                    if avg_quality >= 0.8:
                        print("✅ 数据质量: 优秀")
                    elif avg_quality >= 0.6:
                        print("⚠️ 数据质量: 良好")
                    else:
                        print("❌ 数据质量: 需要改善")
                    
                    # 检查数据一致性
                    if len(unique_shapes) == 1:
                        print("✅ 数据一致性: 良好 (所有试验形状一致)")
                    else:
                        print("⚠️ 数据一致性: 试验间数据形状不一致")
                    
                    return {
                        'file_path': str(file_path),
                        'file_size_kb': file_size / 1024,
                        'trial_count': trial_count,
                        'motor_imagery_count': motor_imagery_count,
                        'rest_count': rest_count,
                        'total_duration': total_duration,
                        'avg_quality': avg_quality,
                        'data_shapes': unique_shapes,
                        'quality_scores': quality_scores
                    }
            else:
                print("❌ 未找到试验数据组")
                return None
                
    except Exception as e:
        print(f"❌ 分析文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_patient_directory(patient_dir: str):
    """分析患者目录下的所有数据"""
    patient_path = Path(patient_dir)
    
    if not patient_path.exists():
        print(f"❌ 目录不存在: {patient_dir}")
        return
    
    print(f"🔍 分析患者目录: {patient_path.name}")
    print(f"📁 完整路径: {patient_path}")
    print("=" * 80)
    
    # 查找所有HDF5文件
    h5_files = list(patient_path.rglob("*.h5"))
    
    if not h5_files:
        print("❌ 未找到任何HDF5文件")
        return
    
    print(f"📊 找到 {len(h5_files)} 个HDF5文件:")
    for i, file_path in enumerate(h5_files, 1):
        print(f"  {i}. {file_path.relative_to(patient_path)}")
    
    # 分析每个文件
    analysis_results = []
    for file_path in h5_files:
        result = analyze_hdf5_file(file_path)
        if result:
            analysis_results.append(result)
    
    # 生成总体报告
    if analysis_results:
        print(f"\n📋 总体分析报告:")
        print("=" * 60)
        
        total_trials = sum(r['trial_count'] for r in analysis_results)
        total_motor_imagery = sum(r['motor_imagery_count'] for r in analysis_results)
        total_rest = sum(r['rest_count'] for r in analysis_results)
        total_duration = sum(r['total_duration'] for r in analysis_results)
        avg_quality = np.mean([r['avg_quality'] for r in analysis_results])
        total_size = sum(r['file_size_kb'] for r in analysis_results)
        
        print(f"会话数量: {len(analysis_results)}")
        print(f"总试验数: {total_trials}")
        print(f"运动想象试验: {total_motor_imagery}")
        print(f"休息状态试验: {total_rest}")
        print(f"总时长: {total_duration:.2f} 秒 ({total_duration/60:.1f} 分钟)")
        print(f"平均质量: {avg_quality:.3f}")
        print(f"总数据大小: {total_size:.2f} KB ({total_size/1024:.2f} MB)")
        
        # 最终评估
        print(f"\n🎯 最终评估:")
        if total_trials >= 10:
            print("✅ 数据量: 充足")
        elif total_trials >= 5:
            print("⚠️ 数据量: 基本够用")
        else:
            print("❌ 数据量: 不足")
        
        if abs(total_motor_imagery - total_rest) <= 2:
            print("✅ 整体标签平衡性: 良好")
        else:
            print("⚠️ 整体标签平衡性: 不平衡")
        
        if avg_quality >= 0.8:
            print("✅ 整体数据质量: 优秀")
        elif avg_quality >= 0.6:
            print("⚠️ 整体数据质量: 良好")
        else:
            print("❌ 整体数据质量: 需要改善")

def main():
    """主函数"""
    # 直接分析指定的患者目录
    patient_dir = r"D:\NK_QT\QT6\NK\NK\Python_NK_System\data\raw_eeg_data\patients\patient_2343465467"
    analyze_patient_directory(patient_dir)

if __name__ == "__main__":
    main()
