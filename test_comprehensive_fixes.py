#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合修复验证测试
Comprehensive Fixes Verification Test

验证所有问题的修复：
1. 连续语音提示问题
2. 电刺激启动标准问题
3. 数据库保存问题（患者编号、操作员、上传状态）

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_voice_prompt_fix():
    """测试语音提示修复"""
    print("=== 测试语音提示修复 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController, TreatmentSession
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟语音引擎
        class MockVoiceEngine:
            def __init__(self):
                self.spoken_texts = []
                
            def speak(self, text):
                self.spoken_texts.append(text)
                print(f"  语音: {text}")
        
        # 设置模拟语音引擎
        mock_voice = MockVoiceEngine()
        controller.voice_engine = mock_voice
        
        # 模拟治疗会话
        controller.current_session = TreatmentSession(
            patient_id="211",
            patient_name="33331",
            start_time=datetime.now(),
            end_time=None,
            total_imagery_count=5,
            successful_triggers=3,
            treatment_duration_minutes=0,
            treatment_score=0,
            treatment_evaluation="",
            record_number=12345
        )
        
        # 测试非自适应学习模式的刺激结束处理
        print("测试非自适应学习模式:")
        controller.adaptive_learning_enabled = False
        controller._handle_stimulation_end()
        
        # 验证只有一次语音提示
        if len(mock_voice.spoken_texts) == 1 and "你做的很棒，请继续想象运动" in mock_voice.spoken_texts[0]:
            print("✓ 非自适应模式语音提示正确")
            result1 = True
        else:
            print("✗ 非自适应模式语音提示错误")
            result1 = False
        
        # 重置语音记录
        mock_voice.spoken_texts.clear()
        
        # 测试自适应学习模式的刺激结束处理
        print("\n测试自适应学习模式:")
        controller.adaptive_learning_enabled = True
        controller._handle_stimulation_end()
        
        # 验证有休息提示
        if len(mock_voice.spoken_texts) == 1 and "休息" in mock_voice.spoken_texts[0]:
            print("✓ 自适应模式语音提示正确")
            result2 = True
        else:
            print("✗ 自适应模式语音提示错误")
            result2 = False
        
        return result1 and result2
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_stimulation_start_standard():
    """测试电刺激启动标准"""
    print("\n=== 测试电刺激启动标准 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟治疗界面（A通道勾选2mA，B通道未勾选0mA）
        class MockTreatmentUI:
            def __init__(self):
                class MockCheckBox:
                    def __init__(self, checked):
                        self._checked = checked
                    def isChecked(self):
                        return self._checked
                
                class MockSpinBox:
                    def __init__(self, value):
                        self._value = value
                    def value(self):
                        return self._value
                
                self.channel_a_checkbox = MockCheckBox(True)   # A通道勾选
                self.channel_b_checkbox = MockCheckBox(False)  # B通道未勾选
                self.channel_a_current = MockSpinBox(2)        # A通道2mA
                self.channel_b_current = MockSpinBox(0)        # B通道0mA
        
        # 模拟电刺激设备
        class MockStimulationDevice:
            def __init__(self):
                self.connected = True
                self.operations = []
                
            def is_connected(self):
                return self.connected
                
            def set_current(self, channel_num, current_value):
                self.operations.append(f"设置通道{channel_num}电流: {current_value}mA")
                return True
                
            def _safe_dll_call(self, func_name, *args):
                if func_name == 'SwitchChannelState' and args == (1, 3):
                    self.operations.append("启动A通道刺激")
                    return 0
                return 0
        
        # 设置模拟组件
        mock_ui = MockTreatmentUI()
        mock_device = MockStimulationDevice()
        
        controller.treatment_ui = mock_ui
        controller.stimulation_device = mock_device
        
        print("界面状态:")
        print(f"  A通道: {'勾选' if mock_ui.channel_a_checkbox.isChecked() else '未勾选'}, 电流: {mock_ui.channel_a_current.value()}mA")
        print(f"  B通道: {'勾选' if mock_ui.channel_b_checkbox.isChecked() else '未勾选'}, 电流: {mock_ui.channel_b_current.value()}mA")
        
        # 测试电刺激启动
        success = controller._start_stimulation()
        
        print(f"启动结果: {'成功' if success else '失败'}")
        print("设备操作:")
        for op in mock_device.operations:
            print(f"  - {op}")
        
        # 验证结果：只启动A通道
        if (success and 
            len(mock_device.operations) == 2 and
            "设置通道1电流: 2mA" in mock_device.operations[0] and
            "启动A通道刺激" in mock_device.operations[1]):
            print("✓ 电刺激启动标准正确：只启动勾选且有电流的通道")
            return True
        else:
            print("✗ 电刺激启动标准错误")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_database_save_fix():
    """测试数据库保存修复"""
    print("\n=== 测试数据库保存修复 ===")
    
    try:
        from core.treatment_workflow import TreatmentWorkflowController, TreatmentSession
        from datetime import datetime
        
        # 创建控制器
        controller = TreatmentWorkflowController()
        
        # 模拟认证管理器
        class MockAuthManager:
            def get_current_user(self):
                return {'name': 'admin'}
        
        # 模拟治疗界面
        class MockTreatmentUI:
            def __init__(self):
                self.current_patient_info = {
                    'bianhao': 211,
                    'name': '33331',
                    'zhuzhi': '张医生'
                }
        
        # 模拟数据库管理器
        class MockDatabaseManager:
            def __init__(self):
                self.saved_records = []
                
            def add_treatment_record(self, treatment_record):
                self.saved_records.append(treatment_record)
                print(f"保存治疗记录: {treatment_record}")
                return True
        
        # 设置模拟组件
        mock_auth = MockAuthManager()
        mock_ui = MockTreatmentUI()
        mock_db = MockDatabaseManager()
        
        controller.auth_manager = mock_auth
        controller.treatment_ui = mock_ui
        controller.database_manager = mock_db
        
        # 创建治疗会话
        session = TreatmentSession(
            patient_id="211",  # 正确的患者编号
            patient_name="33331",
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_imagery_count=10,
            successful_triggers=8,
            treatment_duration_minutes=5,
            treatment_score=80,
            treatment_evaluation="良",
            record_number=12345
        )
        
        # 测试保存方法
        controller._save_treatment_to_database(session, "1")
        
        if len(mock_db.saved_records) > 0:
            record = mock_db.saved_records[0]
            
            print("验证保存的数据:")
            print(f"  患者编号(bianh): {record.get('bianh')} (期望: 211)")
            print(f"  操作员(czy): '{record.get('czy')}' (期望: 'admin')")
            print(f"  主治医师(zhuzhi): '{record.get('zhuzhi')}' (期望: '张医生')")
            print(f"  上传状态(upload_status): '{record.get('upload_status')}' (期望: '1')")
            
            # 验证关键字段
            if (record.get('bianh') == 211 and 
                record.get('czy') == 'admin' and 
                record.get('zhuzhi') == '张医生' and
                record.get('upload_status') == '1'):
                print("✓ 数据库保存修复成功")
                return True
            else:
                print("✗ 数据库保存修复失败")
                return False
        else:
            print("✗ 没有保存任何记录")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_motor_imagery_detection_threshold():
    """测试运动想象检测阈值"""
    print("\n=== 测试运动想象检测阈值 ===")
    
    try:
        from ui.treatment_ui import TreatmentUI
        from core.ml_model import MotorImageryModel
        
        # 创建模拟模型
        model = MotorImageryModel("test")
        model.is_trained = True
        model_info = model.get_model_info()
        model_info.decision_threshold = 0.508  # 设置阈值
        
        # 创建治疗界面实例
        treatment_ui = TreatmentUI()
        treatment_ui.current_model = model
        treatment_ui.classification_buffer = [[1.0] * 8 for _ in range(250)]  # 模拟足够的数据
        treatment_ui.classification_buffer_size = 250
        
        # 模拟分类结果
        def mock_predict(data):
            return 1, 0.510  # 预测=1, 置信度=0.510
        
        model.predict = mock_predict
        
        # 测试检测回调
        result = treatment_ui._check_motor_imagery_for_treatment()
        
        print(f"模型阈值: {model_info.decision_threshold}")
        print(f"模拟置信度: 0.510")
        print(f"检测结果: {result} (期望: True, 因为0.510 > 0.508)")
        
        if result:
            print("✓ 运动想象检测阈值修复成功")
            return True
        else:
            print("✗ 运动想象检测阈值修复失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始综合修复验证测试...")
    print("=" * 70)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("语音提示修复", test_voice_prompt_fix),
        ("电刺激启动标准", test_stimulation_start_standard),
        ("数据库保存修复", test_database_save_fix),
        ("运动想象检测阈值", test_motor_imagery_detection_threshold),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 70)
    print(f"综合测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有问题修复成功！")
        print("\n🔧 修复总结:")
        print("1. ✅ 连续语音提示问题已修复")
        print("   - 移除了重复的计数增加")
        print("   - 自适应和非自适应模式语音提示正确")
        print()
        print("2. ✅ 电刺激启动标准已明确")
        print("   - 检查通道勾选状态")
        print("   - 检查电流设置")
        print("   - 只启动勾选且有电流的通道")
        print()
        print("3. ✅ 数据库保存问题已修复")
        print("   - 患者编号正确对应bingren表的bianhao")
        print("   - 操作员字段填入当前登录用户")
        print("   - 添加了上传状态标志字段")
        print()
        print("4. ✅ 运动想象检测阈值已修复")
        print("   - 使用模型的实际阈值而不是硬编码0.6")
        print("   - 置信度检测逻辑正确")
        return True
    else:
        print("❌ 部分问题修复失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
