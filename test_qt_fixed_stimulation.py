#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的QT风格电刺激设备测试
验证按照QT程序精确逻辑修复后的功能
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core.stimulation_device_qt import StimulationDevice, StimulationParameters

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('qt_fixed_test.log', encoding='utf-8')
        ]
    )

def test_qt_fixed_workflow():
    """测试修复后的QT风格工作流程"""
    print("=" * 60)
    print("🔧 修复后的QT风格电刺激设备测试")
    print("验证按照QT程序精确逻辑修复后的功能")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        # 步骤1：Button_dianchiji_open - 连接设备
        print("\n📡 步骤1: 连接设备 (Button_dianchiji_open)")
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功，已切换到循环刺激状态")
        
        # 步骤2：设置刺激参数
        print("\n⚙️ 步骤2: 设置刺激参数")
        params = StimulationParameters(
            channel_num=1,
            frequency=25.0,
            pulse_width=250.0,
            relax_time=5.0,
            climb_time=2.0,
            work_time=10.0,
            fall_time=2.0,
            wave_type=0
        )
        
        if not device.set_stimulation_parameters(params):
            print("❌ 刺激参数设置失败")
            return False
        print("✅ 刺激参数设置成功")
        
        # 步骤3：测试spinBox调节电流的立即3秒预刺激
        print("\n🎛️ 步骤3: 测试spinBox调节电流 (立即启动3秒预刺激)")
        print("   验证修复：调节电流后立即启动刺激，不再有几秒延迟")
        
        test_current = 5.0
        print(f"\n   调节A通道电流为{test_current}mA")
        start_time = time.time()
        
        if device.adjust_current_with_feedback(1, test_current):
            print(f"   ✅ 电流调节成功，应该立即启动3秒预刺激")
            
            # 监控状态变化，验证立即启动
            stimulation_started = False
            for i in range(5):
                time.sleep(1)
                elapsed = time.time() - start_time
                a_status = device.get_channel_status(1)
                status_text = device.get_channel_status_text(a_status)
                print(f"   [{elapsed:.1f}s] A通道状态: {status_text}")
                
                if a_status == 3:  # 正常工作状态
                    if not stimulation_started:
                        stimulation_started = True
                        print(f"   ✅ 刺激在{elapsed:.1f}秒时启动 (应该接近0秒)")
                elif a_status == 0 and stimulation_started:  # 停止状态
                    print(f"   ✅ 3秒预刺激在{elapsed:.1f}秒时自动停止")
                    break
            
            if stimulation_started:
                print("   ✅ 修复成功：spinBox调节后立即启动刺激")
            else:
                print("   ❌ 修复失败：仍未检测到立即启动")
        else:
            print("   ❌ 电流调节失败")
        
        # 步骤4：测试Button_dianchiji_shart的立即启动
        print("\n🚀 步骤4: 测试Button_dianchiji_shart (立即启动持续刺激)")
        print("   验证修复：只执行SwitchChannelState，立即启动")
        
        start_time = time.time()
        if device.start_continuous_stimulation(1):
            print("   ✅ 持续刺激启动命令执行成功")
            
            # 监控立即启动
            for i in range(3):
                time.sleep(1)
                elapsed = time.time() - start_time
                a_status = device.get_channel_status(1)
                status_text = device.get_channel_status_text(a_status)
                print(f"   [{elapsed:.1f}s] A通道状态: {status_text}")
                
                if a_status == 3:
                    print(f"   ✅ 持续刺激在{elapsed:.1f}秒时启动 (应该接近0秒)")
                    break
            else:
                print("   ⚠️ 3秒内未检测到持续刺激启动")
        else:
            print("   ❌ 持续刺激启动失败")
        
        # 步骤5：测试Button_dianchiji_stop的停止功能
        print("\n🛑 步骤5: 测试Button_dianchiji_stop (停止所有刺激)")
        print("   验证修复：按照QT程序逻辑停止所有通道")
        
        if device.stop_all_stimulation_qt_style():
            print("   ✅ 停止所有刺激成功")
            
            # 验证停止状态
            time.sleep(1)
            a_status = device.get_channel_status(1)
            b_status = device.get_channel_status(2)
            print(f"   A通道状态: {device.get_channel_status_text(a_status)}")
            print(f"   B通道状态: {device.get_channel_status_text(b_status)}")
            
            if a_status == 0 and b_status == 0:
                print("   ✅ 所有通道已正确停止")
            else:
                print("   ⚠️ 部分通道可能未完全停止")
        else:
            print("   ❌ 停止所有刺激失败")
        
        # 步骤6：测试用户退出时的清理
        print("\n🚪 步骤6: 测试用户退出时的清理")
        print("   验证修复：退出时关闭A、B通道输出并切换电刺激仪工作状态")
        
        # 先启动一些刺激
        device.adjust_current_with_feedback(1, 3.0)
        device.adjust_current_with_feedback(2, 4.0)
        time.sleep(1)
        
        print("   执行用户退出清理...")
        # disconnect方法现在包含了用户退出的完整逻辑
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源 - 这里会执行用户退出的完整逻辑
        print("\n🧹 执行用户退出清理...")
        device.disconnect()
        print("✅ 用户退出清理完成")

def test_timing_comparison():
    """测试启动时机对比"""
    print("\n" + "=" * 60)
    print("⏱️ 启动时机对比测试")
    print("对比修复前后的启动延迟")
    print("=" * 60)
    
    device = StimulationDevice()
    
    try:
        if not device.connect(port_num=7):
            print("❌ 设备连接失败")
            return False
        
        # 设置参数
        params = StimulationParameters(channel_num=1, frequency=25.0)
        device.set_stimulation_parameters(params)
        
        print("\n测试1: spinBox调节电流的启动时机")
        for i in range(3):
            print(f"\n第{i+1}次测试:")
            start_time = time.time()
            
            device.adjust_current_with_feedback(1, 3.0 + i)
            
            # 检测启动时机
            for j in range(10):  # 检测1秒内的启动
                time.sleep(0.1)
                elapsed = time.time() - start_time
                status = device.get_channel_status(1)
                
                if status == 3:  # 检测到启动
                    print(f"   启动延迟: {elapsed:.2f}秒")
                    break
            else:
                print("   1秒内未检测到启动")
            
            time.sleep(4)  # 等待3秒预刺激结束
        
        print("\n测试2: Button_dianchiji_shart的启动时机")
        for i in range(2):
            print(f"\n第{i+1}次测试:")
            start_time = time.time()
            
            device.start_continuous_stimulation(1)
            
            # 检测启动时机
            for j in range(10):  # 检测1秒内的启动
                time.sleep(0.1)
                elapsed = time.time() - start_time
                status = device.get_channel_status(1)
                
                if status == 3:  # 检测到启动
                    print(f"   启动延迟: {elapsed:.2f}秒")
                    break
            else:
                print("   1秒内未检测到启动")
            
            device.stop_stimulation(1)
            time.sleep(1)
        
        return True
        
    except Exception as e:
        print(f"❌ 时机测试失败: {e}")
        return False
        
    finally:
        device.disconnect()

def main():
    """主函数"""
    setup_logging()
    
    print("🔧 修复后的QT风格电刺激设备测试")
    print("验证所有修复项目的效果")
    
    # 运行主要测试
    success1 = test_qt_fixed_workflow()
    
    # 运行时机对比测试
    success2 = test_timing_comparison()
    
    print("\n" + "=" * 60)
    print("📊 修复验证结果")
    print("=" * 60)
    
    if success1 and success2:
        print("✅ 所有修复验证成功！")
        print("\n🎯 修复项目验证:")
        print("   1. ✅ 用户退出时关闭A、B通道输出并切换电刺激仪工作状态")
        print("   2. ✅ spinBox调节电流后立即启动3秒预刺激")
        print("   3. ✅ Button_dianchiji_shart只执行SwitchChannelState，立即启动")
        print("   4. ✅ Button_dianchiji_stop按照QT程序逻辑停止刺激")
        print("\n🚀 性能改进:")
        print("   - 消除了启动延迟问题")
        print("   - 简化了命令执行流程")
        print("   - 完全按照QT程序逻辑实现")
    else:
        print("❌ 部分修复验证失败")
        if not success1:
            print("   - 主要功能修复需要进一步调试")
        if not success2:
            print("   - 启动时机仍需优化")
    
    print("\n📄 详细日志: qt_fixed_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
