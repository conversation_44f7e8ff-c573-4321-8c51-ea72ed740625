#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志清理效果
验证频繁的状态变化日志是否已被清理
"""

import sys
import time
import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from core.stimulation_device import StimulationDevice

def test_log_cleanup():
    """测试日志清理效果"""
    print("🧪 测试日志清理效果")
    print("=" * 50)
    
    # 设置日志级别为INFO，确保不显示DEBUG信息
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    app = QApplication(sys.argv)
    
    try:
        # 创建设备实例
        device = StimulationDevice()
        
        print("📱 步骤1: 连接设备...")
        if device.connect(7):
            print("✅ 设备连接成功")
            
            print("\n📊 步骤2: 模拟状态变化（10秒）...")
            print("   注意观察：应该不再有频繁的状态变化日志输出")
            
            # 模拟一些操作来触发状态变化
            def simulate_operations():
                try:
                    # 设置电流
                    device.set_current(1, 5.0)
                    QTimer.singleShot(1000, lambda: device.set_current(1, 10.0))
                    QTimer.singleShot(2000, lambda: device.set_current(1, 15.0))
                    QTimer.singleShot(3000, lambda: device.trigger_stimulation(1))
                    QTimer.singleShot(6000, lambda: device.stop_stimulation(1))
                    QTimer.singleShot(8000, lambda: app.quit())
                except Exception as e:
                    print(f"❌ 操作失败: {e}")
                    app.quit()
            
            QTimer.singleShot(500, simulate_operations)
            
            # 运行事件循环
            app.exec()
            
            print("\n📊 步骤3: 断开设备...")
            device.disconnect()
            print("✅ 设备断开成功")
            
        else:
            print("❌ 设备连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎯 测试完成")
    print("如果看到大量'A通道状态变化'日志，说明清理失败")
    print("如果只看到重要的操作日志，说明清理成功")

if __name__ == "__main__":
    test_log_cleanup()
