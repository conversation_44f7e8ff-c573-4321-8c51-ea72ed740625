#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运动想象训练功能演示脚本
Motor Imagery Training Demo Script

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def simulate_eeg_data(state='rest', duration=4.0, sample_rate=125.0):
    """模拟脑电数据"""
    channels = 8
    samples = int(duration * sample_rate)

    # 基础噪声
    data = np.random.randn(channels, samples) * 20

    # 添加特定状态的特征
    t = np.linspace(0, duration, samples)

    if state == 'motor_imagery':
        # 运动想象：C3, CZ, C4通道的mu节律抑制
        for ch in [3, 4, 5]:  # C3, CZ, C4
            mu_suppression = -15 * np.sin(2 * np.pi * 10 * t) * np.exp(-t/2)
            data[ch] += mu_suppression

        # 添加beta节律增强
        for ch in [3, 4, 5]:
            beta_enhancement = 10 * np.sin(2 * np.pi * 20 * t) * (1 + 0.5 * np.sin(2 * np.pi * 0.5 * t))
            data[ch] += beta_enhancement
    else:
        # 平静状态：alpha节律
        for ch in range(channels):
            alpha_wave = 25 * np.sin(2 * np.pi * 10 * t) * (1 + 0.3 * np.sin(2 * np.pi * 0.2 * t))
            data[ch] += alpha_wave

    return data

def demo_signal_processing():
    """演示信号处理"""
    print("🧠 信号处理演示")
    print("=" * 50)

    from core.signal_processor import EEGSignalProcessor

    processor = EEGSignalProcessor()

    # 模拟不同状态的数据
    states = ['rest', 'motor_imagery']

    for state in states:
        print(f"\n📊 处理{state}状态数据...")

        # 生成模拟数据
        raw_data = simulate_eeg_data(state)
        print(f"   原始数据: {raw_data.shape}")

        # 预处理
        processed_data, quality = processor.preprocess_signal(raw_data)
        print(f"   预处理后: {processed_data.shape}")
        print(f"   信号质量: {quality.overall_quality:.3f}")
        print(f"   是否可用: {'✅' if quality.is_usable else '❌'}")

        # 特征提取
        features = processor.extract_features(processed_data)
        print(f"   提取特征: {len(features)} 类")

        for name, data in features.items():
            print(f"     - {name}: {data.shape}")

def demo_training_process():
    """演示训练过程"""
    print("\n🎯 训练过程演示")
    print("=" * 50)

    from core.motor_imagery_trainer import MotorImageryTrainer, TrainingConfig
    from core.signal_processor import EEGSignalProcessor

    # 创建训练器
    trainer = MotorImageryTrainer()
    processor = EEGSignalProcessor()

    # 设置配置
    config = TrainingConfig()
    config.trials_per_round = 6  # 演示用小数量
    config.motor_imagery_duration = 2.0  # 短时间
    config.quiet_duration = 2.0
    config.rest_duration = 1.0

    trainer.set_config(config)

    print(f"📋 训练配置:")
    print(f"   每轮试验数: {config.trials_per_round}")
    print(f"   运动想象时长: {config.motor_imagery_duration}秒")
    print(f"   平静状态时长: {config.quiet_duration}秒")

    # 模拟训练数据收集
    print(f"\n🔄 模拟训练数据收集...")

    training_data = []
    training_labels = []

    # 生成训练数据
    for i in range(config.trials_per_round):
        state = 'motor_imagery' if i % 2 == 0 else 'rest'
        label = 1 if state == 'motor_imagery' else 0

        print(f"   试验 {i+1}: {state} (标签: {label})")

        # 生成模拟数据
        raw_data = simulate_eeg_data(state, config.motor_imagery_duration)

        # 预处理
        processed_data, quality = processor.preprocess_signal(raw_data)

        # 对于演示，降低质量要求
        if quality.overall_quality > 0.05:  # 降低阈值用于演示
            training_data.append(processed_data)
            training_labels.append(label)
            print(f"     ✅ 数据质量: {quality.overall_quality:.3f}")
        else:
            print(f"     ❌ 数据质量差: {quality.overall_quality:.3f}")

    print(f"\n📈 收集到 {len(training_data)} 个有效样本")
    return training_data, training_labels

def demo_model_training(training_data, training_labels):
    """演示模型训练"""
    print("\n🤖 模型训练演示")
    print("=" * 50)

    from core.ml_model import MotorImageryModel, ModelManager

    # 创建模型
    model_name = f"Demo_Model_{int(time.time())}"
    model = MotorImageryModel(model_name)

    print(f"📝 创建模型: {model_name}")

    # 添加训练数据
    if model.add_training_data(training_data, training_labels):
        print(f"✅ 添加训练数据: {len(training_data)} 个样本")
    else:
        print("❌ 添加训练数据失败")
        return None

    # 训练模型
    print("🔄 开始模型训练...")

    algorithms = ['lda', 'svm']
    best_model = None
    best_accuracy = 0

    for algorithm in algorithms:
        print(f"\n   测试算法: {algorithm.upper()}")

        test_model = MotorImageryModel(f"{model_name}_{algorithm}")
        test_model.add_training_data(training_data, training_labels)

        if test_model.train_model(algorithm):
            performance = test_model.get_model_info().performance
            accuracy = performance.accuracy
            cv_mean = np.mean(performance.cross_val_scores)
            cv_std = np.std(performance.cross_val_scores)

            print(f"     准确率: {accuracy:.3f}")
            print(f"     交叉验证: {cv_mean:.3f}±{cv_std:.3f}")

            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_model = test_model
        else:
            print(f"     ❌ {algorithm}训练失败")

    if best_model:
        print(f"\n🏆 最佳模型准确率: {best_accuracy:.3f}")
        return best_model
    else:
        print("\n❌ 所有模型训练失败")
        return None

def demo_prediction(model, processor):
    """演示预测功能"""
    print("\n🔮 预测功能演示")
    print("=" * 50)

    if not model:
        print("❌ 没有可用的模型")
        return

    # 测试不同状态的预测
    test_states = ['rest', 'motor_imagery']

    for state in test_states:
        print(f"\n🧪 测试{state}状态:")

        # 生成测试数据
        test_data = simulate_eeg_data(state, 4.0)

        # 预处理
        processed_data, quality = processor.preprocess_signal(test_data)

        print(f"   数据质量: {quality.overall_quality:.3f}")

        # 预测
        prediction, confidence = model.predict(processed_data)

        expected = 0 if state == 'rest' else 1
        correct = "✅" if prediction == expected else "❌"

        print(f"   预测结果: {prediction} (期望: {expected}) {correct}")
        print(f"   置信度: {confidence:.3f}")

def demo_complete_workflow():
    """演示完整工作流程"""
    print("🚀 运动想象训练完整演示")
    print("=" * 60)

    # 1. 信号处理演示
    demo_signal_processing()

    # 2. 训练过程演示
    training_data, training_labels = demo_training_process()

    if len(training_data) == 0:
        print("❌ 没有有效的训练数据，演示结束")
        return

    # 3. 模型训练演示
    model = demo_model_training(training_data, training_labels)

    # 4. 预测演示
    from core.signal_processor import EEGSignalProcessor
    processor = EEGSignalProcessor()
    demo_prediction(model, processor)

    # 5. 总结
    print("\n🎉 演示完成！")
    print("=" * 60)
    print("✅ 功能验证:")
    print("  1. 脑电信号预处理和质量评估")
    print("  2. 多状态训练数据收集")
    print("  3. 机器学习模型训练和比较")
    print("  4. 实时预测和置信度评估")

    print("\n📋 实际使用流程:")
    print("  1. 连接脑电设备")
    print("  2. 设置训练参数")
    print("  3. 开始运动想象训练")
    print("  4. 根据语音提示执行动作")
    print("  5. 训练完成后保存模型")
    print("  6. 进行多轮训练优化")

    print("\n🔧 系统特点:")
    print("  • 智能信号预处理，自动去噪和质量评估")
    print("  • 随机试验序列，避免预期效应")
    print("  • 多算法模型训练，自动选择最佳算法")
    print("  • 增量学习支持，多轮训练累积优化")
    print("  • 实时语音引导，提升训练体验")
    print("  • 完整的性能评估和建议系统")

def main():
    """主函数"""
    try:
        demo_complete_workflow()
        return True
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
