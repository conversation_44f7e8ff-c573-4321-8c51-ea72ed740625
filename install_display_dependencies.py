#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装实时显示功能所需的依赖库
"""

import subprocess
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
        
    try:
        # 尝试导入
        __import__(import_name)
        logger.info(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        logger.info(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            logger.info(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {package_name} 安装失败: {e}")
            return False

def main():
    """主函数"""
    logger.info("🚀 开始安装实时显示功能依赖库...")
    
    # 需要安装的包列表
    packages = [
        ("pyqtgraph", "pyqtgraph"),
        ("matplotlib", "matplotlib"),
        ("mne", "mne"),
        ("scipy", "scipy"),
        ("numpy", "numpy"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    logger.info(f"📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        logger.info("🎉 所有依赖库安装完成！")
        logger.info("💡 现在可以运行 python test_realtime_display.py 测试实时显示功能")
    else:
        logger.warning("⚠️ 部分依赖库安装失败，可能影响实时显示功能")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
