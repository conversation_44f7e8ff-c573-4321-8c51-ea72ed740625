#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
Main Window Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional
from PySide6.QtGui import QAction
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QStatusBar, QToolBar, QToolButton, QPushButton,
    QLabel, QLineEdit, QTextEdit, QListWidget,
    QTreeWidget, QTabWidget, QSplitter, QStackedWidget,
    QGroupBox, QScrollArea, QFrame, QMessageBox
)
from PySide6.QtCore import Qt, QTimer, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

from core.database_manager import DatabaseManager
from core.logger_system import get_logger_system
from core.auth_manager import AuthManager, Permission
from core.performance_optimizer import PerformanceOptimizer
from ui.patient_management_ui import PatientManagementWidget
from ui.treatment_ui import TreatmentWidget
from ui.report_ui import ReportWidget
from ui.settings_ui import SettingsWidget
from ui.login_dialog import LoginDialog
from ui.user_management_ui import UserManagementWidget
from ui.data_management_ui import DataManagementWidget
from utils.app_config import AppConfig


class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    window_closing = Signal()
    user_logged_in = Signal(str)  # 用户登录信号
    user_logged_out = Signal()    # 用户登出信号

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化属性
        self.db_manager: Optional[DatabaseManager] = None
        self.auth_manager: Optional[AuthManager] = None
        self.performance_optimizer: Optional[PerformanceOptimizer] = None
        self.logger = logging.getLogger(__name__)
        self.logger_system = get_logger_system()
        self.current_user = None
        self.current_user_role = None
        self.is_user_logged_in = False  # 登录状态标志

        # UI组件
        self.central_widget = None
        self.stacked_widget = None
        self.navigation_widget = None
        self.status_bar = None

        # 页面组件
        self.patient_management_widget = None
        self.treatment_widget = None
        self.report_widget = None
        self.settings_widget = None
        self.data_management_widget = None

        # 导航按钮
        self.nav_buttons = {}

        # 定时器
        self.status_update_timer = QTimer()
        self.auto_save_timer = QTimer()

        # 初始化界面
        self.init_ui()
        self.setup_connections()
        self.setup_timers()

        # 初始状态：隐藏敏感内容，确保医疗隐私安全
        self.hide_sensitive_content()

        self.logger.info("主窗口初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置窗口属性
            self.setWindowTitle(f"{AppConfig.APP_NAME} v{AppConfig.VERSION}")
            self.setMinimumSize(1200, 800)

            # 设置窗口图标
            self.set_window_icon()

            # 创建菜单栏
            self.create_menu_bar()

            # 创建中央部件
            self.create_central_widget()

            # 创建状态栏
            self.create_status_bar()

            # 应用样式
            self.apply_styles()

            self.logger.info("主窗口UI初始化完成")

        except Exception as e:
            self.logger.error(f"主窗口UI初始化失败: {e}")
            raise

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = AppConfig.PROJECT_ROOT / "resources" / "images" / "app_icon.png"
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")

    def create_menu_bar(self):
        """创建菜单栏"""
        try:
            menubar = self.menuBar()

            # 文件菜单
            file_menu = menubar.addMenu("文件(&F)")

            # 数据库备份
            backup_action = QAction("数据库备份", self)
            backup_action.triggered.connect(self.backup_database)
            file_menu.addAction(backup_action)

            # 数据库恢复
            restore_action = QAction("数据库恢复", self)
            restore_action.triggered.connect(self.restore_database)
            file_menu.addAction(restore_action)

            file_menu.addSeparator()

            # 退出
            exit_action = QAction("退出(&X)", self)
            exit_action.setShortcut("Ctrl+Q")
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)

            # 工具菜单
            tools_menu = menubar.addMenu("工具(&T)")

            # 系统设置
            settings_action = QAction("系统设置", self)
            settings_action.triggered.connect(self.show_settings)
            tools_menu.addAction(settings_action)

            # 日志查看
            log_action = QAction("日志查看", self)
            log_action.triggered.connect(self.show_logs)
            tools_menu.addAction(log_action)

            # 帮助菜单
            help_menu = menubar.addMenu("帮助(&H)")

            # 关于
            about_action = QAction("关于", self)
            about_action.triggered.connect(self.show_about)
            help_menu.addAction(about_action)

        except Exception as e:
            self.logger.error(f"创建菜单栏失败: {e}")

    def create_central_widget(self):
        """创建中央部件"""
        try:
            self.central_widget = QWidget()
            self.setCentralWidget(self.central_widget)

            # 主布局
            main_layout = QHBoxLayout(self.central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)

            # 创建分割器
            splitter = QSplitter(Qt.Horizontal)
            main_layout.addWidget(splitter)

            # 创建导航区域
            self.create_navigation_widget()
            splitter.addWidget(self.navigation_widget)

            # 创建内容区域
            self.create_content_widget()
            splitter.addWidget(self.stacked_widget)

            # 设置分割器比例
            splitter.setStretchFactor(0, 0)  # 导航区域固定宽度
            splitter.setStretchFactor(1, 1)  # 内容区域自适应
            splitter.setSizes([200, 1000])

        except Exception as e:
            self.logger.error(f"创建中央部件失败: {e}")
            raise

    def create_navigation_widget(self):
        """创建导航部件"""
        try:
            self.navigation_widget = QFrame()
            self.navigation_widget.setFixedWidth(200)
            self.navigation_widget.setFrameStyle(QFrame.StyledPanel)

            layout = QVBoxLayout(self.navigation_widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(5)

            # 标题
            title_label = QLabel(AppConfig.APP_NAME)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px;")
            layout.addWidget(title_label)

            # 导航按钮
            nav_buttons_config = [
                ("patient_management", "患者管理", "main_person.png"),
                ("treatment", "治疗系统", "main_config.png"),
                ("data_management", "数据管理", "main_data.png"),
                ("report", "报告分析", "main_data.png"),
                ("user_management", "用户管理", "main_person.png"),
                ("settings", "系统设置", "main_config.png"),
            ]

            for button_id, text, icon_name in nav_buttons_config:
                button = self.create_nav_button(button_id, text, icon_name)
                layout.addWidget(button)
                self.nav_buttons[button_id] = button

            # 添加弹性空间
            layout.addStretch()

            # 用户信息区域
            self.create_user_info_widget(layout)

        except Exception as e:
            self.logger.error(f"创建导航部件失败: {e}")
            raise

    def create_nav_button(self, button_id: str, text: str, icon_name: str) -> QToolButton:
        """创建导航按钮"""
        button = QToolButton()
        button.setText(text)
        button.setCheckable(True)
        button.setAutoExclusive(True)
        button.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        button.setMinimumHeight(60)
        button.setMaximumHeight(60)

        # 设置图标
        try:
            icon_path = AppConfig.PROJECT_ROOT / "resources" / "images" / icon_name
            if icon_path.exists():
                button.setIcon(QIcon(str(icon_path)))
                button.setIconSize(QSize(32, 32))
        except Exception:
            pass

        # 连接信号
        button.clicked.connect(lambda: self.switch_page(button_id))

        return button

    def create_user_info_widget(self, layout):
        """创建用户信息部件"""
        try:
            user_frame = QFrame()
            user_frame.setFrameStyle(QFrame.StyledPanel)
            user_layout = QVBoxLayout(user_frame)

            # 用户信息标签
            self.user_info_label = QLabel("未登录")
            self.user_info_label.setAlignment(Qt.AlignCenter)
            user_layout.addWidget(self.user_info_label)

            # 登录/登出按钮
            self.login_button = QPushButton("登录")
            self.login_button.clicked.connect(self.show_login_dialog)
            user_layout.addWidget(self.login_button)

            layout.addWidget(user_frame)

        except Exception as e:
            self.logger.error(f"创建用户信息部件失败: {e}")

    def create_content_widget(self):
        """创建内容部件（延迟加载优化）"""
        try:
            self.stacked_widget = QStackedWidget()

            # 延迟加载：只创建占位符，实际组件在需要时创建
            self.widget_cache = {}
            self.widget_classes = {
                'patient_management': PatientManagementWidget,
                'treatment': TreatmentWidget,
                'data_management': DataManagementWidget,
                'report': ReportWidget,
                'user_management': UserManagementWidget,
                'settings': SettingsWidget,
            }

            # 创建占位符页面
            for widget_name in self.widget_classes.keys():
                placeholder = QLabel(f"正在加载{widget_name}...")
                placeholder.setAlignment(Qt.AlignCenter)
                placeholder.setStyleSheet("font-size: 16px; color: #666;")
                self.stacked_widget.addWidget(placeholder)
                self.widget_cache[widget_name] = {'placeholder': placeholder, 'widget': None}

            # 预加载患者管理页面（最常用）
            self.load_widget('patient_management')
            self.switch_page('patient_management')

        except Exception as e:
            self.logger.error(f"创建内容部件失败: {e}")
            raise

    def load_widget(self, widget_name):
        """延迟加载指定组件"""
        try:
            if widget_name not in self.widget_cache:
                return None

            cache_entry = self.widget_cache[widget_name]

            # 如果已经加载，直接返回
            if cache_entry['widget'] is not None:
                return cache_entry['widget']

            # 创建组件
            widget_class = self.widget_classes[widget_name]
            widget = widget_class()

            # 设置数据库管理器和权限管理器
            if self.db_manager and hasattr(widget, 'set_database_manager'):
                widget.set_database_manager(self.db_manager)
            if self.auth_manager and hasattr(widget, 'set_auth_manager'):
                widget.set_auth_manager(self.auth_manager)

            # 替换占位符
            placeholder = cache_entry['placeholder']
            index = self.stacked_widget.indexOf(placeholder)
            self.stacked_widget.removeWidget(placeholder)
            self.stacked_widget.insertWidget(index, widget)

            # 更新缓存
            cache_entry['widget'] = widget
            placeholder.deleteLater()

            # 设置属性引用（保持向后兼容）
            setattr(self, f"{widget_name}_widget", widget)

            self.logger.info(f"组件 {widget_name} 延迟加载完成")
            return widget

        except Exception as e:
            self.logger.error(f"延迟加载组件 {widget_name} 失败: {e}")
            return None

    def create_status_bar(self):
        """创建状态栏"""
        try:
            self.status_bar = QStatusBar()
            self.setStatusBar(self.status_bar)

            # 状态信息
            self.status_label = QLabel("就绪")
            self.status_bar.addWidget(self.status_label)

            # 数据库状态
            self.db_status_label = QLabel("数据库: 未连接")
            self.status_bar.addPermanentWidget(self.db_status_label)

            # 设备状态
            self.device_status_label = QLabel("设备: 未连接")
            self.status_bar.addPermanentWidget(self.device_status_label)

            # 时间显示
            self.time_label = QLabel()
            self.status_bar.addPermanentWidget(self.time_label)

        except Exception as e:
            self.logger.error(f"创建状态栏失败: {e}")

    def apply_styles(self):
        """应用样式"""
        try:
            # 这里可以设置自定义样式
            pass
        except Exception as e:
            self.logger.error(f"应用样式失败: {e}")

    def setup_connections(self):
        """设置信号连接"""
        try:
            # 用户登录/登出信号
            self.user_logged_in.connect(self.on_user_logged_in)
            self.user_logged_out.connect(self.on_user_logged_out)

            # 患者管理界面信号
            if self.patient_management_widget:
                self.patient_management_widget.switch_to_treatment.connect(self.on_switch_to_treatment)

        except Exception as e:
            self.logger.error(f"设置信号连接失败: {e}")

    def setup_timers(self):
        """设置定时器"""
        try:
            # 状态更新定时器
            self.status_update_timer.timeout.connect(self.update_status)
            self.status_update_timer.start(1000)  # 每秒更新一次

            # 自动保存定时器
            auto_save_interval = AppConfig.UI_CONFIG.get('auto_save_interval', 300) * 1000
            self.auto_save_timer.timeout.connect(self.auto_save)
            self.auto_save_timer.start(auto_save_interval)

        except Exception as e:
            self.logger.error(f"设置定时器失败: {e}")

    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager

        # 传递给已加载的子组件
        for widget_name, cache_entry in self.widget_cache.items():
            widget = cache_entry.get('widget')
            if widget and hasattr(widget, 'set_database_manager'):
                widget.set_database_manager(db_manager)

        self.logger.info("数据库管理器设置完成")

    def set_auth_manager(self, auth_manager: AuthManager):
        """设置权限管理器"""
        self.auth_manager = auth_manager

        # 传递给已加载的子组件
        for widget_name, cache_entry in self.widget_cache.items():
            widget = cache_entry.get('widget')
            if widget:
                if hasattr(widget, 'set_auth_manager'):
                    widget.set_auth_manager(auth_manager)
                elif hasattr(widget, 'auth_manager'):
                    widget.auth_manager = auth_manager

                # 特殊处理患者管理界面
                if widget_name == 'patient_management' and hasattr(widget, 'update_current_operator'):
                    widget.update_current_operator()

        self.logger.info("权限管理器设置完成")

    def set_performance_optimizer(self, optimizer: PerformanceOptimizer):
        """设置性能优化器"""
        self.performance_optimizer = optimizer

        # 启动性能监控
        self.performance_optimizer.start()

        # 添加性能监控到状态栏
        self.setup_performance_monitoring()

        self.logger.info("性能优化器设置完成")

    def setup_performance_monitoring(self):
        """设置性能监控"""
        if not self.performance_optimizer:
            return

        # 创建性能监控定时器
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance_status)
        self.performance_timer.start(5000)  # 每5秒更新一次

    def update_performance_status(self):
        """更新性能状态"""
        if not self.performance_optimizer:
            return

        try:
            metrics = self.performance_optimizer.monitor.get_current_metrics()
            if metrics:
                status_text = f"CPU: {metrics.cpu_usage:.1f}% | 内存: {metrics.memory_usage:.1f}%"
                self.status_bar.showMessage(status_text)
        except Exception as e:
            self.logger.error(f"更新性能状态失败: {e}")

    def update_ui_permissions(self):
        """更新UI权限状态"""
        try:
            if not self.auth_manager:
                self.logger.warning("权限管理器未初始化，跳过权限更新")
                return

            # 更新导航按钮状态
            nav_permissions = {
                "patient_management": Permission.PATIENT_VIEW,
                "treatment": Permission.TREATMENT_OPERATE,
                "data_management": Permission.DATA_ANALYSIS,
                "report": Permission.DATA_ANALYSIS,
                "user_management": Permission.USER_MANAGE,
                "settings": Permission.SYSTEM_CONFIG,
            }

            for button_id, permission in nav_permissions.items():
                try:
                    if button_id in self.nav_buttons:
                        # 安全检查权限，确保返回布尔值
                        try:
                            has_permission = self.auth_manager.has_permission(permission)
                            # 确保返回值是布尔类型
                            if has_permission is None:
                                has_permission = False
                            has_permission = bool(has_permission)
                        except Exception as perm_e:
                            self.logger.error(f"检查权限 {permission.value} 失败: {perm_e}")
                            has_permission = False

                        self.nav_buttons[button_id].setEnabled(has_permission)
                        if not has_permission and self.nav_buttons[button_id].isChecked():
                            # 如果当前页面没有权限，切换到有权限的页面
                            self.switch_to_available_page()
                except Exception as button_e:
                    self.logger.error(f"更新按钮 {button_id} 权限失败: {button_e}")

            # 更新子组件的权限状态
            self.update_child_components_permissions()

        except Exception as e:
            self.logger.error(f"更新UI权限状态失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def update_child_components_permissions(self):
        """更新子组件的权限状态"""
        try:
            self.logger.debug("开始更新子组件权限状态...")

            # 更新已加载组件的权限
            for widget_name, cache_entry in self.widget_cache.items():
                widget = cache_entry.get('widget')
                if widget and hasattr(widget, 'update_ui_permissions'):
                    self.logger.debug(f"更新{widget_name}界面权限...")
                    widget.update_ui_permissions()

            self.logger.debug("子组件权限状态更新完成")

        except Exception as e:
            self.logger.error(f"更新子组件权限状态失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def switch_to_available_page(self):
        """切换到有权限的页面"""
        try:
            if not self.auth_manager:
                self.logger.warning("权限管理器未初始化，无法切换页面")
                return

            # 按优先级检查可用页面
            page_priorities = [
                ("patient_management", Permission.PATIENT_VIEW),
                ("treatment", Permission.TREATMENT_OPERATE),
                ("report", Permission.DATA_ANALYSIS),
                ("user_management", Permission.USER_MANAGE),
                ("settings", Permission.SYSTEM_CONFIG),
            ]

            for page_id, permission in page_priorities:
                try:
                    # 安全检查权限，确保返回布尔值
                    try:
                        has_permission = self.auth_manager.has_permission(permission)
                        # 确保返回值是布尔类型
                        if has_permission is None:
                            has_permission = False
                        has_permission = bool(has_permission)
                    except Exception as perm_e:
                        self.logger.error(f"检查权限 {permission.value} 失败: {perm_e}")
                        has_permission = False

                    if has_permission:
                        self.switch_page(page_id)
                        if page_id in self.nav_buttons:
                            self.nav_buttons[page_id].setChecked(True)
                        break
                except Exception as page_e:
                    self.logger.error(f"切换到页面 {page_id} 失败: {page_e}")

        except Exception as e:
            self.logger.error(f"切换到可用页面失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def switch_page(self, page_id: str):
        """切换页面（支持延迟加载）"""
        try:
            # 延迟加载页面组件
            widget = self.load_widget(page_id)

            if widget:
                self.stacked_widget.setCurrentWidget(widget)
                try:
                    self.logger_system.log_operation(
                        self.current_user or "unknown",
                        f"切换到{page_id}页面"
                    )
                except Exception as log_e:
                    self.logger.error(f"记录页面切换日志失败: {log_e}")
            else:
                self.logger.warning(f"页面 {page_id} 加载失败")

        except Exception as e:
            self.logger.error(f"切换页面失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def on_switch_to_treatment(self, patient_data: dict):
        """处理切换到治疗页面的信号"""
        try:
            # 检查是否有治疗权限
            if self.auth_manager:
                has_permission = self.auth_manager.has_permission(Permission.TREATMENT_OPERATE)
                if not has_permission:
                    QMessageBox.warning(self, "权限不足", "您没有治疗操作权限，无法进入治疗页面")
                    return

            # 切换到治疗页面
            self.switch_page("treatment")
            if "treatment" in self.nav_buttons:
                self.nav_buttons["treatment"].setChecked(True)

            # 设置患者信息到治疗界面
            treatment_widget = self.load_widget('treatment')
            if treatment_widget and hasattr(treatment_widget, 'set_patient_info'):
                treatment_widget.set_patient_info(patient_data)
                self.logger.info(f"已切换到治疗页面，患者: {patient_data.get('name', '')}")

        except Exception as e:
            self.logger.error(f"切换到治疗页面失败: {e}")
            QMessageBox.critical(self, "错误", f"切换到治疗页面失败: {e}")

    def update_status(self):
        """更新状态栏"""
        try:
            # 更新时间
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)

            # 更新数据库状态
            if self.db_manager:
                self.db_status_label.setText("数据库: 已连接")
            else:
                self.db_status_label.setText("数据库: 未连接")

        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")

    def auto_save(self):
        """自动保存"""
        try:
            # 这里可以实现自动保存逻辑
            pass
        except Exception as e:
            self.logger.error(f"自动保存失败: {e}")

    def show_login_dialog(self):
        """显示登录对话框"""
        if not self.auth_manager:
            QMessageBox.warning(self, "错误", "权限管理器未初始化")
            return

        login_dialog = LoginDialog(self.auth_manager, self)
        login_dialog.login_successful.connect(self.on_login_successful)
        login_dialog.exec()

    def on_login_successful(self, user_info: dict):
        """处理登录成功"""
        try:
            self.current_user = user_info['name']
            self.current_user_role = user_info['role']
            self.is_user_logged_in = True

            # 安全获取角色显示名称
            role_display = ""
            try:
                if hasattr(user_info['role'], 'value'):
                    role_display = user_info['role'].value
                else:
                    role_display = str(user_info['role'])
            except Exception as e:
                self.logger.error(f"获取角色显示名称失败: {e}")
                role_display = "未知角色"

            # 更新UI
            self.user_info_label.setText(f"用户: {user_info['name']} ({role_display})")
            self.login_button.setText("登出")

            # 安全断开连接
            try:
                self.login_button.clicked.disconnect()
            except:
                pass  # 如果没有连接则忽略

            self.login_button.clicked.connect(self.logout)

            # 显示敏感内容
            self.show_sensitive_content()

            # 更新权限相关的UI状态
            self.update_ui_permissions()

            # 延迟发送登录信号，避免竞态条件
            QTimer.singleShot(500, lambda: self.emit_user_logged_in_signal(user_info['name']))

            try:
                self.logger_system.log_operation(user_info['name'], "用户登录成功")
            except Exception as log_e:
                self.logger.error(f"记录登录日志失败: {log_e}")

            # 上传设备开机状态
            self.upload_equipment_startup_status()

        except Exception as e:
            self.logger.error(f"处理登录成功失败: {e}")
            # 如果处理失败，至少要更新基本状态
            try:
                self.current_user = user_info.get('name', 'unknown')
                self.is_user_logged_in = True
                self.user_info_label.setText(f"用户: {self.current_user}")
                self.login_button.setText("登出")
                try:
                    self.login_button.clicked.disconnect()
                except:
                    pass
                self.login_button.clicked.connect(self.logout)
            except Exception as inner_e:
                self.logger.error(f"基本状态更新也失败: {inner_e}")

    def emit_user_logged_in_signal(self, username: str):
        """安全发送用户登录信号"""
        try:
            # 检查窗口是否仍然有效
            if not self.isVisible() or not self.isEnabled():
                self.logger.warning("窗口已关闭或禁用，取消发送用户登录信号")
                return

            if username and isinstance(username, str):
                self.logger.debug(f"发送用户登录信号: {username}")
                self.user_logged_in.emit(username)
            else:
                self.logger.error("用户名无效，无法发送用户登录信号")
        except Exception as e:
            self.logger.error(f"发送用户登录信号失败: {e}")

    def on_user_logged_in(self, username: str):
        """用户登录成功处理"""
        self.current_user = username
        self.user_info_label.setText(f"用户: {username}")
        self.login_button.setText("登出")
        self.login_button.clicked.disconnect()
        self.login_button.clicked.connect(self.logout)

        self.logger_system.log_operation(username, "用户登录")

    def on_user_logged_out(self):
        """用户登出处理"""
        if self.current_user:
            self.logger_system.log_operation(self.current_user, "用户登出")

        self.current_user = None
        self.current_user_role = None
        self.is_user_logged_in = False
        self.user_info_label.setText("未登录")
        self.login_button.setText("登录")
        self.login_button.clicked.disconnect()
        self.login_button.clicked.connect(self.show_login_dialog)

        # 隐藏敏感内容
        self.hide_sensitive_content()

    def hide_sensitive_content(self):
        """隐藏敏感内容 - 符合医疗器械软件隐私保护要求"""
        try:
            # 禁用所有导航按钮
            for button in self.nav_buttons.values():
                button.setEnabled(False)

            # 创建登录提示页面
            if not hasattr(self, 'login_prompt_widget'):
                self.create_login_prompt_widget()

            # 显示登录提示页面
            self.stacked_widget.setCurrentWidget(self.login_prompt_widget)

            # 禁用菜单栏中的敏感功能
            if hasattr(self, 'menuBar'):
                menubar = self.menuBar()
                for action in menubar.actions():
                    menu = action.menu()
                    if menu:
                        for menu_action in menu.actions():
                            if menu_action.text() in ["数据库备份", "数据库恢复", "系统设置", "日志查看"]:
                                menu_action.setEnabled(False)

            self.logger.info("敏感内容已隐藏，符合医疗隐私保护要求")

        except Exception as e:
            self.logger.error(f"隐藏敏感内容失败: {e}")

    def show_sensitive_content(self):
        """显示敏感内容 - 用户登录后可访问"""
        try:
            # 启用导航按钮（根据权限）
            self.update_ui_permissions()

            # 切换到第一个有权限的页面
            self.switch_to_available_page()

            # 启用菜单栏功能（根据权限）
            try:
                if hasattr(self, 'menuBar'):
                    menubar = self.menuBar()
                    if menubar:
                        for action in menubar.actions():
                            try:
                                menu = action.menu()
                                if menu:
                                    for menu_action in menu.actions():
                                        try:
                                            # 根据权限启用菜单项
                                            if self.auth_manager:
                                                try:
                                                    if menu_action.text() in ["数据库备份", "数据库恢复"]:
                                                        has_perm = self.auth_manager.has_permission(Permission.SYSTEM_CONFIG)
                                                        menu_action.setEnabled(bool(has_perm) if has_perm is not None else False)
                                                    elif menu_action.text() == "系统设置":
                                                        has_perm = self.auth_manager.has_permission(Permission.SYSTEM_CONFIG)
                                                        menu_action.setEnabled(bool(has_perm) if has_perm is not None else False)
                                                    elif menu_action.text() == "日志查看":
                                                        has_perm = self.auth_manager.has_permission(Permission.LOG_VIEW)
                                                        menu_action.setEnabled(bool(has_perm) if has_perm is not None else False)
                                                    else:
                                                        menu_action.setEnabled(True)
                                                except Exception as perm_e:
                                                    self.logger.error(f"检查菜单权限失败: {perm_e}")
                                                    menu_action.setEnabled(False)
                                        except Exception as menu_e:
                                            self.logger.error(f"处理菜单项失败: {menu_e}")
                            except Exception as action_e:
                                self.logger.error(f"处理菜单动作失败: {action_e}")
            except Exception as menu_e:
                self.logger.error(f"处理菜单栏失败: {menu_e}")

            self.logger.info("敏感内容已显示，用户已通过身份验证")

        except Exception as e:
            self.logger.error(f"显示敏感内容失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def create_login_prompt_widget(self):
        """创建登录提示页面"""
        try:
            self.login_prompt_widget = QWidget()
            layout = QVBoxLayout(self.login_prompt_widget)
            layout.setAlignment(Qt.AlignCenter)

            # 系统图标
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setFixedSize(128, 128)
            icon_label.setStyleSheet("""
                QLabel {
                    background-color: #3498db;
                    border-radius: 64px;
                    color: white;
                    font-size: 48px;
                    font-weight: bold;
                }
            """)
            icon_label.setText("NK")
            layout.addWidget(icon_label)

            # 系统标题
            title_label = QLabel(f"{AppConfig.APP_NAME}")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px 0;
                }
            """)
            layout.addWidget(title_label)

            # 版本信息
            version_label = QLabel(f"版本 {AppConfig.VERSION}")
            version_label.setAlignment(Qt.AlignCenter)
            version_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #7f8c8d;
                    margin-bottom: 30px;
                }
            """)
            layout.addWidget(version_label)

            # 隐私保护提示
            privacy_label = QLabel("🔒 为保护患者隐私，请先登录系统")
            privacy_label.setAlignment(Qt.AlignCenter)
            privacy_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    color: #e74c3c;
                    font-weight: bold;
                    background-color: #fdf2f2;
                    border: 2px solid #e74c3c;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 20px 50px;
                }
            """)
            layout.addWidget(privacy_label)

            # 医疗器械软件合规说明
            compliance_label = QLabel("本系统符合医疗器械软件相关规定\n未经授权不得访问患者信息")
            compliance_label.setAlignment(Qt.AlignCenter)
            compliance_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #95a5a6;
                    margin: 20px 0;
                    line-height: 1.5;
                }
            """)
            layout.addWidget(compliance_label)

            # 登录按钮
            login_btn = QPushButton("点击登录")
            login_btn.setFixedSize(200, 50)
            login_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            login_btn.clicked.connect(self.show_login_dialog)

            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(login_btn)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # 添加到堆栈部件
            self.stacked_widget.addWidget(self.login_prompt_widget)

        except Exception as e:
            self.logger.error(f"创建登录提示页面失败: {e}")

    def logout(self):
        """登出"""
        if self.auth_manager:
            self.auth_manager.logout()
        self.user_logged_out.emit()

        # 上传设备关机状态
        self.upload_equipment_shutdown_status()

    def upload_equipment_startup_status(self):
        """上传设备开机状态"""
        try:
            if not self.db_manager:
                self.logger.warning("数据库管理器未初始化，跳过设备开机状态上传")
                return

            # 获取医院信息
            hospital_info = self.db_manager.get_hospital_info()
            if not hospital_info:
                self.logger.warning("无法获取医院信息，跳过设备开机状态上传")
                return

            # 创建数据上传器
            from core.http_client import PatientDataUploader
            uploader = PatientDataUploader()

            # 上传开机状态
            self.logger.info("登录成功，上传设备开机状态")
            upload_result = uploader.update_equipment_status(hospital_info, "1")

            if upload_result.success:
                self.logger.info("设备开机状态上传成功")
            else:
                self.logger.warning(f"设备开机状态上传失败: {upload_result.message}")

        except Exception as e:
            self.logger.error(f"上传设备开机状态时发生异常: {e}")

    def upload_equipment_shutdown_status(self):
        """上传设备关机状态"""
        try:
            if not self.db_manager:
                self.logger.warning("数据库管理器未初始化，跳过设备关机状态上传")
                return

            # 获取医院信息
            hospital_info = self.db_manager.get_hospital_info()
            if not hospital_info:
                self.logger.warning("无法获取医院信息，跳过设备关机状态上传")
                return

            # 创建数据上传器
            from core.http_client import PatientDataUploader
            uploader = PatientDataUploader()

            # 上传关机状态
            self.logger.info("用户登出，上传设备关机状态")
            upload_result = uploader.update_equipment_status(hospital_info, "0")

            if upload_result.success:
                self.logger.info("设备关机状态上传成功")
            else:
                self.logger.warning(f"设备关机状态上传失败: {upload_result.message}")

        except Exception as e:
            self.logger.error(f"上传设备关机状态时发生异常: {e}")

    def backup_database(self):
        """备份数据库"""
        if self.db_manager:
            if self.db_manager.backup_database():
                QMessageBox.information(self, "成功", "数据库备份成功！")
            else:
                QMessageBox.warning(self, "失败", "数据库备份失败！")

    def restore_database(self):
        """恢复数据库"""
        # 这里应该实现文件选择对话框
        QMessageBox.information(self, "提示", "数据库恢复功能待实现")

    def show_settings(self):
        """显示设置"""
        self.switch_page("settings")

    def show_logs(self):
        """显示日志"""
        # 这里应该实现日志查看对话框
        QMessageBox.information(self, "提示", "日志查看功能待实现")

    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        {AppConfig.APP_NAME}
        版本: {AppConfig.VERSION}
        构建日期: {AppConfig.BUILD_DATE}

        医疗级脑机接口系统
        用于神经康复治疗

        © 2024 {AppConfig.COMPANY_NAME}
        """
        QMessageBox.about(self, "关于", about_text)

    def cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            if self.status_update_timer.isActive():
                self.status_update_timer.stop()
            if self.auto_save_timer.isActive():
                self.auto_save_timer.stop()

            # 清理已加载的子组件
            for widget_name, cache_entry in self.widget_cache.items():
                widget = cache_entry.get('widget')
                if widget and hasattr(widget, 'cleanup'):
                    try:
                        widget.cleanup()
                    except Exception as e:
                        self.logger.error(f"清理组件 {widget_name} 失败: {e}")

            self.logger.info("主窗口资源清理完成")

        except Exception as e:
            self.logger.error(f"主窗口资源清理失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 发送关闭信号
            self.window_closing.emit()

            # 检查是否有电刺激设备正在工作
            stimulation_warning = ""
            treatment_widget = self.widget_cache.get('treatment', {}).get('widget')
            if (treatment_widget and
                hasattr(treatment_widget, 'stimulation_device') and
                treatment_widget.stimulation_device and
                hasattr(treatment_widget, 'stimulation_connected') and
                treatment_widget.stimulation_connected):
                stimulation_warning = "\n\n⚠️ 警告：检测到电刺激设备正在连接中！\n退出程序将自动停止所有电刺激并断开设备连接。"

            # 确认关闭
            reply = QMessageBox.question(
                self,
                "确认退出",
                f"确定要退出系统吗？{stimulation_warning}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 上传设备关机状态（在清理之前）
                if self.is_user_logged_in:
                    self.upload_equipment_shutdown_status()

                # 显示清理进度
                if stimulation_warning:
                    # 如果有电刺激设备，显示安全退出提示
                    progress_msg = QMessageBox(self)
                    progress_msg.setWindowTitle("安全退出中")
                    progress_msg.setText("正在安全断开医疗设备连接，请稍候...")
                    progress_msg.setStandardButtons(QMessageBox.NoButton)
                    progress_msg.show()
                    progress_msg.repaint()

                    # 执行清理
                    self.cleanup()
                    progress_msg.close()
                else:
                    self.cleanup()

                event.accept()
            else:
                event.ignore()

        except Exception as e:
            self.logger.error(f"窗口关闭处理失败: {e}")
            # 即使出错也要尝试清理
            try:
                self.cleanup()
            except:
                pass
            event.accept()  # 强制关闭
