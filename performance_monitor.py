#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时性能监控器
"""

import time
import psutil
import threading
from collections import deque

class RealTimeMonitor:
    """实时性能监控"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = deque(maxlen=100)
        self.monitor_thread = None
        
    def start(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """监控循环"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                metrics = {
                    'timestamp': time.time(),
                    'cpu_percent': process.cpu_percent(),
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'threads': process.num_threads()
                }
                self.metrics.append(metrics)
                time.sleep(1)
            except Exception:
                break
    
    def get_current_metrics(self):
        """获取当前指标"""
        if self.metrics:
            return self.metrics[-1]
        return None
    
    def get_average_metrics(self, seconds=60):
        """获取平均指标"""
        if not self.metrics:
            return None
            
        cutoff_time = time.time() - seconds
        recent_metrics = [m for m in self.metrics if m['timestamp'] > cutoff_time]
        
        if not recent_metrics:
            return None
            
        avg_metrics = {
            'cpu_percent': sum(m['cpu_percent'] for m in recent_metrics) / len(recent_metrics),
            'memory_mb': sum(m['memory_mb'] for m in recent_metrics) / len(recent_metrics),
            'threads': sum(m['threads'] for m in recent_metrics) / len(recent_metrics)
        }
        
        return avg_metrics

# 全局监控器
performance_monitor = RealTimeMonitor()
