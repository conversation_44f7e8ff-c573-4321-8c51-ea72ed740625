#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证原始数据完整性
Verify Raw Data Completeness

检查保存的原始脑电数据是否包含完整的8通道125Hz数据

作者: AI Assistant
版本: 1.0.0
"""

import sys
import h5py
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database_manager import DatabaseManager
from core.eeg_data_loader import EEGDataLoader

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('verify_raw_data.log')
        ]
    )

def analyze_hdf5_data_structure(file_path: Path):
    """分析HDF5文件的数据结构"""
    print(f"\n🔍 分析HDF5文件: {file_path.name}")
    print("=" * 60)
    
    try:
        with h5py.File(file_path, 'r') as f:
            # 1. 文件基本信息
            print(f"📁 文件大小: {file_path.stat().st_size / 1024:.2f} KB")
            print(f"📊 根组: {list(f.keys())}")
            
            # 2. 会话元数据
            if 'session_metadata' in f:
                session_meta = f['session_metadata']
                print(f"\n📋 会话元数据:")
                for key in session_meta.attrs.keys():
                    value = session_meta.attrs[key]
                    if isinstance(value, bytes):
                        value = value.decode('utf-8')
                    print(f"  {key}: {value}")
            
            # 3. 试验数据分析
            if 'trials' in f:
                trials_group = f['trials']
                trial_names = sorted(trials_group.keys())
                print(f"\n🧠 试验数据分析:")
                print(f"试验总数: {len(trial_names)}")
                
                # 分析每个试验的数据
                total_samples = 0
                total_duration = 0
                channel_counts = []
                sampling_rates = []
                data_shapes = []
                
                for i, trial_name in enumerate(trial_names[:5]):  # 只分析前5个试验
                    trial_group = trials_group[trial_name]
                    
                    if 'eeg_data' in trial_group:
                        eeg_data = trial_group['eeg_data']
                        data_shape = eeg_data.shape
                        data_shapes.append(data_shape)
                        
                        # 获取试验元数据
                        duration = trial_group.attrs.get('duration_seconds', 0)
                        total_duration += duration
                        
                        # 计算采样率
                        if duration > 0:
                            estimated_rate = data_shape[1] / duration
                            sampling_rates.append(estimated_rate)
                        
                        total_samples += data_shape[1]
                        channel_counts.append(data_shape[0])
                        
                        print(f"\n  试验 {trial_name}:")
                        print(f"    数据形状: {data_shape} (通道数, 样本数)")
                        print(f"    时长: {duration:.3f} 秒")
                        if duration > 0:
                            print(f"    估算采样率: {estimated_rate:.1f} Hz")
                        print(f"    数据类型: {eeg_data.dtype}")
                        print(f"    数据范围: [{np.min(eeg_data):.1f}, {np.max(eeg_data):.1f}]")
                        
                        # 检查数据质量
                        if data_shape[0] == 8:
                            print(f"    ✅ 通道数正确 (8通道)")
                        else:
                            print(f"    ❌ 通道数异常 ({data_shape[0]}通道)")
                        
                        if duration > 0 and abs(estimated_rate - 125) < 5:
                            print(f"    ✅ 采样率正确 (~125Hz)")
                        elif duration > 0:
                            print(f"    ⚠️ 采样率异常 ({estimated_rate:.1f}Hz)")
                
                # 4. 总体统计
                print(f"\n📊 总体数据统计:")
                if channel_counts:
                    unique_channels = set(channel_counts)
                    print(f"通道数分布: {unique_channels}")
                    if len(unique_channels) == 1 and 8 in unique_channels:
                        print("✅ 所有试验都是8通道数据")
                    else:
                        print("❌ 通道数不一致或不正确")
                
                if sampling_rates:
                    avg_rate = np.mean(sampling_rates)
                    rate_std = np.std(sampling_rates)
                    print(f"平均采样率: {avg_rate:.2f} ± {rate_std:.2f} Hz")
                    if abs(avg_rate - 125) < 2:
                        print("✅ 采样率符合125Hz标准")
                    else:
                        print(f"⚠️ 采样率偏离125Hz标准")
                
                if data_shapes:
                    print(f"数据形状分布: {set(data_shapes)}")
                
                print(f"总样本数: {total_samples}")
                print(f"总时长: {total_duration:.2f} 秒")
                if total_duration > 0:
                    overall_rate = total_samples / total_duration
                    print(f"整体采样率: {overall_rate:.2f} Hz")
                
                return {
                    'trial_count': len(trial_names),
                    'channel_counts': channel_counts,
                    'sampling_rates': sampling_rates,
                    'data_shapes': data_shapes,
                    'total_samples': total_samples,
                    'total_duration': total_duration
                }
            
    except Exception as e:
        print(f"❌ 分析HDF5文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_data_packet_structure():
    """验证数据包结构配置"""
    print(f"\n🔧 验证数据包结构配置:")
    print("=" * 40)
    
    from utils.app_config import AppConfig
    
    eeg_config = AppConfig.EEG_CONFIG
    print(f"配置的采样率: {eeg_config['sample_rate']} Hz")
    print(f"配置的通道数: {eeg_config['channels']}")
    print(f"每包数据组数: {eeg_config['data_groups_per_packet']}")
    print(f"每组字节数: {eeg_config['bytes_per_group']}")
    print(f"通道名称: {eeg_config['channel_names']}")
    
    # 计算理论数据量
    groups_per_second = eeg_config['sample_rate'] / eeg_config['data_groups_per_packet']
    packets_per_second = groups_per_second
    
    print(f"\n📊 理论计算:")
    print(f"每秒数据组数: {groups_per_second:.1f}")
    print(f"每秒数据包数: {packets_per_second:.1f}")
    print(f"每秒样本数: {eeg_config['sample_rate']}")
    
    # 验证配置一致性
    if eeg_config['channels'] == 8:
        print("✅ 通道数配置正确 (8通道)")
    else:
        print(f"❌ 通道数配置异常 ({eeg_config['channels']}通道)")
    
    if eeg_config['sample_rate'] == 125.0:
        print("✅ 采样率配置正确 (125Hz)")
    else:
        print(f"⚠️ 采样率配置异常 ({eeg_config['sample_rate']}Hz)")
    
    if len(eeg_config['channel_names']) == 8:
        print("✅ 通道名称配置正确 (8个通道名)")
    else:
        print(f"❌ 通道名称配置异常 ({len(eeg_config['channel_names'])}个名称)")

def verify_patient_raw_data(patient_id: int):
    """验证患者的原始数据"""
    print(f"\n🔍 验证患者 {patient_id} 的原始数据完整性")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager()
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 查询患者的数据文件
        sql = """
            SELECT DISTINCT file_path 
            FROM eeg_raw_data 
            WHERE patient_id = ?
        """
        results = db_manager.execute_query(sql, [patient_id])
        
        if not results:
            print(f"❌ 未找到患者 {patient_id} 的数据记录")
            return False
        
        print(f"📁 找到 {len(results)} 个数据文件:")
        
        all_analysis = []
        for result in results:
            file_path = Path(result['file_path'])
            print(f"\n📄 文件: {file_path.name}")
            
            if file_path.exists():
                print(f"✅ 文件存在")
                analysis = analyze_hdf5_data_structure(file_path)
                if analysis:
                    all_analysis.append(analysis)
            else:
                print(f"❌ 文件不存在: {file_path}")
        
        # 综合分析
        if all_analysis:
            print(f"\n🎯 综合数据质量评估:")
            print("=" * 40)
            
            total_trials = sum(a['trial_count'] for a in all_analysis)
            total_samples = sum(a['total_samples'] for a in all_analysis)
            total_duration = sum(a['total_duration'] for a in all_analysis)
            
            all_channels = []
            all_rates = []
            for a in all_analysis:
                all_channels.extend(a['channel_counts'])
                all_rates.extend(a['sampling_rates'])
            
            print(f"总试验数: {total_trials}")
            print(f"总样本数: {total_samples}")
            print(f"总时长: {total_duration:.2f} 秒")
            
            if all_channels:
                unique_channels = set(all_channels)
                print(f"通道数分布: {unique_channels}")
                if len(unique_channels) == 1 and 8 in unique_channels:
                    print("✅ 所有数据都是8通道")
                else:
                    print("❌ 通道数不一致")
            
            if all_rates:
                avg_rate = np.mean(all_rates)
                rate_std = np.std(all_rates)
                print(f"平均采样率: {avg_rate:.2f} ± {rate_std:.2f} Hz")
                
                if abs(avg_rate - 125) < 2:
                    print("✅ 采样率符合125Hz标准")
                else:
                    print(f"⚠️ 采样率偏离125Hz标准")
            
            if total_duration > 0:
                overall_rate = total_samples / total_duration
                print(f"整体采样率: {overall_rate:.2f} Hz")
            
            # 最终评估
            print(f"\n🏆 最终评估:")
            issues = []
            
            if not all_channels or 8 not in set(all_channels):
                issues.append("通道数不正确")
            
            if not all_rates or abs(np.mean(all_rates) - 125) > 5:
                issues.append("采样率不正确")
            
            if total_trials < 5:
                issues.append("数据量不足")
            
            if not issues:
                print("🎉 数据完整性验证通过！")
                print("✅ 包含完整的8通道125Hz脑电数据")
                return True
            else:
                print("⚠️ 发现以下问题:")
                for issue in issues:
                    print(f"  - {issue}")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 原始数据完整性验证")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 1. 验证配置
    verify_data_packet_structure()
    
    # 2. 验证具体患者数据
    test_patient_id = **********
    success = verify_patient_raw_data(test_patient_id)
    
    # 总结
    print("\n" + "=" * 50)
    print("验证总结")
    print("=" * 50)
    
    if success:
        print("✅ 原始数据完整性验证通过！")
        print("🎉 保存的数据包含完整的8通道125Hz脑电信号")
        return 0
    else:
        print("❌ 原始数据完整性验证失败")
        print("⚠️ 请检查数据采集和存储配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
